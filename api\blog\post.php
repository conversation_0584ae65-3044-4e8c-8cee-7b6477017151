<?php
// UTF-8 encoding için
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

// Cache kontrol header'ları - API her zaman güncel olsun
header("Cache-Control: no-cache, no-store, must-revalidate, max-age=0");
header("Pragma: no-cache");
header("Expires: Thu, 01 Jan 1970 00:00:00 GMT");

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    // Get post ID or slug from URL
    $identifier = $_GET['id'] ?? $_GET['slug'] ?? null;
    
    if (!$identifier) {
        throw new Exception('Post ID or slug is required');
    }
    
    // Determine if identifier is numeric (ID) or string (slug)
    $isId = is_numeric($identifier);
    $whereField = $isId ? 'bp.id' : 'bp.slug';
    
    // Get single post with category info
    $sql = "SELECT 
                bp.id,
                bp.title,
                bp.slug,
                bp.excerpt,
                bp.content,
                bp.featured_image,
                bp.author,
                bp.meta_title,
                bp.meta_description,
                bp.meta_keywords,
                bp.reading_time,
                bp.view_count,
                bp.featured,
                bp.status,
                bp.published_at,
                bp.created_at,
                bp.updated_at,
                bc.id as category_id,
                bc.name as category_name,
                bc.slug as category_slug,
                bc.description as category_description
            FROM blog_posts bp 
            LEFT JOIN blog_categories bc ON bp.category_id = bc.id 
            WHERE {$whereField} = ? AND bp.status = 'published'";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$identifier]);
    $post = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$post) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Post not found'
        ]);
        exit;
    }
    
    // Format post data
    $post['id'] = (int)$post['id'];
    $post['category_id'] = (int)$post['category_id'];
    $post['featured'] = (bool)$post['featured'];
    $post['view_count'] = (int)($post['view_count'] ?? 0);
    $post['reading_time'] = (int)($post['reading_time'] ?? 5);
    
    // Format dates
    if ($post['published_at']) {
        $post['published_at_formatted'] = date('d.m.Y H:i', strtotime($post['published_at']));
    }
    if ($post['created_at']) {
        $post['created_at_formatted'] = date('d.m.Y H:i', strtotime($post['created_at']));
    }
    if ($post['updated_at']) {
        $post['updated_at_formatted'] = date('d.m.Y H:i', strtotime($post['updated_at']));
    }
    
    // Generate full URL for featured image
    if ($post['featured_image'] && strpos($post['featured_image'], 'http') !== 0) {
        $post['featured_image'] = '/' . ltrim($post['featured_image'], '/');
    }
    
    // Clean HTML from content for plain text version
    if (!empty($post['content'])) {
        $post['plain_content'] = strip_tags($post['content']);
    }
    
    // Fetch tags for this post
    $tagSql = "SELECT bt.id, bt.name, bt.slug 
               FROM blog_tags bt 
               INNER JOIN blog_post_tags bpt ON bt.id = bpt.tag_id 
               WHERE bpt.post_id = ?";
    $tagStmt = $db->prepare($tagSql);
    $tagStmt->execute([$post['id']]);
    $post['tags'] = $tagStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Update view count
    $updateViewSql = "UPDATE blog_posts SET view_count = view_count + 1 WHERE id = ?";
    $updateStmt = $db->prepare($updateViewSql);
    $updateStmt->execute([$post['id']]);
    $post['view_count']++; // Reflect the increment in response
    
    // Get related posts (same category, excluding current post)
    $relatedSql = "SELECT 
                       id, title, slug, excerpt, featured_image, 
                       author, published_at, reading_time, view_count
                   FROM blog_posts 
                   WHERE category_id = ? AND id != ? AND status = 'published'
                   ORDER BY published_at DESC 
                   LIMIT 3";
    $relatedStmt = $db->prepare($relatedSql);
    $relatedStmt->execute([$post['category_id'], $post['id']]);
    $relatedPosts = $relatedStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format related posts
    foreach ($relatedPosts as &$relatedPost) {
        $relatedPost['id'] = (int)$relatedPost['id'];
        $relatedPost['view_count'] = (int)($relatedPost['view_count'] ?? 0);
        $relatedPost['reading_time'] = (int)($relatedPost['reading_time'] ?? 5);
        
        if ($relatedPost['published_at']) {
            $relatedPost['published_at_formatted'] = date('d.m.Y', strtotime($relatedPost['published_at']));
        }
        
        if ($relatedPost['featured_image'] && strpos($relatedPost['featured_image'], 'http') !== 0) {
            $relatedPost['featured_image'] = '/' . ltrim($relatedPost['featured_image'], '/');
        }
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'post' => $post,
        'related_posts' => $relatedPosts
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_NUMERIC_CHECK);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
