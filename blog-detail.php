<?php
// Get blog post slug from URL
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('Location: /blog.php');
    exit();
}

// You can add database connection here to fetch the actual post
// For now, we'll create a placeholder
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="/">

    <!-- Dynamic meta tags will be loaded by JavaScript -->
    <title>Blog Yazısı Yükleniyor... | Tansu Şahal Salamura</title>
    <meta name="description" content="Blog yazısı yükleniyor...">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">

    <link rel="stylesheet" href="styles.css?v=2.0.1">
    
    <style>
        /* Site Theme Compatible Blog Design */
        :root {
            --blog-primary: #2c5530;
            --blog-secondary: #4a7c59;
            --blog-accent: #7fb069;
            --blog-light: #f8fffe;
            --blog-white: #ffffff;
            --blog-dark: #1a1a1a;
            --blog-gray: #6c757d;
            --blog-border: #e9ecef;
            --blog-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --blog-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        body {
            background: var(--blog-light) !important;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            color: var(--blog-dark) !important;
        }

        /* Site Theme Blog Header */
        .blog-header {
            background: linear-gradient(135deg, var(--blog-primary) 0%, var(--blog-secondary) 100%);
            color: white;
            padding: 3rem 0 2rem;
            margin-top: 80px;
            position: relative;
            overflow: hidden;
        }

        .blog-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .blog-header .container {
            max-width: 1000px;
            position: relative;
            z-index: 2;
        }

        /* Minimal Breadcrumb */
        .blog-breadcrumb {
            background: transparent;
            padding: 0;
            margin-bottom: 2rem;
            font-size: 0.875rem;
        }

        .blog-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
            content: "→";
            color: var(--blog-secondary);
            margin: 0 0.75rem;
            font-weight: 400;
        }

        .blog-breadcrumb a {
            color: rgba(255,255,255,0.9) !important;
            text-decoration: none;
            transition: color 0.2s ease;
            font-weight: 400;
        }

        .blog-breadcrumb a:hover {
            color: white !important;
            text-shadow: 0 0 10px rgba(255,255,255,0.5);
        }

        .blog-breadcrumb .active {
            color: white !important;
            font-weight: 600;
        }

        /* Site Theme Blog Title */
        .blog-title {
            font-family: 'Merriweather', serif;
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.1;
            color: white !important;
            margin-bottom: 1.5rem;
            letter-spacing: -0.025em;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* Clean Meta Section */
        .blog-meta {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 3rem;
            padding: 0;
            border: none;
        }

        /* COMPLETE META STYLING - Header (Green Background) */
        .blog-header .blog-meta,
        .blog-header .blog-meta *,
        .blog-header .blog-meta .meta-item,
        .blog-header .blog-meta .meta-item *,
        .blog-header .blog-meta span,
        .blog-header .blog-meta #blogViews,
        .blog-header .blog-meta #blogDate,
        .blog-header .blog-meta #blogReadingTime,
        .blog-header .blog-meta #blogAuthor {
            color: white !important;
        }

        .blog-header .blog-meta .meta-item {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 0.95rem;
            font-weight: 500;
        }

        .blog-header .blog-meta .meta-item i {
            color: rgba(255,255,255,0.9) !important;
            font-size: 1rem;
        }

        /* COMPLETE META STYLING - Content (White Background) */
        .container .blog-meta,
        .container .blog-meta .meta-item,
        .container .blog-meta .meta-item *,
        .container .blog-meta span,
        .container .blog-meta #blogViews,
        .container .blog-meta #blogDate,
        .container .blog-meta #blogReadingTime,
        .container .blog-meta #blogAuthor {
            color: var(--blog-gray) !important;
        }

        .container .blog-meta .meta-item i {
            color: var(--blog-primary) !important;
        }

        .blog-meta .author-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        /* COMPLETE AUTHOR STYLING - Header (Green Background) */
        .blog-header .blog-meta .author-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white !important;
            font-weight: 600;
            font-size: 1.2rem;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
        }

        .blog-header .blog-meta .author-name,
        .blog-header .blog-meta .author-date,
        .blog-header .blog-meta .author-date *,
        .blog-header .blog-meta .author-info *,
        .blog-header .blog-meta span {
            color: white !important;
        }

        /* COMPLETE AUTHOR STYLING - Content (White Background) */
        .container .blog-meta .author-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--blog-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white !important;
            font-weight: 600;
            font-size: 1.2rem;
            border: 2px solid var(--blog-border);
        }

        .container .blog-meta .author-name {
            color: var(--blog-dark) !important;
            font-weight: 600;
            font-size: 1rem;
        }

        .container .blog-meta .author-date,
        .container .blog-meta .author-date *,
        .container .blog-meta .author-info span {
            color: var(--blog-gray) !important;
        }

        /* Featured Image Styling */
        .featured-image-container {
            margin: 3rem 0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--blog-shadow-lg);
        }

        .featured-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            display: block;
        }

        .featured-image-overlay {
            display: none;
        }

        /* Blog Content Container - Wider for Desktop */
        .container.my-5 {
            background: var(--blog-white);
            border-radius: 20px;
            box-shadow: var(--blog-shadow-lg);
            margin: 2rem auto !important;
            padding: 3rem;
            max-width: 1200px;
        }

        .container.my-5 .col-lg-8 {
            max-width: 900px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .container.my-5 {
                max-width: 1000px;
                padding: 2.5rem;
            }

            .container.my-5 .col-lg-8 {
                max-width: 800px;
            }
        }

        @media (max-width: 992px) {
            .blog-header .container {
                max-width: 900px;
            }

            .container.my-5 {
                max-width: 900px;
                padding: 2rem;
            }

            .container.my-5 .col-lg-8 {
                max-width: 700px;
            }
        }

        @media (max-width: 768px) {
            .blog-header {
                padding: 2rem 0 1.5rem;
                margin-top: 70px;
            }

            .blog-title {
                font-size: 2.5rem;
            }

            .blog-meta {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }

            .blog-meta .author-info {
                align-self: stretch;
            }

            .featured-image {
                height: 250px;
            }

            .container.my-5 {
                margin: 1rem auto !important;
                padding: 1.5rem;
                border-radius: 15px;
                max-width: 95%;
            }
        }
        
        /* Modern Blog Content Styling */
        .blog-content {
            background: var(--blog-white);
            border-radius: 12px;
            padding: 3rem;
            margin-bottom: 3rem;
            border: 1px solid var(--blog-border);
            line-height: 1.7;
            font-size: 1.125rem;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--blog-primary);
            box-shadow: var(--blog-shadow);
            position: relative;
            max-width: none;
        }

        .blog-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-green), var(--secondary-green));
            border-radius: 20px 20px 0 0;
        }

        .blog-content h1, .blog-content h2, .blog-content h3, .blog-content h4 {
            color: var(--blog-primary);
            margin-top: 2.5rem;
            margin-bottom: 1.25rem;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            font-weight: 700;
            line-height: 1.2;
            letter-spacing: -0.02em;
        }

        .blog-content h1 {
            font-size: 2.75rem;
            margin-top: 0;
            margin-bottom: 1.5rem;
            font-weight: 800;
            line-height: 1.1;
        }

        .blog-content h2 {
            font-size: 2.25rem;
            position: relative;
            padding-left: 1.25rem;
            margin-top: 3rem;
            font-weight: 700;
        }

        .blog-content h2::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 70%;
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            border-radius: 3px;
        }

        .blog-content h3 {
            font-size: 1.75rem;
            font-weight: 600;
            margin-top: 2.25rem;
        }

        .blog-content h4 {
            font-size: 1.375rem;
            font-weight: 600;
            margin-top: 2rem;
        }

        .blog-content h5 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-top: 1.75rem;
            color: #34495e;
        }

        .blog-content p {
            margin-bottom: 1.75rem;
            text-align: justify;
            color: #2c3e50;
            font-weight: 400;
            line-height: 1.8;
            font-size: 1.2rem;
        }

        .blog-content p:first-of-type {
            font-size: 1.25rem;
            font-weight: 400;
            color: var(--blog-secondary);
            line-height: 1.7;
            margin-bottom: 2rem;
        }

        /* Professional paragraph styling */
        .blog-content p + p {
            text-indent: 0;
        }

        .blog-content blockquote {
            border-left: 4px solid var(--primary-green);
            padding: 1.5rem 2rem;
            margin: 2rem 0;
            background: rgba(76, 175, 80, 0.05);
            border-radius: 0 10px 10px 0;
            font-style: italic;
            font-size: 1.1rem;
            color: #34495e;
        }

        .blog-content blockquote p {
            margin-bottom: 0;
        }

        .blog-content ul, .blog-content ol {
            margin: 1.5rem 0;
            padding-left: 2rem;
        }

        .blog-content li {
            margin-bottom: 0.75rem;
            line-height: 1.7;
        }

        .blog-content strong {
            font-weight: 600;
            color: #2c3e50;
        }

        .blog-content em {
            font-style: italic;
            color: #34495e;
        }

        .blog-content img {
            max-width: 100%;
            height: auto;
            border-radius: 15px;
            margin: 2rem 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .blog-content img:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .blog-meta {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 2px solid var(--light-green);
        }

        /* Professional Featured Image with Error Handling */
        .featured-image-container {
            position: relative;
            width: 100%;
            height: 450px;
            margin-bottom: 3rem;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 50px rgba(0,0,0,0.15);
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .featured-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease, opacity 0.3s ease;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .featured-image:hover {
            transform: scale(1.02);
        }

        .featured-image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            padding: 2rem;
            color: white;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .featured-image-container:hover .featured-image-overlay {
            opacity: 1;
        }

        .image-error-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #6c757d;
            font-size: 1.1rem;
            text-align: center;
            flex-direction: column;
        }

        .image-error-placeholder i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Responsive Design Improvements */
        @media (max-width: 992px) {
            .blog-content {
                padding: 2rem;
                margin-bottom: 2rem;
            }

            .featured-image-container {
                height: 350px;
                margin-bottom: 2rem;
            }

            .blog-content h1 {
                font-size: 2rem;
            }

            .blog-content h2 {
                font-size: 1.75rem;
            }
        }

        @media (max-width: 768px) {
            .blog-content {
                padding: 1.5rem;
                font-size: 1rem;
                border-radius: 15px;
            }

            .featured-image-container {
                height: 280px;
                border-radius: 15px;
            }

            .blog-content h1 {
                font-size: 1.75rem;
            }

            .blog-content h2 {
                font-size: 1.5rem;
                padding-left: 0.75rem;
            }

            .blog-content h2::before {
                width: 3px;
            }

            .tag-list {
                padding: 1rem;
                gap: 0.5rem;
            }
        }

        @media (max-width: 576px) {
            .blog-content {
                padding: 1rem;
                margin-bottom: 1.5rem;
                font-size: 1rem;
            }

            .featured-image-container {
                height: 220px;
                margin-bottom: 1.5rem;
                border-radius: 12px;
            }

            .blog-content h1 {
                font-size: 1.75rem;
                line-height: 1.2;
            }

            .blog-content h2 {
                font-size: 1.375rem;
                padding-left: 0.5rem;
            }

            .blog-content h2::before {
                width: 3px;
            }

            .blog-content h3 {
                font-size: 1.25rem;
            }

            .blog-content p {
                text-align: left;
                margin-bottom: 1.25rem;
                font-size: 1rem;
            }

            .article-meta {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .article-meta .meta-item {
                justify-content: center;
            }

            .related-posts {
                padding: 1.5rem;
                margin: 2rem 0;
            }

            .related-posts h3 {
                font-size: 1.5rem;
            }

            .related-post-card .card-body {
                padding: 1rem;
            }

            .reading-progress {
                height: 3px;
            }
        }

        /* Extra small devices optimization */
        @media (max-width: 400px) {
            .blog-content {
                padding: 0.75rem;
                border-radius: 12px;
            }

            .blog-content h1 {
                font-size: 1.5rem;
            }

            .blog-content h2 {
                font-size: 1.25rem;
            }

            .featured-image-container {
                height: 180px;
            }

            .tag-list {
                padding: 0.75rem;
            }
        }
        
        /* Professional Tags Styling */
        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin: 2rem 0;
            padding: 1.5rem;
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            border: 1px solid rgba(76, 175, 80, 0.2);
        }

        .tag-item {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }

        .tag-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
            color: white;
        }

        /* Share buttons styles moved to styles.css */

        /* Reading Progress Indicator */
        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-green), var(--secondary-green));
            z-index: 9999;
            transition: width 0.3s ease;
        }

        /* Article Meta Information */
        .article-meta {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            border: 1px solid rgba(76, 175, 80, 0.2);
            font-family: 'Inter', sans-serif;
        }

        .article-meta .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6c757d;
            font-size: 0.95rem;
        }

        .article-meta .meta-item i {
            color: var(--primary-green);
        }

        .article-meta .author-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
        }

        /* Table of Contents */
        .table-of-contents {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(76, 175, 80, 0.2);
            font-family: 'Inter', sans-serif;
        }

        .table-of-contents h6 {
            color: var(--primary-green);
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .table-of-contents ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .table-of-contents li {
            margin-bottom: 0.5rem;
        }

        .table-of-contents a {
            color: #6c757d;
            text-decoration: none;
            transition: color 0.3s ease;
            font-size: 0.95rem;
        }

        .table-of-contents a:hover {
            color: var(--primary-green);
        }

        /* Smooth Animations and Transitions */
        * {
            scroll-behavior: smooth;
        }

        .blog-content, .article-meta, .tag-list, .share-buttons, .related-posts {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .featured-image-container {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* Loading States */
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        /* Focus and Accessibility Improvements */
        .share-btn:focus, .tag-item:focus, .related-post-card:focus {
            outline: 2px solid var(--primary-green);
            outline-offset: 2px;
        }

        /* Print Styles */
        @media print {
            .reading-progress, .share-buttons, .related-posts, nav, footer {
                display: none !important;
            }

            .blog-content {
                box-shadow: none;
                border: none;
                background: white;
            }
        }
        
        .share-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .share-btn:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .share-facebook { background: #1877f2; }
        .share-twitter { background: #1da1f2; }
        .share-whatsapp { background: #25d366; }
        .share-linkedin { background: #0077b5; }
        
        /* Professional Related Posts Section */
        .related-posts {
            background: rgba(255,255,255,0.98);
            border-radius: 25px;
            padding: 3rem;
            margin: 4rem 0;
            border: 1px solid rgba(76, 175, 80, 0.2);
            box-shadow: 0 15px 50px rgba(0,0,0,0.08);
            position: relative;
        }

        .related-posts::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-green), var(--secondary-green));
            border-radius: 25px 25px 0 0;
        }

        .related-posts h3 {
            color: var(--primary-green);
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            margin-bottom: 2rem;
            font-size: 1.75rem;
        }

        .related-post-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(76, 175, 80, 0.1);
            height: 100%;
        }

        .related-post-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .related-post-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .related-post-card:hover img {
            transform: scale(1.05);
        }

        .related-post-card .card-body {
            padding: 1.5rem;
        }

        .related-post-card .card-title {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            color: #2c3e50;
            font-size: 1.1rem;
            line-height: 1.4;
            margin-bottom: 0.75rem;
        }

        .related-post-card .card-text {
            color: #6c757d;
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .related-post-card .btn {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            border: none;
            color: white;
            font-weight: 500;
            padding: 0.5rem 1.25rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .related-post-card .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
            color: white;
        }
        
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 60vh;
            margin-top: 80px;
            background: #f8f9fa;
        }

        .loading-spinner .text-center {
            padding: 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Reading Progress Indicator -->
    <div class="reading-progress" id="readingProgress"></div>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm sticky-top main-navbar">
        <div class="container">
            <a class="navbar-brand fw-bold fs-4 d-flex align-items-center" href="/">
                <img src="images/logo.jpg" alt="Tansu Şahal Salamura Logo" style="height: 50px; margin-right: 10px;">
                <span class="brand-text">
                    <span class="brand-line-1">Tansu Şahal</span>
                    <span class="brand-line-2">Salamura</span>
                </span>
            </a>
            
            <button class="navbar-toggler" type="button" onclick="toggleIndependentMobileMenu()" aria-label="Menüyü aç/kapat">
                <span class="navbar-toggler-icon"></span>
                <span class="menu-text">Menü</span>
            </button>
            
            <div class="collapse navbar-collapse" id="mainNav">
                <!-- Desktop Menu -->
                <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
                    <li class="nav-item"><a class="nav-link" href="/">Ana Sayfa</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#products">Tüm Ürünler</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#about-us-section">Hakkımızda</a></li>
                    <li class="nav-item"><a class="nav-link active" href="blog.php">Blog</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#contact-section">İletişim</a></li>
                </ul>
                <div class="d-flex align-items-center navbar-user-info">
                    <span id="userGreeting" class="navbar-user-greeting me-2" style="display:none;"></span>
                    <div class="auth-links-group">
                        <a href="login.php" id="loginLink" class="text-dark text-decoration-none auth-link">Giriş Yap</a>
                        <a href="register.php" id="registerLink" class="text-dark text-decoration-none auth-link">Kayıt Ol</a>
                        <a href="#" id="logoutLink" class="text-dark text-decoration-none auth-link" style="display:none;">Çıkış Yap</a>
                        <a href="admin.php" id="adminLink" class="text-dark text-decoration-none auth-link" style="display:none;">Admin</a>
                        <a href="favorites.php" class="btn btn-outline-danger position-relative rounded-pill px-2 py-1" aria-label="Favoriler">
                            <i class="fas fa-heart me-1"></i>
                            <span class="d-none d-md-inline">Favoriler</span>
                            <span id="favorites-item-count" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger border border-light">0</span>
                        </a>
                    </div>
                </div>

                <!-- Mobile Menu -->
                <div class="mobile-menu-container">
                    <div class="mobile-menu-header">
                        <div class="brand-text">
                            <span class="brand-line-1">Tansu Şahal</span>
                            <span class="brand-line-2">Salamura</span>
                        </div>
                    </div>

                    <ul class="mobile-nav-links">
                        <li class="nav-item"><a class="nav-link" href="/" onclick="closeMobileMenu()">🏠 Ana Sayfa</a></li>
                        <li class="nav-item"><a class="nav-link" href="/#products" onclick="closeMobileMenu()">🛍️ Tüm Ürünler</a></li>
                        <li class="nav-item"><a class="nav-link" href="/#about-us-section" onclick="closeMobileMenu()">🌿 Hakkımızda</a></li>
                        <li class="nav-item"><a class="nav-link active" href="blog.php" onclick="closeMobileMenu()">📝 Blog</a></li>
                        <li class="nav-item"><a class="nav-link" href="/#contact-section" onclick="closeMobileMenu()">📞 İletişim</a></li>
                    </ul>

                    <div class="mobile-user-section">
                        <span id="mobileUserGreeting" class="mobile-user-greeting" style="display:none;"></span>

                        <div class="mobile-auth-buttons">
                            <a href="login.php" id="mobileLoginLink" class="mobile-auth-button secondary" onclick="closeMobileMenu()">
                                <i class="fas fa-sign-in-alt"></i> Giriş Yap
                            </a>
                            <a href="register.php" id="mobileRegisterLink" class="mobile-auth-button primary" onclick="closeMobileMenu()">
                                <i class="fas fa-user-plus"></i> Kayıt Ol
                            </a>
                            <a href="#" id="mobileLogoutLink" class="mobile-auth-button secondary" style="display:none;" onclick="handleMobileLogout()">
                                <i class="fas fa-sign-out-alt"></i> Çıkış Yap
                            </a>
                            <a href="admin.php" id="mobileAdminLink" class="mobile-auth-button primary" style="display:none;" onclick="closeMobileMenu()">
                                <i class="fas fa-cog"></i> Admin
                            </a>
                        </div>

                        <a href="favorites.php" class="mobile-favorites-button" aria-label="Favoriler" onclick="closeMobileMenu()">
                            <i class="fas fa-heart"></i> Favoriler
                            <span id="mobileFavoritesCount" class="badge bg-white text-danger">0</span>
                        </a>
                    </div>

                    <div class="mobile-menu-footer">
                        <p>🌱 Doğallığın ve Lezzetin Adresi</p>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-spinner">
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-3x text-primary mb-3"></i>
            <p class="text-muted">Blog yazısı yükleniyor...</p>
        </div>
    </div>

    <!-- Blog Content -->
    <div id="blogDetailContent" style="display: none;">
        <!-- Blog Header -->
        <section class="blog-header">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb blog-breadcrumb">
                                <li class="breadcrumb-item"><a href="/">Ana Sayfa</a></li>
                                <li class="breadcrumb-item"><a href="/blog.php">Blog</a></li>
                                <li class="breadcrumb-item active" aria-current="page" id="breadcrumbTitle">Yükleniyor...</li>
                            </ol>
                        </nav>
                        
                        <h1 id="blogTitle" class="blog-title">Blog Yazısı Yükleniyor...</h1>
                        
                        <div class="blog-meta">
                            <div class="author-info">
                                <div class="author-avatar" id="authorAvatar">T</div>
                                <div>
                                    <div class="author-name" id="blogAuthor">Yükleniyor...</div>
                                    <div class="author-date">
                                        <span id="blogDate">Yükleniyor...</span>
                                        <span style="margin: 0 0.5rem; color: var(--blog-secondary);">·</span>
                                        <span id="blogReadingTime">0</span> dk okuma
                                    </div>
                                </div>
                            </div>

                            <div class="meta-item" style="margin-left: auto;">
                                <i class="fas fa-eye"></i>
                                <span id="blogViews">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Content -->
        <div class="container my-5">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- Featured Image Container -->
                    <div id="featuredImageContainer" class="featured-image-container" style="display: block;">
                        <img id="blogFeaturedImage" src="/images/logo.jpg" alt="Varsayılan Görsel" class="featured-image"
                             onerror="this.parentElement.innerHTML='<div class=&quot;image-error-placeholder&quot;><i class=&quot;fas fa-image&quot;></i><div>Görsel yüklenemedi</div></div>'">
                        <div class="featured-image-overlay">
                            <h5 id="imageCaption">Yükleniyor...</h5>
                        </div>
                    </div>



                    <!-- Blog Content -->
                    <div class="blog-content" id="blogContent">
                        <p>İçerik yükleniyor...</p>
                    </div>
                    
                    <!-- Tags -->
                    <div class="tag-list" id="blogTags" style="border: 2px dashed #ccc; min-height: 50px; background: #f9f9f9;">
                        <p style="color: #666; margin: 10px;">Tags yükleniyor...</p>
                    </div>
                    
                    <!-- Share Buttons -->
                    <div class="share-buttons">
                        <h6><i class="fas fa-share-alt me-2"></i>Paylaş</h6>
                        <a href="#" class="share-btn facebook" onclick="shareOnFacebook()" title="Facebook'ta Paylaş">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="share-btn twitter" onclick="shareOnTwitter()" title="Twitter'da Paylaş">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="share-btn whatsapp" onclick="shareOnWhatsApp()" title="WhatsApp'ta Paylaş">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                        <a href="#" class="share-btn linkedin" onclick="shareOnLinkedIn()" title="LinkedIn'de Paylaş">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="share-btn copy" onclick="copyToClipboard()" title="Linki Kopyala">
                            <i class="fas fa-link"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Posts -->
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="related-posts">
                        <h3 class="text-center mb-4">
                            <i class="fas fa-newspaper me-2"></i>
                            İlgili Yazılar
                        </h3>
                        <div class="row" id="relatedPosts">
                            <!-- Related posts will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Content -->
    <div id="errorContent" style="display: none;">
        <div class="container my-5">
            <div class="row justify-content-center">
                <div class="col-lg-6 text-center">
                    <i class="fas fa-exclamation-triangle fa-4x text-warning mb-4"></i>
                    <h2>Blog Yazısı Bulunamadı</h2>
                    <p class="text-muted mb-4">Aradığınız blog yazısı bulunamadı veya kaldırılmış olabilir.</p>
                    <a href="/blog.php" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Blog'a Dön
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center p-4 mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>🌿 Tansu Şahal Salamura</h5>
                    <p>Doğallığın ve lezzetin adresi</p>
                </div>
                <div class="col-md-6">
                    <h6>İletişim</h6>
                    <p>
                        📞 +90 (536) 035 92 20<br>
                        📞 +90 536 705 58 69<br>
                        📧 <EMAIL>
                    </p>
                </div>
            </div>
            <hr>
            <p>&copy; 2025 Tansu Şahal Salamura. Tüm hakları saklıdır.</p>
            <p class="small text-white-50 mt-2">
                <a href="/gizlilik-politikasi.php" class="text-white-50">Gizlilik Politikası</a> |
                <a href="/kullanim-sartlari.php" class="text-white-50">Kullanım Şartları</a> |
                <a href="/sitemap.xml" class="text-white-50">Site Haritası</a>
            </p>
            <p class="small text-white-50 mt-2">
                <i class="fas fa-code me-1"></i>
                Web Tasarımı: <a href="mailto:<EMAIL>" class="text-white-50"><EMAIL></a>
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="header-updater.js"></script>
    <script src="mobile-menu.js"></script> <!-- Universal mobile menu system -->

    <script>
    function closeMobileMenu() {
        const navbarCollapse = document.getElementById('mainNav');
        const navbarToggler = document.querySelector('.navbar-toggler');

        if (navbarCollapse && navbarCollapse.classList.contains('show')) {
            navbarToggler.click();
        }
    }

    function toggleMobileDropdown(event) {
        event.preventDefault();
        const dropdown = event.target.closest('.mobile-dropdown');
        const dropdownMenu = dropdown.querySelector('.mobile-dropdown-menu');
        const chevron = dropdown.querySelector('.fa-chevron-down, .fa-chevron-up');

        // Toggle dropdown
        dropdownMenu.classList.toggle('show');

        // Toggle chevron
        if (chevron) {
            chevron.classList.toggle('fa-chevron-down');
            chevron.classList.toggle('fa-chevron-up');
        }
    }
    </script>
    <script>
        console.log('=== BLOG DETAIL SCRIPT STARTED ===');

        // Get slug from URL - support both pretty URLs and query params
        let slug = null;

        console.log('Blog detail page loaded');
        console.log('Current pathname:', window.location.pathname);
        console.log('Current search:', window.location.search);

        // JavaScript loaded successfully

        // First check for query parameter (preferred - means server-side rewrite worked)
        const urlParams = new URLSearchParams(window.location.search);
        slug = urlParams.get('slug');

        if (slug) {
            // Query parameter detected
        } else if (window.location.pathname.startsWith('/blog/')) {
            // Fallback: extract from pretty URL (means server-side rewrite didn't work)
            slug = window.location.pathname.replace('/blog/', '').replace(/\/$/, '');
        }

        if (!slug) {
            // Try to get from PHP if available
            const phpSlug = '<?php echo htmlspecialchars($slug ?? "", ENT_QUOTES); ?>';
            if (phpSlug && phpSlug !== '') {
                slug = phpSlug;
            }
        }
        
        let currentPost = null;
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM loaded, slug:', slug);
            console.log('Current URL:', window.location.href);

            // Initialize reading progress indicator
            initReadingProgress();

            // Check if required elements exist
            const loadingScreen = document.getElementById('loadingScreen');
            const blogContent = document.getElementById('blogDetailContent');
            console.log('Loading screen element:', loadingScreen);
            console.log('Blog content element:', blogContent);

            // Ensure loading screen is visible initially
            if (loadingScreen) {
                loadingScreen.style.display = 'flex';
            }
            if (blogContent) {
                blogContent.style.display = 'none';
            }

            if (slug && slug.trim() !== '') {
                console.log('Loading blog post with slug:', slug);
                // Slug found, proceeding
                loadBlogPost(slug);
            } else {
                console.log('No slug found, showing error');
                // No slug found
                setTimeout(() => showError(), 100);
            }
        });

        // Image URL validation and correction
        function validateImageUrl(url) {
            if (!url) return null;

            // If it's already a full URL, return as is
            if (url.startsWith('http://') || url.startsWith('https://')) {
                return url;
            }

            // If it starts with /, return as is
            if (url.startsWith('/')) {
                return url;
            }

            // Otherwise, add leading slash
            return '/' + url;
        }

        // Reading Progress Indicator
        function initReadingProgress() {
            const progressBar = document.getElementById('readingProgress');

            window.addEventListener('scroll', () => {
                const article = document.getElementById('blogContent');
                if (!article) return;

                const articleTop = article.offsetTop;
                const articleHeight = article.offsetHeight;
                const windowHeight = window.innerHeight;
                const scrollTop = window.pageYOffset;

                const articleStart = articleTop - windowHeight / 3;
                const articleEnd = articleTop + articleHeight - windowHeight / 3;
                const totalProgress = articleEnd - articleStart;
                const currentProgress = scrollTop - articleStart;

                const progressPercentage = Math.max(0, Math.min(100, (currentProgress / totalProgress) * 100));

                if (progressBar) {
                    progressBar.style.width = progressPercentage + '%';
                }
            });
        }
        
        async function loadBlogPost(slug) {
            console.log('=== Starting loadBlogPost ===');
            console.log('Slug:', slug);

            try {
                // Show loading screen
                const loadingScreen = document.getElementById('loadingScreen');
                const blogContent = document.getElementById('blogDetailContent');

                console.log('Elements found:', {
                    loadingScreen: !!loadingScreen,
                    blogContent: !!blogContent
                });

                if (loadingScreen) loadingScreen.style.display = 'flex';
                if (blogContent) blogContent.style.display = 'none';

                console.log('Fetching from API...');
                const apiUrl = `/api/blog/post.php?slug=${slug}`;
                console.log('API URL:', apiUrl);

                // API call in progress - no UI update needed

                let response, data;

                try {
                    response = await fetch(apiUrl);
                    console.log('Response status:', response.status);
                    console.log('Response ok:', response.ok);

                    // Response received - continue processing

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    data = await response.json();

                } catch (fetchError) {
                    console.warn('🚨 API fetch failed, using mock data:', fetchError);
                    console.log('🚨 API URL was:', `/api/blog/post.php?slug=${slug}`);

                    // Use mock data if API fails
                    data = {
                        success: true,
                        source: 'FALLBACK_DATA',
                        post: {
                            id: 1,
                            title: 'Doğallığın Kavanozdaki Hali: Salamura Üzüm ve Ev Ürünleriyle Sofralarımıza Yolculuk',
                            slug: slug, // Use the requested slug
                            excerpt: 'Geleneksel Türk mutfağının vazgeçilmez lezzetlerinden salamura üzüm, doğallığın ve sağlığın bir araya geldiği eşsiz bir tat deneyimi sunar.',
                            content: `<h2>Salamura Üzümün Hikayesi</h2>
                            <p>Salamura üzüm, Anadolu topraklarının bereketli bağlarından sofralarımıza uzanan bir lezzet yolculuğudur. Bu geleneksel ürün, sadece bir besin değil, aynı zamanda kültürümüzün ve tarihimizin bir parçasıdır.</p>

                            <h3>Doğal Fermentasyon Süreci</h3>
                            <p>Salamura üzüm, doğal fermentasyon süreciyle hazırlanır. Bu süreç, üzümün doğal şekerlerinin probiyotik bakteriler tarafından dönüştürülmesiyle gerçekleşir.</p>

                            <h3>Sağlık Faydaları</h3>
                            <ul>
                                <li>Probiyotik açısından zengin</li>
                                <li>Bağışıklık sistemini güçlendirir</li>
                                <li>Sindirim sistemine faydalı</li>
                                <li>Doğal antioksidanlar içerir</li>
                            </ul>`,
                            author: 'Tansu Şahal',
                            category_name: 'Salamura Üretimi',
                            category_slug: 'salamura-uretimi',
                            published_at: '2025-01-20',
                            reading_time: 8,
                            view_count: 156,
                            featured_image: '/uploads/blog/blog_1753733912_6887db1861b95.jpeg',
                            tags: []
                        },
                        related_posts: []
                    };
                }

                console.log('🔥 API response data:', data);
                console.log('🔥 API success:', data.success);
                console.log('🔥 Post found:', data.post ? 'Yes' : 'No');
                console.log('🔥 Data source:', data.source || 'API');
                if (data.post) {
                    console.log('🔥 Post featured_image:', data.post.featured_image);
                    console.log('🔥 Post title:', data.post.title);
                    console.log('🔥 Post slug:', data.post.slug);
                }

                if (!data.success) {
                    throw new Error(data.message || 'API returned error');
                }

                if (!data.post) {
                    throw new Error('No post found in API response');
                }

                console.log('Post slug:', data.post.slug);
                console.log('Requested slug:', slug);

                // Use the post directly from API response
                const post = data.post;
                console.log('Found post:', post);

                currentPost = post;
                console.log('Displaying post...');
                console.log('Post data:', {
                    title: post.title,
                    author: post.author,
                    published_at: post.published_at,
                    reading_time: post.reading_time,
                    view_count: post.view_count,
                    category_name: post.category_name
                });

                // Post found, calling displayBlogPost
                displayBlogPost(post);

                // Load related posts if available
                if (data.related_posts && data.related_posts.length > 0) {
                    displayRelatedPosts(data.related_posts);
                } else {
                    // Try to load related posts via API
                    try {
                        loadRelatedPosts(post.category_slug, post.id);
                    } catch (e) {
                        console.warn('Related posts loading failed:', e);
                    }
                }
            } catch (error) {
                console.error('=== Blog post loading error ===');
                console.error('Error:', error);
                console.error('Stack:', error.stack);

                // Show detailed error to user
                if (blogContent) {
                    blogContent.innerHTML = `
                        <div class="container my-5">
                            <div class="row justify-content-center">
                                <div class="col-lg-8">
                                    <div class="alert alert-danger">
                                        <h4>Hata Oluştu</h4>
                                        <p><strong>Hata:</strong> ${error.message}</p>
                                        <p><strong>Slug:</strong> ${slug}</p>
                                        <hr>
                                        <p>Lütfen sayfayı yenileyin veya <a href="/blog.php">blog sayfasına</a> geri dönün.</p>
                                        <button onclick="location.reload()" class="btn btn-primary">Sayfayı Yenile</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    blogContent.style.display = 'block';
                }
                if (loadingScreen) {
                    loadingScreen.style.display = 'none';
                }
            }
        }
        
        function displayBlogPost(post) {
            console.log('=== DISPLAY BLOG POST CALLED ===');
            console.log('Post data:', post);

            // Function called successfully

            // Check if meta area exists
            const metaArea = document.querySelector('.blog-meta');
            if (metaArea) {
                console.log('Meta area found');
            } else {
                console.error('Meta area not found');
            }

            try {
                // Update page title and meta
                document.title = `${post.title} | Tansu Şahal Salamura Blog`;
                updateMetaTags(post);

                // Update breadcrumb
                const breadcrumbTitle = document.getElementById('breadcrumbTitle');
                if (breadcrumbTitle) {
                    breadcrumbTitle.innerHTML = `<i class="fas fa-file-alt me-1"></i>${post.title}`;
                }

                // Update header content
                const blogTitle = document.getElementById('blogTitle');
                if (blogTitle) blogTitle.textContent = post.title;

                // Update meta information with proper contrast
                console.log('Updating meta information...');

                // Ensure the meta section has proper background and contrast
                const metaSection = document.querySelector('.blog-meta');
                if (metaSection) {
                    // Ensure the parent header section is visible and has green background
                    // Modern blog header styling is handled by CSS
                    console.log('Using modern blog header design');
                    console.log('Meta section styling applied');
                }

                // Update author
                const authorElement = document.getElementById('blogAuthor');
                const authorAvatar = document.getElementById('authorAvatar');
                if (authorElement) {
                    const authorName = post.author || 'Tansu Şahal';
                    authorElement.textContent = authorName;

                    // Update avatar with first letter of author name
                    if (authorAvatar) {
                        authorAvatar.textContent = authorName.charAt(0).toUpperCase();
                    }

                    console.log('Author updated:', authorName);
                }

                // Update date
                const dateElement = document.getElementById('blogDate');
                if (dateElement) {
                    const formattedDate = formatDate(post.published_at);
                    dateElement.textContent = formattedDate;
                    // Color is handled by CSS
                    console.log('Date updated:', formattedDate);
                }

                // Update reading time
                const readingTimeElement = document.getElementById('blogReadingTime');
                if (readingTimeElement) {
                    readingTimeElement.textContent = post.reading_time || 5;
                    // Color is handled by CSS
                    console.log('Reading time updated:', post.reading_time);
                }

                // Update views
                const viewsElement = document.getElementById('blogViews');
                if (viewsElement) {
                    viewsElement.textContent = post.view_count || 0;
                    // Color is handled by CSS - no manual styling needed
                    console.log('Views updated:', post.view_count);
                }

                console.log('Meta information update completed');

                // Meta information is now updated above in one go

                // Update category if available
                const blogCategory = document.getElementById('blogCategory');
                const blogCategoryContainer = document.getElementById('blogCategoryContainer');
                if (blogCategory && post.category_name) {
                    blogCategory.textContent = post.category_name;
                    if (blogCategoryContainer) {
                        blogCategoryContainer.style.display = 'flex';
                    }
                }

                // Update featured image with error handling
                const featuredImageContainer = document.getElementById('featuredImageContainer');
                const featuredImg = document.getElementById('blogFeaturedImage');
                const imageCaption = document.getElementById('imageCaption');

                // Validate and correct image URL
                const validatedImageUrl = validateImageUrl(post.featured_image);

                console.log('🖼️ Featured image data:', {
                    original: post.featured_image,
                    validated: validatedImageUrl,
                    hasContainer: !!featuredImageContainer,
                    hasImg: !!featuredImg,
                    postTitle: post.title,
                    apiWorking: !!post.id
                });

                if (validatedImageUrl && featuredImg && featuredImageContainer) {
                    console.log('🖼️ Setting up featured image:', validatedImageUrl);

                    // Görsel container'ı göster
                    featuredImageContainer.style.display = 'block';

                    // Yeni görsel oluştur (cache sorunlarını önlemek için)
                    const newImg = new Image();

                    newImg.onload = function() {
                        console.log('✅ Featured image loaded successfully:', validatedImageUrl);

                        // Başarılı yüklendiyse, mevcut img'yi güncelle
                        featuredImg.src = validatedImageUrl;
                        featuredImg.alt = post.title;
                        featuredImg.style.opacity = '1';

                        // Caption'ı güncelle
                        if (imageCaption) {
                            imageCaption.textContent = post.title;
                        }
                    };

                    newImg.onerror = function() {
                        console.warn('❌ Featured image failed to load:', validatedImageUrl);
                        console.log('🔄 Keeping default image (logo)');

                        // Hata durumunda varsayılan görsel kalır
                        if (imageCaption) {
                            imageCaption.textContent = 'Varsayılan Görsel';
                        }
                    };

                    // Test yüklemesi başlat
                    newImg.src = validatedImageUrl;

                    // Set caption
                    if (imageCaption) {
                        imageCaption.textContent = post.title;
                    }
                } else {
                    console.log('No featured image or missing elements, showing placeholder');
                    if (featuredImageContainer) {
                        featuredImageContainer.style.display = 'block';
                        featuredImageContainer.innerHTML = `
                            <div class="image-error-placeholder">
                                <i class="fas fa-newspaper"></i>
                                <div>Bu yazı için görsel bulunmuyor</div>
                                <small class="text-muted mt-2">Varsayılan görsel yükleniyor...</small>
                            </div>
                        `;

                        // Try to load default image
                        setTimeout(() => {
                            featuredImageContainer.innerHTML = `
                                <img src="/images/logo.jpg" alt="Varsayılan Görsel" class="featured-image" style="opacity: 0.7;">
                                <div class="featured-image-overlay">
                                    <h5>Varsayılan Görsel</h5>
                                </div>
                            `;
                        }, 1000);
                    }
                }

                // Meta information is now handled in the header section above

                // Update content with proper formatting
                const content = formatBlogContent(post.content || post.excerpt || 'İçerik bulunamadı.');
                const blogContent = document.getElementById('blogContent');
                if (blogContent) {
                    blogContent.innerHTML = content;
                }

                // Update tags from blog post data
                const tagsContainer = document.getElementById('blogTags');
                console.log('=== TAG RENDERING DEBUG ===');
                console.log('tagsContainer found:', !!tagsContainer);
                console.log('post.tags exists:', !!post.tags);
                console.log('Post tags data:', post.tags);
                console.log('Post tags type:', typeof post.tags);
                console.log('Post tags length:', Array.isArray(post.tags) ? post.tags.length : 'Not an array');

                if (tagsContainer && post.tags) {

                    let tags = [];

                    if (Array.isArray(post.tags)) {
                        // Tags are already an array of objects with name and slug
                        tags = post.tags.map(tag => {
                            if (typeof tag === 'object' && tag.name) {
                                return {
                                    name: tag.name,
                                    slug: tag.slug || tag.name.toLowerCase().replace(/[^a-z0-9]/g, '-')
                                };
                            } else if (typeof tag === 'string') {
                                return {
                                    name: tag,
                                    slug: tag.toLowerCase().replace(/[^a-z0-9]/g, '-')
                                };
                            }
                            return null;
                        }).filter(tag => tag !== null);
                    } else if (typeof post.tags === 'string') {
                        // Extract hashtags from string
                        const hashtagMatches = post.tags.match(/#[a-zA-ZşğüöçıİŞĞÜÖÇ0-9]+/g);
                        if (hashtagMatches) {
                            tags = hashtagMatches.map(tag => ({
                                name: tag,
                                slug: tag.substring(1).toLowerCase().replace(/[^a-z0-9]/g, '-')
                            }));
                        } else {
                            // Split by comma or space if no hashtags
                            const tagNames = post.tags.split(/[,\s]+/).filter(tag => tag.trim().length > 0);
                            tags = tagNames.map(tag => ({
                                name: tag,
                                slug: tag.toLowerCase().replace(/[^a-z0-9]/g, '-')
                            }));
                        }
                    }

                    console.log('Processed tags:', tags);
                    console.log('Tags length after processing:', tags.length);

                    if (tags.length > 0) {
                        const tagsHtml = tags.map(tag =>
                            `<a href="/blog.php" onclick="goToBlogWithTag('${tag.slug}'); return false;" class="tag-item">${tag.name}</a>`
                        ).join('');

                        console.log('Generated tags HTML:', tagsHtml);
                        tagsContainer.innerHTML = tagsHtml;
                        console.log('✅ Tags rendered successfully');
                    } else {
                        // Fallback to default tags if no tags found
                        tagsContainer.innerHTML = `
                            <a href="/blog?tag=salamura" class="tag-item">salamura</a>
                            <a href="/blog?tag=doğal" class="tag-item">doğal</a>
                            <a href="/blog?tag=sağlıklı" class="tag-item">sağlıklı</a>
                        `;
                    }
                } else {
                    console.log('❌ Tags rendering failed');
                    console.log('tagsContainer exists:', !!tagsContainer);
                    console.log('post.tags exists:', !!post.tags);

                    if (tagsContainer) {
                        // Default tags if no post.tags
                        console.log('🔄 Using default tags');
                        tagsContainer.innerHTML = `
                            <a href="/blog?tag=salamura" class="tag-item">salamura</a>
                            <a href="/blog?tag=doğal" class="tag-item">doğal</a>
                            <a href="/blog?tag=sağlıklı" class="tag-item">sağlıklı</a>
                        `;
                    }
                }

                // Show content, hide loading
                const loadingScreen = document.getElementById('loadingScreen');
                const blogDetailContent = document.getElementById('blogDetailContent');

                if (loadingScreen) loadingScreen.style.display = 'none';
                if (blogDetailContent) blogDetailContent.style.display = 'block';

                console.log('Blog post displayed successfully');

            } catch (error) {
                console.error('Error displaying blog post:', error);
                showError();
            }
        }
        
        function updateMetaTags(post) {
            // Update meta description
            const metaDesc = document.querySelector('meta[name="description"]');
            if (metaDesc) {
                metaDesc.content = post.meta_description || post.excerpt || post.title;
            }
            
            // Add Open Graph meta tags
            updateOrCreateMeta('property', 'og:title', post.title);
            updateOrCreateMeta('property', 'og:description', post.meta_description || post.excerpt || '');
            updateOrCreateMeta('property', 'og:image', post.featured_image || '/images/logo.jpg');
            updateOrCreateMeta('property', 'og:url', window.location.href);
        }
        
        function updateOrCreateMeta(attribute, name, content) {
            let meta = document.querySelector(`meta[${attribute}="${name}"]`);
            if (!meta) {
                meta = document.createElement('meta');
                meta.setAttribute(attribute, name);
                document.head.appendChild(meta);
            }
            meta.content = content;
        }

        function formatBlogContent(content) {
            if (!content) return '';

            // Convert markdown-style formatting to HTML
            let formatted = content
                // Convert \r\n to \n for consistency
                .replace(/\r\n/g, '\n')
                // Convert \r to \n for consistency
                .replace(/\r/g, '\n')

                // Convert headers
                .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                .replace(/^# (.*$)/gm, '<h1>$1</h1>')

                // Convert bold and italic
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')

                // Convert lists
                .replace(/^- (.*$)/gm, '<li>$1</li>')
                .replace(/^\d+\. (.*$)/gm, '<li>$1</li>')

                // Convert quotes
                .replace(/^> (.*$)/gm, '<blockquote>$1</blockquote>')

                // Convert links
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')

                // Convert double line breaks to paragraphs
                .replace(/\n\n+/g, '</p><p>')
                // Convert single line breaks to <br>
                .replace(/\n/g, '<br>')

                // Wrap lists in ul/ol tags
                .replace(/(<li>.*?<\/li>)(<br>)*(<li>.*?<\/li>)/g, function(match, p1, p2, p3) {
                    return p1 + p3; // Remove <br> between list items
                })
                .replace(/(<li>.*?<\/li>(<li>.*?<\/li>)*)/g, '<ul>$1</ul>')

                // Wrap in paragraph tags
                .replace(/^/, '<p>')
                .replace(/$/, '</p>')

                // Clean up empty paragraphs and fix formatting
                .replace(/<p><\/p>/g, '')
                .replace(/<p><br><\/p>/g, '')
                .replace(/<p>(<h[1-6]>.*?<\/h[1-6]>)<\/p>/g, '$1')
                .replace(/<p>(<ul>.*?<\/ul>)<\/p>/g, '$1')
                .replace(/<p>(<blockquote>.*?<\/blockquote>)<\/p>/g, '$1')
                .replace(/<br><\/p>/g, '</p>')
                .replace(/<p><br>/g, '<p>');

            // Fix emoji encoding issues
            formatted = formatted
                // Common emoji fixes - replace question marks with proper emojis
                .replace(/\?\?\?\?/g, '🌿')
                .replace(/\?\?\?/g, '🌱')
                .replace(/\?\?/g, '🍇')
                // Specific emoji patterns that might be corrupted
                .replace(/ð\?\?\?/g, '🌿')
                .replace(/ð\?\?/g, '🌱')
                .replace(/ð\?\?\?/g, '🍇')
                .replace(/ð\?\?\?/g, '🥒')
                .replace(/ð\?\?\?/g, '🏠')
                .replace(/ð\?\?\?/g, '💚')
                // You can add more emoji fixes here as needed

            return formatted;
        }

        async function incrementViewCount(postId) {
            try {
                // This would call an API to increment view count
                // await fetch(`/api/blog/increment-view?id=${postId}`, { method: 'POST' });
            } catch (error) {
                console.error('View count increment error:', error);
            }
        }
        
        async function loadRelatedPosts(categorySlug, currentPostId) {
            try {
                const response = await fetch(`/api/blog/posts.php?category=${categorySlug}&limit=3`);
                const data = await response.json();
                
                if (data.success && data.posts.length > 0) {
                    const relatedPosts = data.posts.filter(post => post.id !== currentPostId).slice(0, 3);
                    displayRelatedPosts(relatedPosts);
                }
            } catch (error) {
                console.error('Related posts loading error:', error);
            }
        }
        
        function displayRelatedPosts(posts) {
            const container = document.getElementById('relatedPosts');
            
            if (posts.length === 0) {
                container.innerHTML = '<p class="text-center text-muted">İlgili yazı bulunamadı.</p>';
                return;
            }
            
            const postsHtml = posts.map(post => {
                const excerpt = post.excerpt || post.plain_content || '';
                const truncatedExcerpt = excerpt.length > 120 ? excerpt.substring(0, 120) + '...' : excerpt;

                return `
                    <div class="col-md-4 mb-4">
                        <article class="related-post-card">
                            <img src="${post.featured_image || '/images/logo.jpg'}"
                                 alt="${post.title}"
                                 onerror="this.src='/images/logo.jpg'">
                            <div class="card-body">
                                <h5 class="card-title">${post.title}</h5>
                                <p class="card-text">${truncatedExcerpt}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="/blog/${post.slug}" class="btn btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>Oku
                                    </a>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>${post.reading_time || 5} dk
                                    </small>
                                </div>
                            </div>
                        </article>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = postsHtml;
        }
        
        function showError() {
            console.log('Showing error state');

            const loadingScreen = document.getElementById('loadingScreen');
            const blogDetailContent = document.getElementById('blogDetailContent');

            if (loadingScreen) loadingScreen.style.display = 'none';

            // Show error in the main content area
            if (blogDetailContent) {
                blogDetailContent.innerHTML = `
                    <div class="container my-5">
                        <div class="row justify-content-center">
                            <div class="col-lg-8 text-center">
                                <div class="error-content">
                                    <i class="fas fa-exclamation-triangle fa-4x text-warning mb-4"></i>
                                    <h2 class="mb-3">Blog Yazısı Bulunamadı</h2>
                                    <p class="text-muted mb-4">Aradığınız blog yazısı bulunamadı veya kaldırılmış olabilir.</p>
                                    <a href="/blog.php" class="btn btn-primary">
                                        <i class="fas fa-arrow-left me-2"></i>Blog'a Geri Dön
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                blogDetailContent.style.display = 'block';
            }
        }
        
        function formatDate(dateString) {
            const date = new Date(dateString);
            const options = { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                timeZone: 'Europe/Istanbul'
            };
            return date.toLocaleDateString('tr-TR', options);
        }
        
        // Social sharing functions
        function shareOnFacebook() {
            if (currentPost) {
                const url = encodeURIComponent(window.location.href);
                window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank', 'width=600,height=400');
            }
        }
        
        function shareOnTwitter() {
            if (currentPost) {
                const url = encodeURIComponent(window.location.href);
                const text = encodeURIComponent(currentPost.title);
                window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank', 'width=600,height=400');
            }
        }
        
        function shareOnWhatsApp() {
            if (currentPost) {
                const url = encodeURIComponent(window.location.href);
                const text = encodeURIComponent(`${currentPost.title} - ${window.location.href}`);
                window.open(`https://wa.me/?text=${text}`, '_blank');
            }
        }
        
        function shareOnLinkedIn() {
            if (currentPost) {
                const url = encodeURIComponent(window.location.href);
                const title = encodeURIComponent(currentPost.title);
                window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`, '_blank', 'width=600,height=400');
            }
        }

        function copyToClipboard() {
            const url = window.location.href;
            navigator.clipboard.writeText(url).then(() => {
                // Show success message
                const copyBtn = document.querySelector('.share-btn.copy');
                const originalIcon = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i>';
                copyBtn.style.background = '#28a745';

                setTimeout(() => {
                    copyBtn.innerHTML = originalIcon;
                    copyBtn.style.background = '#6c757d';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            });
        }

        // Function to navigate to blog page with tag filter
        function goToBlogWithTag(tagSlug) {
            console.log('Navigating to blog with tag:', tagSlug);
            // Store the tag in sessionStorage so blog page can pick it up
            sessionStorage.setItem('filterTag', tagSlug);
            // Navigate to blog page
            window.location.href = '/blog.php';
        }
    </script>
</body>
</html>
