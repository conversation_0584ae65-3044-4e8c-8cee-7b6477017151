/**
 * Independent Mobile Menu System
 * Works across all pages with consistent functionality
 */

// Independent Mobile Menu Functions
function toggleIndependentMobileMenu() {
    const mobileMenu = document.getElementById('independentMobileMenu');
    if (mobileMenu && mobileMenu.classList.contains('show')) {
        closeIndependentMobileMenu();
    } else {
        openIndependentMobileMenu();
    }
}

function openIndependentMobileMenu() {
    const mobileMenu = document.getElementById('independentMobileMenu');
    const backdrop = document.getElementById('mobileMenuBackdrop');

    if (mobileMenu) {
        mobileMenu.classList.add('show');
    }
    if (backdrop) {
        backdrop.classList.add('show');
    }
    document.body.classList.add('mobile-menu-open');

    // Update user info when menu opens
    setTimeout(updateMobileMenuUserInfo, 50);
}

function closeIndependentMobileMenu() {
    const mobileMenu = document.getElementById('independentMobileMenu');
    const backdrop = document.getElementById('mobileMenuBackdrop');
    
    if (mobileMenu) {
        mobileMenu.classList.remove('show');
    }
    if (backdrop) {
        backdrop.classList.remove('show');
    }
    document.body.classList.remove('mobile-menu-open');
}

// Legacy function for compatibility
function closeMobileMenu() {
    closeIndependentMobileMenu();
}

// Handle mobile logout
function handleMobileLogout() {
    closeIndependentMobileMenu();
    if (typeof logout === 'function') {
        logout();
    } else {
        // Fallback if logout function is not available
        localStorage.removeItem("userInfo");
        window.location.reload();
    }
}

// Initialize mobile menu when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // ESC key to close mobile menu
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeIndependentMobileMenu();
        }
    });

    // Update hamburger buttons to use new system
    const hamburgerButtons = document.querySelectorAll('.navbar-toggler');
    hamburgerButtons.forEach(button => {
        // Remove old Bootstrap attributes
        button.removeAttribute('data-bs-toggle');
        button.removeAttribute('data-bs-target');
        button.removeAttribute('aria-controls');
        button.removeAttribute('aria-expanded');

        // Add new click handler
        button.onclick = toggleIndependentMobileMenu;
    });

    // Ensure mobile menu HTML exists
    if (!document.getElementById('independentMobileMenu')) {
        createMobileMenuHTML();
    }

    // Update mobile menu user info after creation
    setTimeout(updateMobileMenuUserInfo, 100);
});

// Create mobile menu HTML if it doesn't exist
function createMobileMenuHTML() {
    const currentPage = window.location.pathname;
    const isHomePage = currentPage === '/' || currentPage === '/index.php' || currentPage.endsWith('/');
    const isBlogPage = currentPage.includes('blog.php');
    
    const mobileMenuHTML = `
    <!-- Mobile Menu Backdrop -->
    <div id="mobileMenuBackdrop" class="mobile-menu-backdrop" onclick="closeIndependentMobileMenu()"></div>
    
    <!-- Independent Mobile Menu -->
    <div id="independentMobileMenu" class="independent-mobile-menu">
        <div class="mobile-menu-container">
            <button class="mobile-menu-close" type="button" onclick="closeIndependentMobileMenu()" aria-label="Menüyü kapat">
                <span class="sr-only">Kapat</span>
            </button>
            
            <div class="mobile-menu-header">
                <div class="brand-text">
                    <span class="brand-line-1">Tansu Şahal</span>
                    <span class="brand-line-2">Salamura</span>
                </div>
            </div>
            
            <ul class="mobile-nav-links">
                <li class="nav-item"><a class="nav-link ${isHomePage ? 'active' : ''}" href="/" onclick="closeIndependentMobileMenu()">🏠 Ana Sayfa</a></li>
                <li class="nav-item"><a class="nav-link" href="/#products" onclick="closeIndependentMobileMenu()">🛍️ Tüm Ürünler</a></li>
                <li class="nav-item"><a class="nav-link" href="/#about-us-section" onclick="closeIndependentMobileMenu()">🌿 Hakkımızda</a></li>
                <li class="nav-item"><a class="nav-link ${isBlogPage ? 'active' : ''}" href="blog.php" onclick="closeIndependentMobileMenu()">📝 Blog</a></li>
                <li class="nav-item"><a class="nav-link" href="/#contact-section" onclick="closeIndependentMobileMenu()">📞 İletişim</a></li>
            </ul>
            
            <div class="mobile-user-section">
                <div id="mobileUserGreeting3" class="mobile-user-greeting" style="display:none;"></div>
                
                <div class="mobile-auth-buttons">
                    <a href="login.php" id="mobileLoginLink3" class="mobile-auth-button secondary" onclick="closeIndependentMobileMenu()">
                        <i class="fas fa-sign-in-alt"></i> Giriş Yap
                    </a>
                    <a href="register.php" id="mobileRegisterLink3" class="mobile-auth-button primary" onclick="closeIndependentMobileMenu()">
                        <i class="fas fa-user-plus"></i> Kayıt Ol
                    </a>
                    <a href="#" id="mobileLogoutLink3" class="mobile-auth-button secondary" style="display:none;" onclick="handleMobileLogout()">
                        <i class="fas fa-sign-out-alt"></i> Çıkış Yap
                    </a>
                    <a href="admin.php" id="mobileAdminLink3" class="mobile-auth-button primary" style="display:none;" onclick="closeIndependentMobileMenu()">
                        <i class="fas fa-cog"></i> Admin
                    </a>
                </div>
                
                <a href="favorites.php" class="mobile-favorites-button" aria-label="Favoriler" onclick="closeIndependentMobileMenu()">
                    <i class="fas fa-heart"></i> Favoriler
                    <span id="mobileFavoritesCount3" class="badge bg-white text-danger">0</span>
                </a>
            </div>
            
            <div class="mobile-menu-footer">
                <p>🌱 Doğallığın ve Lezzetin Adresi</p>
            </div>
        </div>
    </div>`;
    
    // Insert before closing body tag
    document.body.insertAdjacentHTML('beforeend', mobileMenuHTML);
}

// Update mobile menu user info based on desktop navbar
function updateMobileMenuUserInfo() {
    // Get desktop user info
    const desktopUserGreeting = document.getElementById('userGreeting');
    const desktopLoginLink = document.getElementById('loginLink');
    const desktopRegisterLink = document.getElementById('registerLink');
    const desktopLogoutLink = document.getElementById('logoutLink');
    const desktopAdminLink = document.getElementById('adminLink');
    const desktopFavoritesCount = document.getElementById('favorites-item-count');

    // Get mobile user info elements (try all possible IDs)
    let mobileUserGreeting = document.getElementById('mobileUserGreeting3') ||
                            document.getElementById('mobileUserGreeting2') ||
                            document.getElementById('mobileUserGreeting');
    let mobileLoginLink = document.getElementById('mobileLoginLink3') ||
                         document.getElementById('mobileLoginLink2') ||
                         document.getElementById('mobileLoginLink');
    let mobileRegisterLink = document.getElementById('mobileRegisterLink3') ||
                            document.getElementById('mobileRegisterLink2') ||
                            document.getElementById('mobileRegisterLink');
    let mobileLogoutLink = document.getElementById('mobileLogoutLink3') ||
                          document.getElementById('mobileLogoutLink2') ||
                          document.getElementById('mobileLogoutLink');
    let mobileAdminLink = document.getElementById('mobileAdminLink3') ||
                         document.getElementById('mobileAdminLink2') ||
                         document.getElementById('mobileAdminLink');
    let mobileFavoritesCount = document.getElementById('mobileFavoritesCount3') ||
                              document.getElementById('mobileFavoritesCount2') ||
                              document.getElementById('mobileFavoritesCount');

    if (!mobileUserGreeting) return; // Mobile menu not created yet

    // Sync user greeting
    if (desktopUserGreeting && mobileUserGreeting) {
        if (desktopUserGreeting.style.display !== 'none' && desktopUserGreeting.innerHTML.trim()) {
            mobileUserGreeting.innerHTML = desktopUserGreeting.innerHTML;
            mobileUserGreeting.style.display = 'block';
        } else {
            mobileUserGreeting.style.display = 'none';
        }
    }

    // Sync login link
    if (desktopLoginLink && mobileLoginLink) {
        mobileLoginLink.style.display = desktopLoginLink.style.display;
    }

    // Sync register link
    if (desktopRegisterLink && mobileRegisterLink) {
        mobileRegisterLink.style.display = desktopRegisterLink.style.display;
    }

    // Sync logout link
    if (desktopLogoutLink && mobileLogoutLink) {
        mobileLogoutLink.style.display = desktopLogoutLink.style.display;
    }

    // Sync admin link
    if (desktopAdminLink && mobileAdminLink) {
        mobileAdminLink.style.display = desktopAdminLink.style.display;
    }

    // Sync favorites count
    if (desktopFavoritesCount && mobileFavoritesCount) {
        mobileFavoritesCount.textContent = desktopFavoritesCount.textContent;
    }
}

// Watch for changes in desktop navbar and update mobile menu
function watchDesktopNavbarChanges() {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' || mutation.type === 'childList') {
                updateMobileMenuUserInfo();
            }
        });
    });

    // Observe desktop navbar for changes
    const desktopNavbar = document.querySelector('.navbar-user-info');
    if (desktopNavbar) {
        observer.observe(desktopNavbar, {
            attributes: true,
            childList: true,
            subtree: true,
            attributeFilter: ['style', 'class']
        });
    }
}

// Start watching after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(watchDesktopNavbarChanges, 200);
});
