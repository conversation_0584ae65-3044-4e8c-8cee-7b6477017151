<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

require_once '../../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $limit = isset($_GET['limit']) ? min(50, max(1, intval($_GET['limit']))) : 20;
    $popular = isset($_GET['popular']) && $_GET['popular'] === 'true';
    
    if ($popular) {
        // Get popular tags (used in published posts)
        $sql = "SELECT 
                    bt.id,
                    bt.name,
                    bt.slug,
                    COUNT(bpt.post_id) as usage_count
                FROM blog_tags bt
                INNER JOIN blog_post_tags bpt ON bt.id = bpt.tag_id
                INNER JOIN blog_posts bp ON bpt.post_id = bp.id
                WHERE bp.status = 'published'
                GROUP BY bt.id, bt.name, bt.slug
                ORDER BY usage_count DESC, bt.name ASC
                LIMIT :limit";
    } else {
        // Get all tags
        $sql = "SELECT 
                    bt.id,
                    bt.name,
                    bt.slug,
                    COUNT(bpt.post_id) as usage_count
                FROM blog_tags bt
                LEFT JOIN blog_post_tags bpt ON bt.id = bpt.tag_id
                LEFT JOIN blog_posts bp ON bpt.post_id = bp.id AND bp.status = 'published'
                GROUP BY bt.id, bt.name, bt.slug
                ORDER BY bt.name ASC
                LIMIT :limit";
    }
    
    $stmt = $db->prepare($sql);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    $tags = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'tags' => $tags
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("Tags API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Sunucu hatası oluştu.'
    ], JSON_UNESCAPED_UNICODE);
}
?>
