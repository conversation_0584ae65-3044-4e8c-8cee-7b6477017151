document.addEventListener("DOMContentLoaded", async () => {
  
  // Admin kontrolü - GÜVENLİK KONTROLÜ
  const userInfo = getUserInfo()

  // Yetkisiz erişimi engelle
  if (!userInfo || userInfo.role !== "admin") {
    alert("Bu sayfaya erişim yetkiniz bulunmamaktadır. Ana sayfaya yönlendiriliyorsunuz.")
    window.location.href = "index.php?error=unauthorized"
    return
  }

  try {
    await loadProducts()

    if (typeof loadBlogData === 'function') {
      try {
        await loadBlogData()
      } catch (blogError) {
        // Blog data loading failed silently
      }
    }

    updateStats()
    setupEventListeners()

    if (typeof setupBlogEventListeners === 'function') {
      setupBlogEventListeners()
    }
  } catch (error) {
    // Admin initialization failed silently
  }
})

let products = []
let editingProductId = null
let selectedImages = [] // Çoklu resim için seçilen dosyalar
let uploadedImageUrls = [] // Yüklenen resim URL'leri
let mainImageIndex = 0 // Ana resim indexi

function getUserInfo() {
  const userInfoString = localStorage.getItem("userInfo")
  try {
    return userInfoString ? JSON.parse(userInfoString) : null
  } catch (e) {
    localStorage.removeItem("userInfo")
    return null
  }
}

async function loadProducts() {
  try {
    const userInfo = getUserInfo()
    
    // Try admin API first if authenticated
    if (userInfo && userInfo.token) {
      try {
        const response = await fetch("/api/admin/products.php", {
          headers: {
            Authorization: `Bearer ${userInfo.token}`,
            "Content-Type": "application/json",
          },
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            products = data.products
            displayProducts()
            updateStats()
            return
          }
        }
      } catch (error) {
        console.log("Admin API failed, trying public API")
      }
    }

    // Fallback to public API
    const response = await fetch("/api/products/index.php", {
      headers: {
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error("Ürünler yüklenemedi")
    }

    products = await response.json()
    displayProducts()
    updateStats()
  } catch (error) {
    console.error("Load products error:", error)
    showNotification("Ürünler yüklenirken hata oluştu: " + error.message, "error")
  }
}

function displayProducts() {
  const tbody = document.getElementById("productListBody")
  if (!tbody) return

  tbody.innerHTML = ""

  if (products.length === 0) {
    tbody.innerHTML = `
      <tr>
        <td colspan="6" class="text-center py-4">
          <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
          <p class="text-muted">Henüz ürün eklenmemiş</p>
        </td>
      </tr>
    `
    return
  }

  products.forEach((product) => {
    const row = document.createElement("tr")

    // Çoklu resim desteği: tüm resimleri göster
    let images = []
    if (Array.isArray(product.images) && product.images.length > 0) {
      images = product.images
    } else if (product.image) {
      images = [product.image]
    } else {
      images = ["/images/default-product.png"]
    }

    let imagesHtml = images
      .slice(0, 3)
      .map(
        (url, index) =>
          `<img src="${url}" alt="${product.name}" class="product-image me-1 mb-1" 
           style="width: 35px; height: 35px; object-fit: cover; border-radius: 6px; ${index === 0 ? "border: 2px solid var(--primary-green);" : ""}" 
           title="${index === 0 ? "Ana resim" : `Resim ${index + 1}`}">`,
      )
      .join("")

    if (images.length > 3) {
      imagesHtml += `<div class="text-muted small">+${images.length - 3} daha</div>`
    }

    const hasVideo = product.video_url
      ? '<i class="fas fa-video text-success" title="Video mevcut"></i>'
      : '<i class="fas fa-video-slash text-muted" title="Video yok"></i>'

    row.innerHTML = `
      <td style="min-width:120px;">
        <div class="d-flex flex-wrap align-items-center">
          ${imagesHtml}
        </div>
      </td>
      <td>
        <div class="fw-semibold">${product.name}</div>
        <small class="text-muted">${product.category || "Kategori yok"}</small>
      </td>
      <td class="text-end">
        <span class="fw-bold text-success">${(product.price / 100).toFixed(2)} TL</span>
      </td>
      <td class="text-center">
        <span class="badge ${product.stock > 0 ? "bg-success" : "bg-danger"}">${product.stock}</span>
      </td>
      <td class="text-center">${hasVideo}</td>
      <td class="text-center">
        <div class="btn-group" role="group">
          <button type="button" class="btn btn-outline-primary btn-sm edit-btn" data-product-id="${product._id}" title="Düzenle">
            <i class="fas fa-edit"></i>
          </button>
          <button type="button" class="btn btn-outline-danger btn-sm delete-btn" data-product-id="${product._id}" title="Sil">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </td>
    `
    tbody.appendChild(row)
  })

  // Event listener'ları ekle
  document.querySelectorAll(".edit-btn").forEach((btn) => {
    btn.addEventListener("click", (e) => {
      const productId = e.currentTarget.getAttribute("data-product-id")
      editProduct(productId)
    })
  })

  document.querySelectorAll(".delete-btn").forEach((btn) => {
    btn.addEventListener("click", (e) => {
      const productId = e.currentTarget.getAttribute("data-product-id")
      deleteProduct(productId)
    })
  })
}

function updateStats() {
  const totalProducts = products.length
  const totalStock = products.reduce((sum, product) => sum + Number.parseInt(product.stock), 0)

  const totalProductsElement = document.getElementById("totalProducts")
  const totalStockElement = document.getElementById("totalStock")

  if (totalProductsElement) {
    totalProductsElement.textContent = totalProducts
  }
  if (totalStockElement) {
    totalStockElement.textContent = totalStock
    console.log('Set total stock to:', totalStock)
  }
}

function setupEventListeners() {
  const imagesInput = document.getElementById("images")
  const imagesPreview = document.getElementById("imagesPreview")
  const productForm = document.getElementById("productForm")
  const clearFormButton = document.getElementById("clearFormButton")

  // Çoklu resim seçimi event listener
  if (imagesInput && imagesPreview) {
    imagesInput.addEventListener("change", handleImageSelection)
  }

  if (productForm) {
    productForm.addEventListener("submit", handleProductSubmit)
  }

  if (clearFormButton) {
    clearFormButton.addEventListener("click", clearForm)
  }
}

function handleImageSelection(event) {
  const files = Array.from(event.target.files)
  const imagesPreview = document.getElementById("imagesPreview")

  if (files.length === 0) {
    resetImagePreview()
    return
  }

  selectedImages = files

  // DÜZELTME: Mevcut resimleri koru, sadece yeni seçilen dosyaları ekle
  // uploadedImageUrls = [] // Bu satırı kaldırıyoruz

  // Eğer düzenleme modunda değilsek, uploadedImageUrls'i sıfırla
  if (!editingProductId) {
    uploadedImageUrls = []
    mainImageIndex = 0
  }

  // Önizleme alanını temizle
  imagesPreview.innerHTML = ""

  // Önce mevcut resimleri göster (düzenleme modundaysa)
  if (editingProductId && uploadedImageUrls.length > 0) {
    uploadedImageUrls.forEach((imageUrl, index) => {
      const imageItem = document.createElement("div")
      imageItem.className = "image-preview-item existing-image"
      imageItem.dataset.index = index
      imageItem.dataset.type = "existing"

      imageItem.innerHTML = `
        <img src="${imageUrl}" alt="Mevcut resim" class="image-preview ${index === mainImageIndex ? "main-image" : ""}"
             onclick="setMainImage(${index})" title="Ana resim yapmak için tıklayın">
        <button type="button" class="image-remove-btn" onclick="removeExistingImage(${index})" title="Resmi kaldır">
          <i class="fas fa-times"></i>
        </button>
        ${index === mainImageIndex ? '<div class="main-image-badge">ANA</div>' : ""}
      `
      imagesPreview.appendChild(imageItem)
    })
  }

  // Sonra yeni seçilen dosyaları ekle
  files.forEach((file, index) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const actualIndex = uploadedImageUrls.length + index
      const imageItem = createImagePreviewItem(e.target.result, file.name, actualIndex, true)
      imagesPreview.appendChild(imageItem)
    }
    reader.readAsDataURL(file)
  })
}

function createImagePreviewItem(src, fileName, index, isNewFile = false) {
  const imageItem = document.createElement("div")
  imageItem.className = "image-preview-item"
  imageItem.dataset.index = index
  imageItem.dataset.type = isNewFile ? "new" : "existing"

  const removeFunction = isNewFile ? "removeImage" : "removeExistingImage"

  imageItem.innerHTML = `
    <img src="${src}" alt="${fileName}" class="image-preview ${index === mainImageIndex ? "main-image" : ""}"
         onclick="setMainImage(${index})" title="Ana resim yapmak için tıklayın">
    <button type="button" class="image-remove-btn" onclick="${removeFunction}(${index})" title="Resmi kaldır">
      <i class="fas fa-times"></i>
    </button>
    ${index === mainImageIndex ? '<div class="main-image-badge">ANA</div>' : ""}
  `

  return imageItem
}

function setMainImage(index) {
  console.log("Setting main image to index:", index)
  mainImageIndex = index
  const imagesPreview = document.getElementById("imagesPreview")

  // Tüm resimlerdeki ana resim işaretlerini kaldır
  imagesPreview.querySelectorAll(".image-preview").forEach((img) => {
    img.classList.remove("main-image")
  })
  imagesPreview.querySelectorAll(".main-image-badge").forEach((badge) => {
    badge.remove()
  })

  // Yeni ana resmi işaretle
  const selectedItem = imagesPreview.querySelector(`[data-index="${index}"]`)
  if (selectedItem) {
    const img = selectedItem.querySelector(".image-preview")
    img.classList.add("main-image")

    const badge = document.createElement("div")
    badge.className = "main-image-badge"
    badge.textContent = "ANA"
    selectedItem.appendChild(badge)
  }

  console.log("Main image index updated to:", mainImageIndex)
  console.log("Current uploadedImageUrls:", uploadedImageUrls)
  showNotification("Ana resim değiştirildi", "success")
}

function removeImage(index) {
  // Seçilen dosyaları güncelle
  const newFiles = Array.from(selectedImages)
  newFiles.splice(index, 1)

  // FileList'i yeniden oluştur (bu biraz karmaşık)
  const dt = new DataTransfer()
  newFiles.forEach((file) => dt.items.add(file))
  document.getElementById("images").files = dt.files

  selectedImages = newFiles

  // Ana resim indexini güncelle
  if (index === mainImageIndex && newFiles.length > 0) {
    mainImageIndex = 0
  } else if (index < mainImageIndex) {
    mainImageIndex--
  }

  // Önizlemeyi yeniden oluştur
  if (newFiles.length === 0) {
    resetImagePreview()
  } else {
    handleImageSelection({ target: { files: newFiles } })
  }

  showNotification("Resim kaldırıldı", "info")
}

function removeExistingImage(index) {
  // Mevcut resimleri uploadedImageUrls'ten kaldır
  uploadedImageUrls.splice(index, 1)

  // Ana resim indexini güncelle
  if (index === mainImageIndex && uploadedImageUrls.length > 0) {
    mainImageIndex = 0
  } else if (index < mainImageIndex) {
    mainImageIndex--
  }

  // Önizlemeyi yeniden oluştur
  const imagesPreview = document.getElementById("imagesPreview")
  if (uploadedImageUrls.length === 0) {
    resetImagePreview()
  } else {
    imagesPreview.innerHTML = ""
    uploadedImageUrls.forEach((imageUrl, newIndex) => {
      const imageItem = document.createElement("div")
      imageItem.className = "image-preview-item"
      imageItem.dataset.index = newIndex

      imageItem.innerHTML = `
        <img src="${imageUrl}" alt="Resim" class="image-preview ${newIndex === mainImageIndex ? "main-image" : ""}" 
             onclick="setMainImage(${newIndex})" title="Ana resim yapmak için tıklayın">
        <button type="button" class="image-remove-btn" onclick="removeExistingImage(${newIndex})" title="Resmi kaldır">
          <i class="fas fa-times"></i>
        </button>
        ${newIndex === mainImageIndex ? '<div class="main-image-badge">ANA</div>' : ""}
      `

      imagesPreview.appendChild(imageItem)
    })
  }

  showNotification("Resim kaldırıldı", "info")
}

function resetImagePreview() {
  const imagesPreview = document.getElementById("imagesPreview")
  imagesPreview.innerHTML = `
    <div class="text-muted text-center w-100">
      <i class="fas fa-images fa-2x mb-2"></i>
      <p class="mb-0">Seçilen görseller burada görünecek</p>
    </div>
  `
  selectedImages = []
  uploadedImageUrls = []
  mainImageIndex = 0
}

async function handleProductSubmit(e) {
  e.preventDefault()

  // Video dosyası kontrolü
  const videoFile = document.getElementById("videoFile").files[0]
  let videoUrl = document.getElementById("videoUrl").value

  // Eğer video dosyası seçildiyse önce onu yükle
  if (videoFile) {
    try {
      showNotification("Video yükleniyor...", "info")
      const uploadedVideoUrl = await uploadVideoFile(videoFile)
      videoUrl = uploadedVideoUrl
    } catch (error) {
      showNotification("Video yüklenirken hata oluştu: " + error.message, "error")
      return
    }
  }

  // Çoklu görsel yükleme
  if (selectedImages.length > 0) {
    try {
      showUploadProgress(true)
      const newImageUrls = await uploadMultipleImages(selectedImages)
      showUploadProgress(false)

      // DÜZELTME: Yeni resimleri mevcut resimlere ekle, değiştirme
      if (editingProductId) {
        // Düzenleme modunda: yeni resimleri mevcut resimlerin sonuna ekle
        uploadedImageUrls = [...uploadedImageUrls, ...newImageUrls]
      } else {
        // Yeni ürün modunda: sadece yeni resimler
        uploadedImageUrls = newImageUrls
      }

      // Ana resmi ilk sıraya taşı
      if (mainImageIndex > 0 && uploadedImageUrls.length > 1) {
        const mainImageUrl = uploadedImageUrls[mainImageIndex]
        uploadedImageUrls.splice(mainImageIndex, 1)
        uploadedImageUrls.unshift(mainImageUrl)
      }
    } catch (error) {
      showUploadProgress(false)
      showNotification("Görseller yüklenirken hata oluştu: " + error.message, "error")
      return
    }
  } else if (editingProductId && uploadedImageUrls.length > 0) {
    // Düzenleme modunda resim seçimi değişmişse ana resmi yeniden sırala
    console.log("Edit mode - mainImageIndex:", mainImageIndex, "uploadedImageUrls:", uploadedImageUrls)
    if (mainImageIndex > 0 && uploadedImageUrls.length > 1) {
      const mainImageUrl = uploadedImageUrls[mainImageIndex]
      uploadedImageUrls.splice(mainImageIndex, 1)
      uploadedImageUrls.unshift(mainImageUrl)
      console.log("Rearranged images - new order:", uploadedImageUrls)
    }
  }

  const formData = new FormData(e.target)
  const productData = {
    name: formData.get("name"),
    description: formData.get("description"),
    price: Number.parseFloat(formData.get("price")),
    stock: Number.parseInt(formData.get("stock")),
    category: formData.get("category"),
    videoUrl: videoUrl,
    purchaseLink: formData.get("purchaseLink"),
    images: uploadedImageUrls.length > 0 ? uploadedImageUrls : [],
  }
  
  console.log("Final productData images:", productData.images)
  console.log("Final mainImageIndex:", mainImageIndex)

  // Validation
  if (!productData.name || !productData.description || !productData.price || productData.stock < 0) {
    showNotification("Lütfen tüm gerekli alanları doldurun.", "error")
    return
  }

  try {
    const userInfo = getUserInfo()
    const url = editingProductId ? `/api/admin/products.php?id=${editingProductId}` : "/api/admin/products.php"
    const method = editingProductId ? "PUT" : "POST"

    console.log("Sending request:", {
      url,
      method,
      productData,
      editingProductId,
      editingProductIdType: typeof editingProductId
    })

    const response = await fetch(url, {
      method: method,
      headers: {
        Authorization: `Bearer ${userInfo.token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(productData),
    })

    console.log("Response status:", response.status)
    console.log("Response headers:", response.headers)

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Error response:", errorText)
      showNotification(`HTTP ${response.status}: ${errorText}`, "error")
      return
    }

    const result = await response.json()
    console.log("Response result:", result)

    if (result.success) {
      showNotification(result.message, "success")
      clearForm()
      await loadProducts()
    } else {
      showNotification(result.message || "İşlem başarısız", "error")
    }
  } catch (error) {
    console.error("Product submit error:", error)
    showNotification("Bağlantı hatası: " + error.message, "error")
  }
}

async function uploadMultipleImages(imageFiles) {
  const formData = new FormData()

  // Tüm dosyaları FormData'ya ekle - PHP'de images[] array'i olarak alınması için
  imageFiles.forEach((file, index) => {
    formData.append("images[]", file) // "images" yerine "images[]" kullan
  })

  const userInfo = getUserInfo()
  const response = await fetch("/api/admin/upload-image.php", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${userInfo.token}`,
    },
    body: formData,
  })

  const result = await response.json()

  if (result.success) {
    return result.data.imageUrls || [result.data.imageUrl] // Hem çoklu hem tekli desteği
  } else {
    throw new Error(result.message || "Görsel yükleme başarısız")
  }
}

// Video dosyası yükleme fonksiyonu
async function uploadVideoFile(videoFile) {
  const formData = new FormData()
  formData.append("video", videoFile)

  const userInfo = getUserInfo()
  const response = await fetch("/api/admin/upload-video.php", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${userInfo.token}`,
    },
    body: formData,
  })

  const result = await response.json()

  if (result.success) {
    return result.data.videoUrl
  } else {
    throw new Error(result.message || "Video yükleme başarısız")
  }
}

function showUploadProgress(show) {
  const progressContainer = document.getElementById("uploadProgress")
  if (progressContainer) {
    progressContainer.style.display = show ? "block" : "none"
    if (show) {
      // Basit progress animasyonu
      const progressBar = progressContainer.querySelector(".progress-bar")
      let width = 0
      const interval = setInterval(() => {
        width += 10
        progressBar.style.width = width + "%"
        if (width >= 90) {
          clearInterval(interval)
        }
      }, 200)
    }
  }
}

function editProduct(productId) {
  console.log("Editing product:", productId, "Type:", typeof productId)
  const product = products.find((p) => p._id == productId)
  if (!product) {
    console.error("Product not found:", productId)
    return
  }

  console.log("Found product:", product)
  console.log("Product _id:", product._id, "Type:", typeof product._id)
  console.log("Product id:", product.id, "Type:", typeof product.id)

  // Use the numeric id instead of _id if available and ensure it's a number
  editingProductId = parseInt(product.id || product._id)
  console.log("Set editingProductId to:", editingProductId, "Type:", typeof editingProductId)

  // Formu doldur
  document.getElementById("name").value = product.name || ""
  document.getElementById("description").value = product.description || ""
  document.getElementById("price").value = (product.price / 100).toFixed(2)
  document.getElementById("stock").value = product.stock || 0
  document.getElementById("category").value = product.category || ""
  document.getElementById("videoUrl").value = product.video_url || ""
  document.getElementById("purchaseLink").value = product.purchase_link || ""

  // Mevcut resimleri önizleme alanında göster
  const imagesPreview = document.getElementById("imagesPreview")
  if (imagesPreview && product.images && Array.isArray(product.images)) {
    imagesPreview.innerHTML = ""

    product.images.forEach((imageUrl, index) => {
      const imageItem = document.createElement("div")
      imageItem.className = "image-preview-item"
      imageItem.dataset.index = index

      imageItem.innerHTML = `
        <img src="${imageUrl}" alt="${product.name}" class="image-preview ${index === 0 ? "main-image" : ""}" 
             onclick="setMainImage(${index})" title="Ana resim yapmak için tıklayın">
        <button type="button" class="image-remove-btn" onclick="removeExistingImage(${index})" title="Resmi kaldır">
          <i class="fas fa-times"></i>
        </button>
        ${index === 0 ? '<div class="main-image-badge">ANA</div>' : ""}
      `

      imagesPreview.appendChild(imageItem)
    })

    // Mevcut resimleri uploadedImageUrls'e kopyala
    uploadedImageUrls = [...product.images]
    mainImageIndex = 0
    console.log("Loaded existing images:", uploadedImageUrls)
  }

  // Scroll to form
  document.getElementById("productSection").scrollIntoView({ behavior: "smooth" })
  showNotification("Ürün düzenleme moduna geçildi", "info")
}

async function deleteProduct(productId) {
  const product = products.find((p) => p._id == productId)
  if (!product) return

  if (!confirm(`"${product.name}" ürününü silmek istediğinizden emin misiniz?`)) {
    return
  }

  try {
    const userInfo = getUserInfo()
    // Use the numeric id instead of _id, same as in editProduct
    const numericId = parseInt(product.id || product._id)
    console.log("Deleting product with ID:", numericId, "Type:", typeof numericId)
    const response = await fetch(`/api/admin/products.php?id=${numericId}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${userInfo.token}`,
        "Content-Type": "application/json",
      },
    })

    const result = await response.json()

    if (result.success) {
      showNotification(result.message, "success")
      await loadProducts()
    } else {
      showNotification(result.message || "Silme işlemi başarısız", "error")
    }
  } catch (error) {
    console.error("Delete product error:", error)
    showNotification("Bağlantı hatası: " + error.message, "error")
  }
}

function clearForm() {
  editingProductId = null
  document.getElementById("productForm").reset()

  // Tüm form alanlarını manuel olarak temizle
  document.getElementById("name").value = ""
  document.getElementById("description").value = ""
  document.getElementById("price").value = ""
  document.getElementById("stock").value = ""
  document.getElementById("category").value = ""
  document.getElementById("videoUrl").value = ""
  document.getElementById("purchaseLink").value = ""
  document.getElementById("videoFile").value = ""

  // Resim seçimini temizle
  document.getElementById("images").value = ""
  resetImagePreview()

  showNotification("Form temizlendi", "success")
}

function showNotification(message, type = "success") {
  const container = document.getElementById("notificationContainer")
  if (!container) return

  const notification = document.createElement("div")
  notification.className = `notification-bubble ${type}`
  notification.innerHTML = `
    <div class="d-flex align-items-center">
      <i class="fas ${type === "success" ? "fa-check-circle" : type === "error" ? "fa-exclamation-triangle" : "fa-info-circle"} me-2"></i>
      <span>${message}</span>
    </div>
  `

  container.appendChild(notification)

  // Animasyon için timeout
  setTimeout(() => {
    notification.classList.add("show")
  }, 100)

  // 5 saniye sonra kaldır
  setTimeout(() => {
    notification.classList.remove("show")
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification)
      }
    }, 400)
  }, 5000)
}
