<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendErrorResponse('Sadece POST metodu desteklenir', 405);
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    sendErrorResponse('Geçersiz JSON verisi');
}

$required_fields = ['name', 'email', 'subject', 'message'];
foreach ($required_fields as $field) {
    if (!isset($input[$field]) || empty(trim($input[$field]))) {
        sendErrorResponse("$field alanı gereklidir");
    }
}

$name = sanitizeInput($input['name']);
$email = sanitizeInput($input['email']);
$subject = sanitizeInput($input['subject']);
$message = sanitizeInput($input['message']);

if (!validateEmail($email)) {
    sendErrorResponse('Geçersiz email adresi');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->prepare("INSERT INTO contact_messages (name, email, subject, message) VALUES (?, ?, ?, ?)");
    
    if ($stmt->execute([$name, $email, $subject, $message])) {
        // Email gönderme (opsiyonel)
        $to = '<EMAIL>';
        $email_subject = 'Yeni İletişim Mesajı: ' . $subject;
        $email_body = "Ad: $name\nEmail: $email\nKonu: $subject\n\nMesaj:\n$message";
        $headers = "From: $email\r\nReply-To: $email\r\n";
        
        @mail($to, $email_subject, $email_body, $headers);
        
        sendSuccessResponse([], 'Mesajınız başarıyla gönderildi');
    } else {
        sendErrorResponse('Mesaj gönderilirken hata oluştu');
    }
    
} catch(Exception $e) {
    logError('Contact form error: ' . $e->getMessage());
    sendErrorResponse('Sunucu hatası');
}
?>
