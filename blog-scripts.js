// Blog JavaScript Functions
let currentPage = 1;
let currentCategory = 'all';
let currentSearch = '';
const postsPerPage = 6;

document.addEventListener('DOMContentLoaded', () => {
    // Check if we need to filter by tag from blog detail page
    const filterTag = sessionStorage.getItem('filterTag');
    if (filterTag) {
        // Clear the session storage
        sessionStorage.removeItem('filterTag');
        // Apply the tag filter
        setTimeout(() => {
            filterByTag(filterTag);
        }, 500); // Small delay to ensure page is loaded
    } else {
        loadBlogPosts();
    }

    loadPopularPosts();
    loadCategories();
    loadCategoryFilters(); // Load category filter buttons
    loadTags();
    setupEventListeners();
});

// Event Listeners
function setupEventListeners() {
    // Search input
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                searchPosts();
            }
        });
    }
    
    // Newsletter form
    const newsletterForm = document.getElementById('newsletterForm');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', handleNewsletterSignup);
    }
}

// Load Blog Posts
async function loadBlogPosts(page = 1, category = 'all', search = '') {
    const container = document.getElementById('blogPostsContainer');
    const paginationContainer = document.getElementById('paginationContainer');

    if (!container) {
        console.error('Blog posts container not found');
        return;
    }

    try {
        // Show loading
        container.innerHTML = '<div class="col-12 text-center py-5"><i class="fas fa-spinner fa-spin fa-2x text-muted"></i><p class="mt-3 text-muted">Yükleniyor...</p></div>';

        const params = new URLSearchParams({
            page: page,
            limit: postsPerPage,
            category: category,
            search: search,
            status: 'published'
        });

        const apiUrl = `/api/blog/posts.php?${params}`;
        const response = await fetch(apiUrl);

        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            // If not JSON, probably an error page - show mock data
            console.warn('API not available, showing mock data');
            displayMockData(container, paginationContainer);
            return;
        }

        const data = await response.json();

        if (data.success) {
            displayBlogPosts(data.posts);
            displayPagination(data.pagination, paginationContainer);
        } else {
            container.innerHTML = '<div class="col-12 text-center py-5"><p class="text-muted">Blog yazısı bulunamadı.</p></div>';
        }
    } catch (error) {
        console.error('Blog posts loading error:', error);
        console.warn('Showing mock data due to API error');
        displayMockData(container, paginationContainer);
    }
}

// Display Mock Data (for when API is not available)
function displayMockData(container, paginationContainer) {
    const allMockPosts = [
        {
            id: 1,
            title: 'Evde Salamura Nasıl Yapılır? Geleneksel Yöntemler',
            slug: 'evde-salamura-nasil-yapilir',
            excerpt: 'Alaşehir\'in geleneksel salamura yapım teknikleri ile evde kolayca salamura yapabilirsiniz. İşte adım adım salamura tarifi...',
            featured_image: '/images/logo.jpg',
            category_name: 'Salamura Üretimi',
            category_slug: 'salamura-uretimi',
            published_at: '2025-01-20',
            reading_time: 8,
            view_count: 156,
            featured: true
        },
        {
            id: 2,
            title: 'Fermente Gıdaların Sağlık Faydaları',
            slug: 'fermente-gidalar-saglik-faydalari',
            excerpt: 'Probiyotik açısından zengin fermente gıdalar sindirim sisteminizi güçlendirir ve bağışıklık sisteminize destek olur.',
            featured_image: '/images/logo.jpg',
            category_name: 'Doğal Beslenme',
            category_slug: 'dogal-beslenme',
            published_at: '2025-01-18',
            reading_time: 6,
            view_count: 89,
            featured: false
        },
        {
            id: 3,
            title: 'Turşu Çeşitleri ve Yapım Teknikleri',
            slug: 'tursu-cesitleri-yapim-teknikleri',
            excerpt: 'Geleneksel Türk mutfağının vazgeçilmezi turşuların farklı çeşitlerini ve yapım yöntemlerini keşfedin.',
            featured_image: '/images/logo.jpg',
            category_name: 'Turşu Rehberi',
            category_slug: 'tursu-rehberi',
            published_at: '2025-01-15',
            reading_time: 7,
            view_count: 134,
            featured: false
        },
        {
            id: 4,
            title: 'Probiyotik Gıdalar ve Bağırsak Sağlığı',
            slug: 'probiyotik-gidalar-bagirsak-sagligi',
            excerpt: 'Doğal probiyotik kaynakları olan fermente gıdalar bağırsak floranızı destekler ve genel sağlığınızı iyileştirir.',
            featured_image: '/images/logo.jpg',
            category_name: 'Doğal Beslenme',
            category_slug: 'dogal-beslenme',
            published_at: '2025-01-12',
            reading_time: 5,
            view_count: 78,
            featured: false
        },
        {
            id: 5,
            title: 'Geleneksel Alaşehir Salamurası',
            slug: 'geleneksel-alasehir-salamurasi',
            excerpt: 'Alaşehir\'in asırlık salamura geleneği ve bu lezzetli ürünün hikayesi. Nesilden nesile aktarılan tarifler.',
            featured_image: '/images/logo.jpg',
            category_name: 'Geleneksel Yöntemler',
            category_slug: 'geleneksel-yontemler',
            published_at: '2025-01-10',
            reading_time: 9,
            view_count: 203,
            featured: true
        },
        {
            id: 6,
            title: 'Kış Hazırlıkları: Turşu ve Salamura',
            slug: 'kis-hazirliklari-tursu-salamura',
            excerpt: 'Kış aylarına hazırlık için en iyi turşu ve salamura tarifleri. Uzun süre dayanıklı ve lezzetli çözümler.',
            featured_image: '/images/logo.jpg',
            category_name: 'Turşu Rehberi',
            category_slug: 'tursu-rehberi',
            published_at: '2025-01-08',
            reading_time: 6,
            view_count: 145,
            featured: false
        },
        {
            id: 7,
            title: 'Doğal Koruma Yöntemleri',
            slug: 'dogal-koruma-yontemleri',
            excerpt: 'Kimyasal koruyucu kullanmadan doğal yöntemlerle gıda koruma teknikleri ve fermentasyon süreçleri.',
            featured_image: '/images/logo.jpg',
            category_name: 'Geleneksel Yöntemler',
            category_slug: 'geleneksel-yontemler',
            published_at: '2025-01-05',
            reading_time: 7,
            view_count: 112,
            featured: false
        },
        {
            id: 8,
            title: 'Salamura Suyu ve Kullanım Alanları',
            slug: 'salamura-suyu-kullanim-alanlari',
            excerpt: 'Salamura suyunun farklı kullanım alanları ve bu değerli sıvının nasıl değerlendirileceği hakkında ipuçları.',
            featured_image: '/images/logo.jpg',
            category_name: 'Salamura Üretimi',
            category_slug: 'salamura-uretimi',
            published_at: '2025-01-03',
            reading_time: 4,
            view_count: 67,
            featured: false
        }
    ];

    // Sayfalama için veriyi böl
    const startIndex = (currentPage - 1) * postsPerPage;
    const endIndex = startIndex + postsPerPage;
    const currentPosts = allMockPosts.slice(startIndex, endIndex);
    const totalPages = Math.ceil(allMockPosts.length / postsPerPage);

    displayBlogPosts(currentPosts);

    // Mock pagination
    if (paginationContainer && totalPages > 1) {
        const mockPagination = {
            currentPage: currentPage,
            totalPages: totalPages,
            hasNext: currentPage < totalPages,
            hasPrev: currentPage > 1
        };
        displayPagination(mockPagination, paginationContainer);
    } else {
        // Tek sayfa varsa pagination bilgisini gizle
        updatePaginationInfo(currentPage, totalPages);
    }
}

// Display Blog Posts
function displayBlogPosts(posts) {
    const container = document.getElementById('blogPostsContainer');
    
    if (!posts || posts.length === 0) {
        container.innerHTML = '<div class="col-12 text-center py-5"><p class="text-muted">Henüz blog yazısı bulunmuyor.</p></div>';
        return;
    }
    
    const postsHtml = posts.map(post => `
        <div class="col-md-6 mb-4">
            <article class="blog-post-card">
                ${post.featured ? '<span class="featured-badge">✨ Öne Çıkan</span>' : ''}

                <img src="${post.featured_image || '/images/logo.jpg'}"
                     alt="${post.title}"
                     class="post-image"
                     loading="lazy">

                <div class="post-content">
                    <a href="/blog/${post.slug}" class="text-decoration-none">
                        <h2 class="post-title">${post.title}</h2>
                    </a>

                    <p class="post-excerpt">${post.excerpt || (post.content ? post.content.substring(0, 120) + '...' : 'İçerik önizlemesi mevcut değil.')}</p>

                    <div class="post-meta">
                        <div>
                            <a href="#" class="post-category" onclick="filterByCategory('${post.category_slug}')">
                                ${post.category_name}
                            </a>
                            <div class="mt-1">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    ${formatDate(post.published_at)}
                                </small>
                                <small class="text-muted ms-3">
                                    <i class="fas fa-clock me-1"></i>
                                    ${post.reading_time || 5} dk okuma
                                </small>
                            </div>
                        </div>
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-eye me-1"></i>
                                ${post.view_count || 0}
                            </small>
                        </div>
                    </div>
                </div>

                <div class="p-3 pt-0">
                    <a href="/blog/${post.slug}" class="btn btn-outline-primary btn-sm w-100">
                        <i class="fas fa-arrow-right me-2"></i>Devamını Oku
                    </a>
                </div>
            </article>
        </div>
    `).join('');
    
    container.innerHTML = postsHtml;
}

// Display Pagination
function displayPagination(pagination, container) {
    if (!container || !pagination) return;

    const { currentPage, totalPages, hasNext, hasPrev } = pagination;

    if (totalPages <= 1) {
        container.innerHTML = '';
        return;
    }

    let paginationHtml = '';

    // Previous button
    if (hasPrev && currentPage > 1) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link nav-button" href="#" onclick="changePage(${currentPage - 1}); return false;">
                    <i class="fas fa-chevron-left me-1"></i>Önceki
                </a>
            </li>
        `;
    }

    // Page numbers - daha basit yaklaşım
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    // İlk sayfa
    if (startPage > 1) {
        paginationHtml += `<li class="page-item"><a class="page-link page-number" href="#" onclick="changePage(1); return false;">1</a></li>`;
        if (startPage > 2) {
            paginationHtml += `<li class="page-item disabled"><span class="page-link page-number">...</span></li>`;
        }
    }

    // Orta sayfalar
    for (let i = startPage; i <= endPage; i++) {
        if (i === currentPage) {
            paginationHtml += `<li class="page-item active"><span class="page-link page-number">${i}</span></li>`;
        } else {
            paginationHtml += `<li class="page-item"><a class="page-link page-number" href="#" onclick="changePage(${i}); return false;">${i}</a></li>`;
        }
    }

    // Son sayfa
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHtml += `<li class="page-item disabled"><span class="page-link page-number">...</span></li>`;
        }
        paginationHtml += `<li class="page-item"><a class="page-link page-number" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a></li>`;
    }

    // Next button
    if (hasNext && currentPage < totalPages) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link nav-button" href="#" onclick="changePage(${currentPage + 1}); return false;">
                    Sonraki<i class="fas fa-chevron-right ms-1"></i>
                </a>
            </li>
        `;
    }

    container.innerHTML = paginationHtml;

    // Pagination bilgisini güncelle
    updatePaginationInfo(currentPage, totalPages);
}

// Pagination bilgisini güncelle
function updatePaginationInfo(currentPage, totalPages) {
    const paginationInfo = document.getElementById('paginationInfo');
    const paginationText = document.getElementById('paginationText');

    if (paginationInfo && paginationText && totalPages > 1) {
        paginationText.textContent = `Sayfa ${currentPage} / ${totalPages}`;
        paginationInfo.style.display = 'block';
    } else if (paginationInfo) {
        paginationInfo.style.display = 'none';
    }
}

// Load Popular Posts
async function loadPopularPosts() {
    const container = document.getElementById('popularPostsContainer');
    if (!container) return;
    
    try {
        const response = await fetch('/api/blog/posts.php?limit=5&popular=true');
        
        if (!response.ok) {
            throw new Error('API not available');
        }
        
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Invalid response');
        }
        
        const data = await response.json();
        
        if (data.success && data.posts.length > 0) {
            const popularHtml = data.posts.map(post => `
                <div class="popular-post">
                    <img src="${post.featured_image || '/images/logo.jpg'}" 
                         alt="${post.title}" 
                         class="popular-post-image">
                    <div class="popular-post-content">
                        <h6><a href="/blog/${post.slug}" class="text-decoration-none">${post.title}</a></h6>
                        <div class="popular-post-date">${formatDate(post.published_at)}</div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = popularHtml;
        } else {
            container.innerHTML = '<p class="text-muted small">Henüz popüler yazı bulunmuyor.</p>';
        }
    } catch (error) {
        console.error('Popular posts loading error:', error);
        // Show mock popular posts
        const mockPopular = [
            { title: 'Evde Salamura Yapımı', slug: 'evde-salamura-yapimi', published_at: '2025-01-20', featured_image: '/images/logo.jpg' },
            { title: 'Probiyotik Faydaları', slug: 'probiyotik-faydalari', published_at: '2025-01-18', featured_image: '/images/logo.jpg' }
        ];
        
        const popularHtml = mockPopular.map(post => `
            <div class="popular-post">
                <img src="${post.featured_image}" alt="${post.title}" class="popular-post-image">
                <div class="popular-post-content">
                    <h6><a href="#" class="text-decoration-none">${post.title}</a></h6>
                    <div class="popular-post-date">${formatDate(post.published_at)}</div>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = popularHtml;
    }
}

// Load Category Filters (for filter buttons)
async function loadCategoryFilters() {
    const filterContainer = document.querySelector('.category-filter .text-center');
    if (!filterContainer) {
        console.error('Filter container not found');
        return;
    }

    try {
        const response = await fetch('/api/blog/categories.php');
        const data = await response.json();

        if (data.success && data.categories.length > 0) {
            // Create filter buttons HTML
            let filtersHtml = `
                <h5 class="mb-3">📚 Kategoriler</h5>
                <button class="category-btn active" onclick="filterByCategory('all')">Tümü</button>
            `;

            data.categories.forEach(category => {
                filtersHtml += `<button class="category-btn" onclick="filterByCategory('${category.slug}')">${category.name}</button>`;
            });

            filterContainer.innerHTML = filtersHtml;
        } else {
            console.warn('No categories found, using fallback');
            // Fallback to hardcoded categories if API fails
            loadFallbackCategoryFilters(filterContainer);
        }
    } catch (error) {
        console.error('Category filters loading error:', error);
        // Fallback to hardcoded categories
        loadFallbackCategoryFilters(filterContainer);
    }
}

// Fallback category filters
function loadFallbackCategoryFilters(container) {
    container.innerHTML = `
        <h5 class="mb-3">📚 Kategoriler</h5>
        <button class="category-btn active" onclick="filterByCategory('all')">Tümü</button>
        <button class="category-btn" onclick="filterByCategory('salamura-uretimi')">Salamura Üretimi</button>
        <button class="category-btn" onclick="filterByCategory('tursu-rehberi')">Turşu Rehberi</button>
        <button class="category-btn" onclick="filterByCategory('dogal-beslenme')">Doğal Beslenme</button>
        <button class="category-btn" onclick="filterByCategory('geleneksel-yontemler')">Geleneksel Yöntemler</button>
        <button class="category-btn" onclick="filterByCategory('haberler')">Haberler</button>
    `;
}

// Load Categories (for sidebar widget)
async function loadCategories() {
    const container = document.getElementById('categoriesWidget');
    if (!container) return;

    try {
        const response = await fetch('/api/blog/categories.php');
        const data = await response.json();

        if (data.success && data.categories.length > 0) {
            const categoriesHtml = data.categories.map(category => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <a href="#" class="text-decoration-none" onclick="filterByCategory('${category.slug}'); return false;">
                        ${category.name}
                    </a>
                    <span class="badge bg-light text-dark">${category.post_count || 0}</span>
                </div>
            `).join('');

            container.innerHTML = categoriesHtml;
        } else {
            container.innerHTML = '<p class="text-muted small">Kategori bulunmuyor.</p>';
        }
    } catch (error) {
        console.error('Categories loading error:', error);
        // Show mock categories
        const mockCategories = [
            { name: 'Salamura Üretimi', slug: 'salamura-uretimi', post_count: 5 },
            { name: 'Turşu Rehberi', slug: 'tursu-rehberi', post_count: 3 },
            { name: 'Doğal Beslenme', slug: 'dogal-beslenme', post_count: 4 }
        ];

        const categoriesHtml = mockCategories.map(category => `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <a href="#" class="text-decoration-none" onclick="filterByCategory('${category.slug}'); return false;">${category.name}</a>
                <span class="badge bg-light text-dark">${category.post_count}</span>
            </div>
        `).join('');

        container.innerHTML = categoriesHtml;
    }
}

// Load Tags
async function loadTags() {
    const container = document.getElementById('tagsContainer');
    if (!container) return;

    try {
        // Use the correct tags API - get all tags
        const response = await fetch('/api/blog/tags.php?limit=100');
        const data = await response.json();

        if (data.success && data.tags && data.tags.length > 0) {

            // Filter to show only tags that are actually used and sort by popularity
            const usedTags = data.tags.filter(tag => tag.usage_count > 0);
            const tagsToShow = usedTags.length > 0 ? usedTags : data.tags; // Show all if no used tags

            // Sort by usage count (most popular first), then alphabetically
            tagsToShow.sort((a, b) => {
                if (b.usage_count !== a.usage_count) {
                    return b.usage_count - a.usage_count; // Higher usage first
                }
                return a.name.localeCompare(b.name, 'tr'); // Alphabetical for same usage
            });



            const tagsHtml = tagsToShow.map(tag => `
                <a href="#" class="tag-item" onclick="filterByTag('${tag.slug}'); return false;" title="${tag.name} (${tag.usage_count || 0} yazı)">
                    ${tag.name}
                </a>
            `).join('');

            container.innerHTML = tagsHtml;
        } else {
            console.warn('No tags found from API');
            container.innerHTML = '<p class="text-muted small">Etiket bulunmuyor.</p>';
        }
    } catch (error) {
        console.error('Tags loading error:', error);
        // Show mock tags
        const mockTags = [
            { name: '#salamura', slug: 'salamura' },
            { name: '#turşu', slug: 'tursu' },
            { name: '#fermentasyon', slug: 'fermentasyon' },
            { name: '#probiyotik', slug: 'probiyotik' },
            { name: '#doğal', slug: 'dogal' },
            { name: '#geleneksel', slug: 'geleneksel' }
        ];

        const tagsHtml = mockTags.map(tag => `
            <a href="#" class="tag-item" onclick="filterByTag('${tag.slug}'); return false;" title="${tag.name}">
                ${tag.name}
            </a>
        `).join('');

        container.innerHTML = tagsHtml;
    }
}

// Filter Functions
function filterByCategory(category) {

    currentCategory = category;
    currentPage = 1;
    currentSearch = ''; // Clear search when filtering by category

    // Clear search input
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
    }

    // Update active category button
    document.querySelectorAll('.category-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Find and activate the correct button
    let targetButton = null;

    if (category === 'all') {
        targetButton = document.querySelector('.category-btn[onclick="filterByCategory(\'all\')"]');
    } else {
        targetButton = document.querySelector(`.category-btn[onclick="filterByCategory('${category}')"]`);
    }

    if (targetButton) {
        targetButton.classList.add('active');
    } else {
        console.warn('Could not find button for category:', category);
        // Fallback: activate "Tümü" button
        const allButton = document.querySelector('.category-btn[onclick="filterByCategory(\'all\')"]');
        if (allButton) {
            allButton.classList.add('active');
        }
    }

    loadBlogPosts(currentPage, currentCategory, currentSearch);

    // Scroll to blog posts section (not to the very top)
    const blogPostsContainer = document.getElementById('blogPostsContainer');
    if (blogPostsContainer) {
        // Scroll to a bit above the blog posts container
        const containerTop = blogPostsContainer.offsetTop - 100; // 100px above the container
        window.scrollTo({
            top: Math.max(0, containerTop),
            behavior: 'smooth'
        });
    } else {
        // Fallback: scroll to a reasonable position (not the very top)
        window.scrollTo({ top: 300, behavior: 'smooth' });
    }
}

function searchPosts() {
    const searchInput = document.getElementById('searchInput');
    currentSearch = searchInput ? searchInput.value.trim() : '';
    currentPage = 1;

    loadBlogPosts(currentPage, currentCategory, currentSearch);

    // Blog posts bölümüne kaydır
    const blogPostsContainer = document.getElementById('blogPostsContainer');
    if (blogPostsContainer) {
        const containerTop = blogPostsContainer.offsetTop - 100;
        window.scrollTo({
            top: Math.max(0, containerTop),
            behavior: 'smooth'
        });
    } else {
        window.scrollTo({ top: 300, behavior: 'smooth' });
    }
}

function filterByTag(tagSlug) {

    // Tag slug'ından tag name'i bul ve search kutusuna yaz
    findTagNameAndSearch(tagSlug);
}

// Etiket filtreleme fonksiyonu
async function loadPostsByTag(tagSlug) {
    try {

        // Loading göster
        const container = document.getElementById('blogPostsContainer');
        if (container) {
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                    <p class="mt-3 text-muted">Etiket yazıları yükleniyor...</p>
                </div>
            `;
        }

        // Etiket API'sini çağır - cache bypass için timestamp ekle
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/blog/posts-by-tag.php?tag=${encodeURIComponent(tagSlug)}&page=1&limit=${postsPerPage}&_t=${timestamp}`);
        const data = await response.json();



        if (data.success && data.posts) {

            // Search kutusunu temizle
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
            }

            // Global state'i güncelle
            currentSearch = '';
            currentPage = 1;
            currentCategory = 'all';

            // Kategori butonlarını sıfırla
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // "Tümü" butonunu aktif yap
            const allButton = document.querySelector('[onclick="filterByCategory(\'all\')"]');
            if (allButton) {
                allButton.classList.add('active');
            }

            // Blog yazılarını göster
            displayBlogPosts(data.posts);

            // Pagination'ı ayrı olarak göster
            const paginationContainer = document.getElementById('paginationContainer');
            if (paginationContainer && data.pagination) {
                displayPagination(data.pagination, paginationContainer);
            }

            // Blog posts bölümüne kaydır
            const blogPostsContainer = document.getElementById('blogPostsContainer');
            if (blogPostsContainer) {
                const containerTop = blogPostsContainer.offsetTop - 100;
                window.scrollTo({
                    top: Math.max(0, containerTop),
                    behavior: 'smooth'
                });
            }

        } else {
            console.error('No posts found for tag:', tagSlug);

            // Boş sonuç göster
            const container = document.getElementById('blogPostsContainer');
            if (container) {
                container.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-tag fa-2x text-muted mb-3"></i>
                        <h5 class="text-muted">Bu etikete ait yazı bulunamadı</h5>
                        <p class="text-muted">Başka etiketleri deneyebilirsiniz.</p>
                    </div>
                `;
            }
        }

    } catch (error) {
        console.error('Error loading posts by tag:', error);

        // Hata durumunda fallback
        const container = document.getElementById('blogPostsContainer');
        if (container) {
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                    <h5 class="text-muted">Bir hata oluştu</h5>
                    <p class="text-muted">Lütfen sayfayı yenileyin ve tekrar deneyin.</p>
                </div>
            `;
        }
    }
}

// Tag slug'ından tag name'i bulup search yap (eski fonksiyon - fallback için)
async function findTagNameAndSearch(tagSlug) {
    try {
        // Yeni fonksiyonu kullan
        await loadPostsByTag(tagSlug);
    } catch (error) {
        console.error('Error finding tag name:', error);
        // Fallback: slug'ı direkt kullan
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '#' + tagSlug;
        }
        currentSearch = '#' + tagSlug;
        loadBlogPosts(1, 'all', currentSearch);
    }
}

function changePage(page) {
    currentPage = page;

    // Loading göster
    const container = document.getElementById('blogPostsContainer');
    if (container) {
        container.innerHTML = '<div class="col-12 text-center py-5"><i class="fas fa-spinner fa-spin fa-2x text-muted"></i><p class="mt-3 text-muted">Sayfa yükleniyor...</p></div>';
    }

    // Blog posts container'ına scroll yap
    const blogContainer = document.getElementById('blogPostsContainer');
    if (blogContainer) {
        blogContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    // Kısa bir gecikme ile yükle (kullanıcı deneyimi için)
    setTimeout(() => {
        loadBlogPosts(currentPage, currentCategory, currentSearch);
    }, 300);
}

// Newsletter Signup
async function handleNewsletterSignup(e) {
    e.preventDefault();
    
    const form = e.target;
    const email = form.querySelector('input[type="email"]').value;
    const button = form.querySelector('button');
    
    // Disable form
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Kaydediliyor...';
    
    try {
        const response = await fetch('/api/contact.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification('E-posta listemize başarıyla eklendi!', 'success');
            form.reset();
        } else {
            showNotification(data.message || 'Bir hata oluştu.', 'error');
        }
    } catch (error) {
        console.error('Newsletter signup error:', error);
        showNotification('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
    } finally {
        // Re-enable form
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-envelope me-2"></i>Abone Ol';
    }
}

// Utility Functions
function formatDate(dateString) {
    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: 'Europe/Istanbul'
    };
    return date.toLocaleDateString('tr-TR', options);
}

// XSS koruması için HTML escape
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Güvenli innerHTML alternatifi
function safeSetHTML(element, html) {
    // Basit HTML temizleme - sadece güvenli tagları bırak
    const cleanHtml = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                         .replace(/javascript:/gi, '')
                         .replace(/on\w+\s*=/gi, '');
    element.innerHTML = cleanHtml;
}

function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
    notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'} me-2"></i>
        ${message}
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 4000);
}

// Social Share Functions
function shareOnFacebook(url, title) {
    const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function shareOnTwitter(url, title) {
    const shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp(url, title) {
    const shareUrl = `https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`;
    window.open(shareUrl, '_blank');
}



// Initialize smooth scrolling for anchor links
document.addEventListener('click', (e) => {
    if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const href = e.target.getAttribute('href');
        // Sadece # olan veya boş olan href'leri atla
        if (href && href.length > 1) {
            const target = document.querySelector(href);
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        }
    }
});
