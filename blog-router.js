// Enhanced blog router - handles both server-side and client-side routing
(function() {

    // Only run client-side routing if we're on a pretty URL and no query parameters exist
    // This means server-side rewriting didn't work
    if (window.location.pathname.startsWith('/blog/') &&
        window.location.pathname !== '/blog.php' &&
        !window.location.search) {

        const slug = window.location.pathname.replace('/blog/', '').replace(/\/$/, ''); // Remove trailing slash

        if (slug && slug.length > 0) {
            // Redirect to blog-detail.php with slug parameter
            const newUrl = `/blog-detail.php?slug=${encodeURIComponent(slug)}`;

            // Use replace instead of assign to avoid back button issues
            window.location.replace(newUrl);
        }
    }
})();
