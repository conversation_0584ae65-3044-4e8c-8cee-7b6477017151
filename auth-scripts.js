document.addEventListener("DOMContentLoaded", () => {
  // Login Form Handler
  const loginForm = document.getElementById("loginForm")
  const loginMessageArea = document.getElementById("loginMessageArea")

  if (loginForm && loginMessageArea) {
    loginForm.addEventListener("submit", async (e) => {
      e.preventDefault()

      const email = document.getElementById("email").value.trim()
      const password = document.getElementById("password").value

      if (!email || !password) {
        showMessage(loginMessageArea, "Lütfen tüm alanları doldurun.", "error")
        return
      }

      try {
        console.log('Login API çağrısı başlıyor...');
        console.log('Email:', email);
        console.log('Password length:', password.length);
        
        const response = await fetch("/api/users/login.php", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email, password }),
        })

        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);
        console.log('Response headers:', response.headers);
        
        const result = await response.json()
        console.log('Response result:', result);

        if (result.success) {
          // Kullanıcı bilgilerini localStorage'a kaydet
          localStorage.setItem("userInfo", JSON.stringify(result.data))

          showMessage(loginMessageArea, "Giriş başarılı! Yönlendiriliyorsunuz...", "success")

          // Header'ı güncelle
          if (typeof updateHeaderUserInfo === 'function') {
            updateHeaderUserInfo()
          }

          setTimeout(() => {
            window.location.href = "/"
          }, 1500)
        } else {
          showMessage(loginMessageArea, result.message || "Giriş başarısız.", "error")
        }
      } catch (error) {
        showMessage(loginMessageArea, "Bağlantı hatası. Lütfen tekrar deneyin.", "error")
      }
    })
  }

  // Register Form Handler
  const registerForm = document.getElementById("registerForm")
  const registerMessageArea = document.getElementById("registerMessageArea")

  if (registerForm && registerMessageArea) {
    registerForm.addEventListener("submit", async (e) => {
      e.preventDefault()

      const name = document.getElementById("name").value.trim()
      const email = document.getElementById("email").value.trim()
      const password = document.getElementById("password").value
      const confirmPassword = document.getElementById("confirmPassword").value

      if (!name || !email || !password || !confirmPassword) {
        showMessage(registerMessageArea, "Lütfen tüm alanları doldurun.", "error")
        return
      }

      if (password !== confirmPassword) {
        showMessage(registerMessageArea, "Şifreler eşleşmiyor.", "error")
        return
      }

      if (password.length < 6) {
        showMessage(registerMessageArea, "Şifre en az 6 karakter olmalıdır.", "error")
        return
      }

      try {
        console.log('Register API çağrısı başlıyor...');
        console.log('Name:', name);
        console.log('Email:', email);
        console.log('Password length:', password.length);
        
        const response = await fetch("/api/users/register.php", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ name, email, password }),
        })

        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);
        console.log('Response headers:', response.headers);
        
        const result = await response.json()
        console.log('Response result:', result);

        if (result.success) {
          showMessage(registerMessageArea, result.message, "success")

          setTimeout(() => {
            window.location.href = "login.php"
          }, 2000)
        } else {
          showMessage(registerMessageArea, result.message || "Kayıt başarısız.", "error")
        }
      } catch (error) {
        console.error('Register API hatası:', error);
        showMessage(registerMessageArea, "Bağlantı hatası. Lütfen tekrar deneyin.", "error")
      }
    })
  }

  function showMessage(messageArea, message, type) {
    messageArea.textContent = message
    messageArea.className = `message-area ${type === "success" ? "message-success" : "message-error"}`
    messageArea.style.display = "block"

    setTimeout(() => {
      messageArea.style.display = "none"
    }, 5000)
  }
})
