# Tansu Şahal <PERSON>amura - Git Ignore File

# Development ve Test Dosyaları
*-test.php
test-*.php
debug-*.php
seo-test.php
xss-test.php
quick-*.php

# Log Dosyaları
logs/
*.log
error.log
php_error.log

# Geçici Dosyalar
*.tmp
*.temp
*.bak
*.old
*.swp
*~

# IDE ve Editor Dosyaları
.vscode/
.idea/
*.sublime-*
.DS_Store
Thumbs.db

# Database Dosyaları
*.sql
*.db
*.sqlite
database/

# Upload Klasörü İçeriği (Dosyalar hariç klasör yapısı)
uploads/*
!uploads/.gitkeep
!uploads/products/.gitkeep
!uploads/blog/.gitkeep
!uploads/videos/.gitkeep

# Config Dosyaları (Hassas bilgiler)
config/local.php
config/production.php
.env
.env.local
.env.production

# Cache Dosyaları
cache/
*.cache

# Backup Dosyaları
backup/
*.backup

# OS Dosyaları
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Güvenlik Test Dosyaları
security-test.php
penetration-test.php

# Production'da Olmaması Gerekenler
TODO.md
NOTES.md
development.md

# Node.js (Legacy - artık kullanılmıyor)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock