document.addEventListener("DOMContentLoaded", async () => {
  const urlParams = new URLSearchParams(window.location.search)
  const productId = urlParams.get("id")

  console.log("Product ID from URL:", productId)

  if (!productId) {
    showError("Ürün ID bulunamadı")
    return
  }

  await loadProductDetail(productId)
  updateFavoritesCount()
})

let currentProduct = null
let currentImageIndex = 0
let productImages = []

async function loadProductDetail(productId) {
  const loadingIndicator = document.getElementById("loadingIndicator")
  const productDetailContent = document.getElementById("product-detail-content")

  console.log("Loading product detail for ID:", productId)

  try {
    const response = await fetch(`api/products/detail?id=${productId}`)
    console.log("Response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error("API Error:", errorText)
      throw new Error(`HTTP ${response.status}: ${errorText}`)
    }

    const product = await response.json()
    console.log("Product data received:", product)

    currentProduct = product

    // Prepare images array
    if (Array.isArray(product.images) && product.images.length > 0) {
      productImages = product.images
    } else if (product.image) {
      productImages = [product.image]
    } else {
      productImages = ["/images/default-product.png"]
    }

    if (loadingIndicator) {
      loadingIndicator.style.display = "none"
    }

    displayProductDetail(product)
    initializeGallery()
  } catch (error) {
    console.error("Product detail error:", error)
    if (loadingIndicator) {
      loadingIndicator.style.display = "none"
    }
    showError("Ürün yüklenirken hata oluştu: " + error.message)
  }
}

function displayProductDetail(product) {
  const productDetailContent = document.getElementById("product-detail-content")
  if (!productDetailContent) {
    console.error("product-detail-content element not found")
    return
  }

  console.log("Displaying product:", product)

  const stockInfo =
    product.stock > 0
      ? `<div class="alert alert-success d-flex align-items-center mb-2 py-2">
             <i class="fas fa-check-circle me-2"></i>
             <span><strong>Stokta var</strong> - ${product.stock} adet</span>
         </div>`
      : `<div class="alert alert-danger d-flex align-items-center mb-2 py-2">
             <i class="fas fa-exclamation-circle me-2"></i>
             <span><strong>Stok tükendi</strong></span>
         </div>`

  // Favoriler kontrolü
  const favorites = getFavorites()
  const isFavorited = favorites.some((item) => String(item._id) === String(product._id))
  const heartIcon = isFavorited ? "fas fa-heart" : "far fa-heart"
  const heartButtonClass = isFavorited ? "btn-danger" : "btn-outline-danger"
  const heartTitle = isFavorited ? "Favorilerden Kaldır" : "Favorilere Ekle"

  // Video section - daha kompakt
  let videoSection = ""
  if (product.video_url) {
    let embedUrl = ""

    if (product.video_url.includes("youtube.com") || product.video_url.includes("youtu.be")) {
      const videoId = extractYouTubeVideoId(product.video_url)
      if (videoId) {
        embedUrl = `https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1`
      }
    } else if (product.video_url.includes("vimeo.com")) {
      const vimeoId = product.video_url.split("/").pop()
      embedUrl = `https://player.vimeo.com/video/${vimeoId}`
    } else if (
      product.video_url.endsWith(".mp4") ||
      product.video_url.endsWith(".webm") ||
      product.video_url.endsWith(".ogg")
    ) {
      videoSection = `
        <div class="col-12 mt-3">
          <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white py-2">
              <h6 class="mb-0"><i class="fas fa-play-circle me-2"></i>Ürün Videosu</h6>
            </div>
            <div class="card-body p-0">
              <video controls class="w-100" style="max-height: 350px;">
                <source src="${product.video_url}" type="video/mp4">
                Tarayıcınız video oynatmayı desteklemiyor.
              </video>
            </div>
          </div>
        </div>
      `
    }

    if (embedUrl && !videoSection) {
      videoSection = `
        <div class="col-12 mt-3">
          <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white py-2">
              <h6 class="mb-0"><i class="fas fa-play-circle me-2"></i>Ürün Videosu</h6>
            </div>
            <div class="card-body p-0">
              <div class="ratio ratio-16x9">
                <iframe src="${embedUrl}" 
                        title="Ürün Videosu" 
                        allowfullscreen
                        frameborder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
                </iframe>
              </div>
            </div>
          </div>
        </div>
      `
    }
  }

  // Create professional image gallery
  const galleryHtml = createImageGallery(productImages, product.name)

  productDetailContent.innerHTML = `
<!-- Breadcrumb - En üstte -->
<div class="col-12">
  <nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb mb-0 bg-light p-2 rounded">
      <li class="breadcrumb-item"><a href="/" class="text-decoration-none small">Ana Sayfa</a></li>
      <li class="breadcrumb-item"><a href="/#products" class="text-decoration-none small">Ürünler</a></li>
      <li class="breadcrumb-item active small" aria-current="page">${product.name}</li>
    </ol>
  </nav>
</div>

<!-- Ürün başlığı - Breadcrumb'ın hemen altında -->
<div class="col-12 mb-3">
  <h1 class="product-title mb-2">${product.name}</h1>
  ${product.category ? `<p class="text-muted mb-0"><i class="fas fa-tag me-2"></i>${product.category}</p>` : ""}
</div>

<div class="col-lg-6 col-md-6 col-12">
  ${galleryHtml}
  
  <!-- Butonları resmin altına taşı - mobil için özel class -->
  <div class="row g-2 mobile-action-buttons">
    <div class="col-4">
      <button class="btn btn-outline-primary btn-sm w-100" onclick="shareProduct()" title="Paylaş">
        <i class="fas fa-share-alt"></i>
      </button>
    </div>
    <div class="col-4">
      <button class="btn ${heartButtonClass} btn-sm w-100 favorite-btn-detail" 
              title="${heartTitle}"
              onclick="toggleFavoriteDetail('${product._id}')">
        <i class="${heartIcon}"></i>
      </button>
    </div>
    <div class="col-4">
      <a href="/" class="btn btn-outline-secondary btn-sm w-100" title="Ana Sayfaya Dön">
        <i class="fas fa-arrow-left"></i>
      </a>
    </div>
  </div>

  <!-- Hızlı Bilgi bölümü - Mobilde göster -->
  <div class="mobile-quick-info d-lg-none">
    <div class="alert alert-info py-2 mt-3">
      <h6 class="alert-heading mb-1"><i class="fas fa-info-circle me-2"></i>Hızlı Bilgi</h6>
      <div class="row g-2 small">
        <div class="col-6">🚛 Ücretsiz kargo</div>
        <div class="col-6">💳 Kapıda ödeme</div>
        <div class="col-6">🌿 %100 doğal</div>
        <div class="col-6">📞 Destek hattı</div>
      </div>
    </div>
  </div>
</div>

<div class="col-lg-6 col-md-6 col-12">
  <div class="ps-lg-3">
    <!-- Fiyat ve Stok Bilgisi -->
    <div class="product-info-grid mb-3">
      <div class="info-card">
        <div class="info-card-icon">💰</div>
        <p class="info-card-title">Fiyat</p>
        <p class="info-card-value">${(product.price / 100).toFixed(2)} TL</p>
      </div>
      <div class="info-card">
        <div class="info-card-icon">${product.stock > 0 ? "✅" : "❌"}</div>
        <p class="info-card-title">Stok</p>
        <p class="info-card-value">${product.stock > 0 ? `${product.stock} adet` : "Tükendi"}</p>
      </div>
    </div>

    <!-- Hızlı Bilgi - Desktop'ta göster -->
    <div class="d-none d-lg-block mb-3">
      <div class="alert alert-info py-2">
        <h6 class="alert-heading mb-1"><i class="fas fa-info-circle me-2"></i>Hızlı Bilgi</h6>
        <div class="row g-2 small">
          <div class="col-6">🚛 Ücretsiz kargo</div>
          <div class="col-6">💳 Kapıda ödeme</div>
          <div class="col-6">🌿 %100 doğal</div>
          <div class="col-6">📞 Destek hattı</div>
        </div>
      </div>
    </div>
    
    <!-- Ürün Açıklaması -->
    <div class="mb-3">
      <h6 class="fw-semibold mb-2">📝 Ürün Açıklaması</h6>
      <div class="product-description-compact">
        <pre style="white-space: pre-wrap; font-family: inherit; margin: 0; font-size: inherit; line-height: inherit;">${product.description || "Açıklama bulunmuyor."}</pre>
      </div>
    </div>

    <!-- Satın Alma Butonu -->
    <div class="d-grid gap-2 mb-3">
      <a href="${product.purchase_link || "https://www.shopier.com/tansusahalsalamura"}" 
         target="_blank" 
         class="btn-trendyol">
        <i class="fas fa-shopping-cart"></i>
        Shopier'da Satın Al
      </a>
    </div>
  </div>
</div>

${videoSection}
`

  // Sayfa başlığını güncelle
  document.title = `${product.name} - Tansu Şahal Salamura`
}

function createImageGallery(images, productName) {
  if (!images || images.length === 0) {
    images = ["/images/default-product.png"]
  }

  const mainImage = images[0]

  let thumbnailsHtml = ""
  if (images.length > 1) {
    thumbnailsHtml = images
      .map(
        (img, index) => `
      <div class="thumbnail-item ${index === 0 ? "active" : ""}" onclick="changeMainImage(${index})">
        <img src="${img}" alt="${productName} - Görsel ${index + 1}">
      </div>
    `,
      )
      .join("")
  }

  return `
  <div class="product-gallery">
    <div class="main-image-container">
      ${
        images.length > 1
          ? `
        <button class="gallery-nav-btn prev" onclick="previousMainImage()">
          <i class="fas fa-chevron-left"></i>
        </button>
        <button class="gallery-nav-btn next" onclick="nextMainImage()">
          <i class="fas fa-chevron-right"></i>
        </button>
      `
          : ""
      }
      
      <div class="image-counter">${currentImageIndex + 1} / ${images.length}</div>
      <button class="fullscreen-btn" onclick="openLightbox(${currentImageIndex})">
        <i class="fas fa-expand"></i>
      </button>
      
      <img id="mainProductImage" 
           src="${mainImage}" 
           alt="${productName}" 
           class="main-product-image"
           onclick="openLightbox(${currentImageIndex})">
    </div>
    
    ${
      images.length > 1
        ? `
      <div class="thumbnail-container">
        ${thumbnailsHtml}
      </div>
    `
        : ""
    }
  </div>
`
}

function initializeGallery() {
  currentImageIndex = 0
  updateImageCounter()
}

function changeMainImage(index) {
  if (index < 0 || index >= productImages.length) return

  console.log('changeMainImage called with index:', index)
  currentImageIndex = index
  const mainImage = document.getElementById("mainProductImage")
  if (mainImage) {
    mainImage.src = productImages[index]
    console.log('Updated main image src to:', productImages[index])
    console.log('Updated currentImageIndex to:', currentImageIndex)
    
    // Ana resmin onclick event'ini güncelle
    mainImage.onclick = () => {
      console.log('Main image clicked, opening lightbox with index:', index)
      openLightbox(index)
    }
    
    // Mobil için touch event'i de ekle
    mainImage.ontouchstart = (e) => {
      e.preventDefault()
      console.log('Main image touched, opening lightbox with index:', index)
      openLightbox(index)
    }
  }

  // Fullscreen button'ın onclick event'ini de güncelle
  const fullscreenBtn = document.querySelector(".fullscreen-btn")
  if (fullscreenBtn) {
    fullscreenBtn.onclick = () => {
      console.log('Fullscreen button clicked, opening lightbox with index:', index)
      openLightbox(index)
    }
    
    // Mobil için touch event'i de ekle
    fullscreenBtn.ontouchstart = (e) => {
      e.preventDefault()
      console.log('Fullscreen button touched, opening lightbox with index:', index)
      openLightbox(index)
    }
  }

  // Update active thumbnail
  document.querySelectorAll(".thumbnail-item").forEach((thumb, i) => {
    thumb.classList.toggle("active", i === index)
  })

  updateImageCounter()
}

function previousMainImage() {
  const newIndex = currentImageIndex > 0 ? currentImageIndex - 1 : productImages.length - 1
  changeMainImage(newIndex)
}

function nextMainImage() {
  const newIndex = currentImageIndex < productImages.length - 1 ? currentImageIndex + 1 : 0
  changeMainImage(newIndex)
}

// Lightbox functionality
function openLightbox(index) {
  console.log('openLightbox called with index:', index)
  console.log('productImages array:', productImages)
  
  const modal = document.getElementById("lightboxModal")
  const lightboxImage = document.getElementById("lightboxImage")

  if (modal && lightboxImage) {
    currentImageIndex = index
    lightboxImage.src = productImages[index]
    console.log('Lightbox opened with image:', productImages[index])
    
    modal.classList.add("active")
    document.body.style.overflow = "hidden"
    
    // Lightbox modal'a click event listener ekle (arka plana tıklayınca kapat)
    setupLightboxClickToClose(modal)
  }
}

function closeLightbox() {
  const modal = document.getElementById("lightboxModal")
  if (modal) {
    modal.classList.remove("active")
    document.body.style.overflow = "auto"
    
    // Event listener'ları temizle
    modal.removeEventListener('click', handleLightboxClick)
    modal.removeEventListener('touchstart', handleLightboxTouch)
  }
}

// Lightbox arka plana tıklayınca kapatma
function setupLightboxClickToClose(modal) {
  // Önceki event listener'ları temizle
  modal.removeEventListener('click', handleLightboxClick)
  modal.removeEventListener('touchstart', handleLightboxTouch)
  
  // Yeni event listener'lar ekle (hem click hem touch)
  modal.addEventListener('click', handleLightboxClick)
  modal.addEventListener('touchstart', handleLightboxTouch)
}

function handleLightboxClick(e) {
  const lightboxContent = document.querySelector('.lightbox-content')
  
  console.log('Lightbox clicked, target:', e.target)
  console.log('Is inside content:', lightboxContent.contains(e.target))
  
  // Eğer tıklanan element lightbox content'in içinde değilse (yani arka plana tıklandıysa)
  if (!lightboxContent.contains(e.target)) {
    console.log('Closing lightbox - clicked outside content')
    closeLightbox()
  }
}

function handleLightboxTouch(e) {
  const lightboxContent = document.querySelector('.lightbox-content')
  
  console.log('Lightbox touched, target:', e.target)
  console.log('Is inside content:', lightboxContent.contains(e.target))
  
  // Eğer dokunulan element lightbox content'in içinde değilse (yani arka plana dokunulduysa)
  if (!lightboxContent.contains(e.target)) {
    console.log('Closing lightbox - touched outside content')
    e.preventDefault()
    closeLightbox()
  }
}

function previousImage() {
  const newIndex = currentImageIndex > 0 ? currentImageIndex - 1 : productImages.length - 1
  currentImageIndex = newIndex
  const lightboxImage = document.getElementById("lightboxImage")
  if (lightboxImage) {
    lightboxImage.src = productImages[newIndex]
  }
  changeMainImage(newIndex)
}

function nextImage() {
  const newIndex = currentImageIndex < productImages.length - 1 ? currentImageIndex + 1 : 0
  currentImageIndex = newIndex
  const lightboxImage = document.getElementById("lightboxImage")
  if (lightboxImage) {
    lightboxImage.src = productImages[newIndex]
  }
  changeMainImage(newIndex)
}

// Keyboard navigation for lightbox
document.addEventListener("keydown", (e) => {
  const modal = document.getElementById("lightboxModal")
  if (modal && modal.classList.contains("active")) {
    if (e.key === "Escape") {
      closeLightbox()
    } else if (e.key === "ArrowLeft") {
      previousImage()
    } else if (e.key === "ArrowRight") {
      nextImage()
    }
  }
})

function extractYouTubeVideoId(url) {
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
  const match = url.match(regExp)

  if (match && match[2].length === 11) {
    return match[2]
  }

  const urlParams = new URLSearchParams(new URL(url).search)
  return urlParams.get("v")
}

function toggleFavoriteDetail(productId) {
  const userInfo = getUserInfo()
  if (!userInfo || !userInfo.token) {
    alert("Favorilere eklemek için giriş yapmanız gerekmektedir.")
    window.location.href = "login.php"
    return
  }

  const favorites = getFavorites()
  const isCurrentlyFavorited = favorites.some((item) => String(item._id) === String(productId))

  if (isCurrentlyFavorited) {
    removeFromFavorites(productId)
    updateFavoriteButton(productId, false)
    showNotification("Ürün favorilerden kaldırıldı", "success")
  } else {
    addToFavorites(currentProduct)
    updateFavoriteButton(productId, true)
    showNotification("Ürün favorilere eklendi", "success")
  }

  updateFavoritesCount()
}

function updateFavoriteButton(productId, isFavorited) {
  const favoriteBtn = document.querySelector(".favorite-btn-detail")
  if (!favoriteBtn) return

  const heartIcon = isFavorited ? "fas fa-heart" : "far fa-heart"
  const heartButtonClass = isFavorited ? "btn-danger" : "btn-outline-danger"
  const heartTitle = isFavorited ? "Favorilerden Kaldır" : "Favorilere Ekle"

  // Tüm cihazlarda aynı boyut - sadece ikon
  favoriteBtn.className = `btn ${heartButtonClass} btn-sm w-100 favorite-btn-detail`
  favoriteBtn.innerHTML = `<i class="${heartIcon}"></i>`
  favoriteBtn.title = heartTitle
}

function shareProduct() {
  if (navigator.share) {
    navigator.share({
      title: document.title,
      url: window.location.href,
    })
  } else {
    navigator.clipboard.writeText(window.location.href).then(() => {
      showNotification("Ürün linki kopyalandı!", "success")
    })
  }
}

function showError(message) {
  const productDetailContent = document.getElementById("product-detail-content")
  if (productDetailContent) {
    productDetailContent.innerHTML = `
      <div class="col-12">
        <div class="alert alert-danger text-center py-5">
          <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
          <h4>Hata</h4>
          <p>${message}</p>
          <a href="/" class="btn btn-primary">Ana Sayfaya Dön</a>
        </div>
      </div>
    `
  }
}

function showNotification(message, type = "success") {
  const notification = document.createElement("div")
  notification.className = `alert alert-${type === "success" ? "success" : "danger"} position-fixed`
  notification.style.cssText = "top: 20px; right: 20px; z-index: 9999; min-width: 300px;"
  notification.innerHTML = `
    <i class="fas ${type === "success" ? "fa-check-circle" : "fa-exclamation-triangle"} me-2"></i>
    ${message}
  `

  document.body.appendChild(notification)

  setTimeout(() => {
    notification.remove()
  }, 3000)
}

// Utility functions
function getUserInfo() {
  const userInfoString = localStorage.getItem("userInfo")
  try {
    return userInfoString ? JSON.parse(userInfoString) : null
  } catch (e) {
    localStorage.removeItem("userInfo")
    return null
  }
}

function getFavorites() {
  const favoritesString = localStorage.getItem("userFavorites")
  try {
    const favorites = favoritesString ? JSON.parse(favoritesString) : []
    return Array.isArray(favorites) ? favorites : []
  } catch (e) {
    return []
  }
}

function isInFavorites(productId) {
  const favorites = getFavorites()
  return favorites.some((item) => String(item._id) === String(productId))
}

function addToFavorites(product) {
  const favorites = getFavorites()
  if (!isInFavorites(product._id)) {
    favorites.push(product)
    localStorage.setItem("userFavorites", JSON.stringify(favorites))
    return true
  }
  return false
}

function removeFromFavorites(productId) {
  let favorites = getFavorites()
  favorites = favorites.filter((item) => String(item._id) !== String(productId))
  localStorage.setItem("userFavorites", JSON.stringify(favorites))
}

function updateFavoritesCount() {
  const favorites = getFavorites()
  const totalItems = favorites.length
  const favoritesCountElement = document.getElementById("favorites-item-count")
  if (favoritesCountElement) {
    favoritesCountElement.textContent = totalItems
  }
}

function updateImageCounter() {
  const counter = document.querySelector(".image-counter")
  if (counter) {
    counter.textContent = `${currentImageIndex + 1} / ${productImages.length}`
  }
}
