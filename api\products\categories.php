<?php
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';

setCorsHeaders();

$db = new Database();
$conn = $db->getConnection();

if (!$conn) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Veritabanı bağlantısı kurulamadı.']);
    exit;
}

try {
    // Kategorileri ve her kategorideki ürün sayısını al
    $stmt = $conn->prepare("
        SELECT 
            category, 
            COUNT(*) as count 
        FROM products 
        WHERE category IS NOT NULL AND category != '' 
        GROUP BY category 
        ORDER BY category
    ");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Toplam ürün sayısını al
    $totalStmt = $conn->prepare("SELECT COUNT(*) as total FROM products");
    $totalStmt->execute();
    $total = $totalStmt->fetch(PDO::FETCH_ASSOC)['total'];

    $result = [
        'categories' => $categories,
        'total' => $total
    ];

    header('Content-Type: application/json');
    echo json_encode($result);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Kategoriler alınamadı.', 'error' => $e->getMessage()]);
}
?>
