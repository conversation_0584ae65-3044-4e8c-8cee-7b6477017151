<?php
// <PERSON>ache kontrol header'ları - API her zaman güncel olsun
header("Cache-Control: no-cache, no-store, must-revalidate, max-age=0");
header("Pragma: no-cache");
header("Expires: Thu, 01 Jan 1970 00:00:00 GMT");

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

require_once '../../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $limit = isset($_GET['limit']) ? min(10, max(1, intval($_GET['limit']))) : 5;
    
    $sql = "SELECT 
                bp.id,
                bp.title,
                bp.slug,
                bp.featured_image,
                bp.view_count,
                bp.published_at,
                bc.name as category_name,
                bc.slug as category_slug
            FROM blog_posts bp
            LEFT JOIN blog_categories bc ON bp.category_id = bc.id
            WHERE bp.status = 'published'
            ORDER BY bp.view_count DESC, bp.published_at DESC
            LIMIT :limit";
    
    $stmt = $db->prepare($sql);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process posts
    foreach ($posts as &$post) {
        // Format date
        $post['published_at_formatted'] = date('d F Y', strtotime($post['published_at']));
        
        // Generate full URL for featured image
        if ($post['featured_image'] && strpos($post['featured_image'], 'http') !== 0) {
            $post['featured_image'] = '/' . ltrim($post['featured_image'], '/');
        }
    }
    
    echo json_encode([
        'success' => true,
        'posts' => $posts
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("Popular posts API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Sunucu hatası oluştu.'
    ], JSON_UNESCAPED_UNICODE);
}
?>
