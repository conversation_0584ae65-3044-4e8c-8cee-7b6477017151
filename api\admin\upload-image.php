<?php
header('Content-Type: application/json');

// Yet<PERSON> kontrol<PERSON> ekleyin (örneğin JWT veya session ile)
// ...yetki kontrolü...

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Yalnızca POST isteği kabul edilir.']);
    exit;
}

$uploadDir = __DIR__ . '/../../uploads/products/';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0777, true);
}

$allowed = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

// Güvenlik fonksiyonları
function isValidImageFile($tmpName, $fileName) {
    // MIME type kontrolü
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $tmpName);
    finfo_close($finfo);

    $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($mimeType, $allowedMimes)) {
        return false;
    }

    // Dosya imzası kontrolü (magic bytes)
    $handle = fopen($tmpName, 'rb');
    $header = fread($handle, 10);
    fclose($handle);

    // JPEG: FF D8 FF
    // PNG: 89 50 4E 47
    // GIF: 47 49 46 38
    $validHeaders = [
        "\xFF\xD8\xFF", // JPEG
        "\x89\x50\x4E\x47", // PNG
        "\x47\x49\x46\x38", // GIF
    ];

    $isValid = false;
    foreach ($validHeaders as $validHeader) {
        if (strpos($header, $validHeader) === 0) {
            $isValid = true;
            break;
        }
    }

    // WebP kontrolü
    if (!$isValid && strpos($header, 'WEBP') !== false) {
        $isValid = true;
    }

    return $isValid;
}

function sanitizeFileName($fileName) {
    // Tehlikeli karakterleri kaldır
    $fileName = preg_replace('/[^a-zA-Z0-9._-]/', '', $fileName);
    // Çoklu nokta ve tire temizle
    $fileName = preg_replace('/\.+/', '.', $fileName);
    $fileName = preg_replace('/-+/', '-', $fileName);
    return $fileName;
}
// URL'leri relatif path olarak oluştur (localhost/production uyumlu)
// $baseUrl artık gerekli değil - relatif path kullanacağız

// Çoklu dosya yükleme desteği
if (isset($_FILES['images']) && is_array($_FILES['images']['name'])) {
    // Çoklu dosya yükleme
    $uploadedImages = [];
    $fileCount = count($_FILES['images']['name']);

    for ($i = 0; $i < $fileCount; $i++) {
        if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
            $fileName = $_FILES['images']['name'][$i];
            $tmpName = $_FILES['images']['tmp_name'][$i];
            $fileSize = $_FILES['images']['size'][$i];

            // Dosya boyutu kontrolü (5MB limit)
            if ($fileSize > 5 * 1024 * 1024) {
                continue; // Bu dosyayı atla
            }

            $ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

            if (in_array($ext, $allowed)) {
                $newFileName = uniqid('img_') . '.' . $ext;
                $targetPath = $uploadDir . $newFileName;

                if (move_uploaded_file($tmpName, $targetPath)) {
                    // Relatif path kullan - kök dizininden başlayarak
                    $imageUrl = '/uploads/products/' . $newFileName;
                    $uploadedImages[] = $imageUrl;
                }
            }
        }
    }
    
    if (!empty($uploadedImages)) {
        echo json_encode(['success' => true, 'data' => ['imageUrls' => $uploadedImages]]);
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Hiçbir dosya yüklenemedi.']);
    }
    
} elseif (isset($_FILES['image'])) {
    // Tekli dosya yükleme (geriye dönük uyumluluk)
    $file = $_FILES['image'];
    $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($ext, $allowed)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Geçersiz dosya türü.']);
        exit;
    }
    
    // Dosya boyutu kontrolü (5MB limit)
    if ($file['size'] > 5 * 1024 * 1024) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Dosya boyutu çok büyük (max 5MB).']);
        exit;
    }
    
    $newFileName = uniqid('img_') . '.' . $ext;
    $targetPath = $uploadDir . $newFileName;
    
    if (move_uploaded_file($file['tmp_name'], $targetPath)) {
        // Relatif path kullan - kök dizininden başlayarak
        $imageUrl = '/uploads/products/' . $newFileName;

        // Log ekleyerek dosya yolunu ve URL'yi kontrol ediyoruz
        error_log("Upload Directory: " . $uploadDir);
        error_log("Target Path: " . $targetPath);
        error_log("Image URL: " . $imageUrl);

        echo json_encode(['success' => true, 'data' => ['imageUrl' => $imageUrl]]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Yükleme başarısız.']);
    }
    
} else {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Dosya bulunamadı.']);
}
?>
