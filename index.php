<?php
// Cache kontrol header'ları - Ana sayfa güncel olsun
header("Cache-Control: no-cache, no-store, must-revalidate, max-age=0");
header("Pragma: no-cache");
header("Expires: Thu, 01 Jan 1970 00:00:00 GMT");

// Cache buster fonksiyonları
require_once 'includes/cache-buster.php';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- SEO Meta Tags -->
    <title>Tansu Şahal Salamura - Doğal Ev Yapımı Turşu ve Salamura Ürünleri | Alaşehir</title>
    <meta name="description" content="Alaşehir'in en kaliteli doğal salamura, turşu ve zeytinyağı ürünleri. %100 doğal, kimyasal katkısız, geleneksel yöntemlerle üretilen ev yapımı lezzetler. Tüm Türkiye kargo.">
    <meta name="keywords" content="salamura, turşu, zeytin<PERSON>ı, doğal, geleneksel, Alaşehir, Manisa, ev yapımı, kimyasal katkısız, organik, fermente, konserve, sebze turşusu, meyve turşusu">
    <meta name="author" content="Tansu Şahal Salamura">
    <meta name="robots" content="index, follow">
    <meta name="language" content="Turkish">
    <meta name="revisit-after" content="7 days">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Tansu Şahal Salamura - Doğal Turşu ve Salamura Ürünleri">
    <meta property="og:description" content="Alaşehir'in en kaliteli doğal salamura, turşu ve zeytinyağı ürünleri. %100 doğal, kimyasal katkısız lezzetler.">
    <meta property="og:image" content="https://tansusahalsalamura.com.tr/images/logo.jpg">
    <meta property="og:url" content="https://tansusahalsalamura.com.tr">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="tr_TR">
    <meta property="og:site_name" content="Tansu Şahal Salamura">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Tansu Şahal Salamura - Doğal Turşu ve Salamura Ürünleri">
    <meta name="twitter:description" content="Alaşehir'in en kaliteli doğal salamura, turşu ve zeytinyağı ürünleri. %100 doğal, kimyasal katkısız lezzetler.">
    <meta name="twitter:image" content="https://tansusahalsalamura.com.tr/images/logo.jpg">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://tansusahalsalamura.com.tr/">

    <!-- Logo için özel meta taglar -->
    <meta property="og:logo" content="https://tansusahalsalamura.com.tr/images/logo.jpg">
    <meta name="msapplication-square150x150logo" content="https://tansusahalsalamura.com.tr/images/logo.jpg">
    <link rel="image_src" href="https://tansusahalsalamura.com.tr/images/logo.jpg">

    <!-- Favicon ve Icon Tanımlamaları -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
    
    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/images/apple-icon.png">
    <link rel="apple-touch-icon" sizes="57x57" href="/images/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/images/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/images/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/images/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/images/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/images/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/images/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/images/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-icon-180x180.png">
    
    <!-- Android Chrome Icons -->
    <link rel="icon" type="image/png" sizes="36x36" href="/images/android-icon-36x36.png">
    <link rel="icon" type="image/png" sizes="48x48" href="/images/android-icon-48x48.png">
    <link rel="icon" type="image/png" sizes="72x72" href="/images/android-icon-72x72.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/images/android-icon-96x96.png">
    <link rel="icon" type="image/png" sizes="144x144" href="/images/android-icon-144x144.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/images/android-icon-192x192.png">
    
    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#2c5530">
    <meta name="msapplication-TileImage" content="/images/android-icon-144x144.png">
    <meta name="msapplication-config" content="/images/browserconfig.xml">
    
    <!-- Web App Manifest -->
    <link rel="manifest" href="/images/manifest.json">
    
    <!-- Tema Rengi -->
    <meta name="theme-color" content="#2c5530">

    <!-- Safari Pinned Tab -->
    <link rel="mask-icon" href="/images/safari-pinned-tab.svg" color="#2c5530">
    
    <!-- Shortcut Icon -->
    <link rel="shortcut icon" href="/images/favicon.ico">
    
    <!-- Precomposed Apple Icon -->
    <link rel="apple-touch-icon-precomposed" href="/images/apple-icon-precomposed.png">
    
    <!-- Preconnect for Performance -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo getCSSLink(); ?>">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "Tansu Şahal Salamura",
        "description": "Alaşehir'in en kaliteli doğal salamura, turşu ve zeytinyağı ürünleri. %100 doğal, kimyasal katkısız lezzetler.",
        "url": "https://tansusahalsalamura.com.tr",
        "telephone": ["+90 (536) 035 92 20", "+90 (536) 705 58 69"],
        "email": "<EMAIL>",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "Hacıbey, Eski İzmir Cd. no69",
            "addressLocality": "Alaşehir",
            "addressRegion": "Manisa",
            "postalCode": "45600",
            "addressCountry": "TR"
        },
        "geo": {
            "@type": "GeoCoordinates",
            "latitude": "38.3500",
            "longitude": "28.5167"
        },
        "openingHours": "Mo-Sa 08:00-18:00",
        "priceRange": "₺₺",
        "servesCuisine": "Turkish",
        "hasMenu": "https://tansusahalsalamura.com.tr/#products",
        "image": "https://tansusahalsalamura.com.tr/images/logo.jpg",
        "logo": "https://tansusahalsalamura.com.tr/images/logo.jpg",
        "sameAs": [
            "https://www.facebook.com/tansusahalsalamura",
            "https://www.instagram.com/tansusahalsalamura"
        ]
    }
    </script>

    <!-- Organization Schema for Logo -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Tansu Şahal Salamura",
        "url": "https://tansusahalsalamura.com.tr",
        "logo": {
            "@type": "ImageObject",
            "url": "https://tansusahalsalamura.com.tr/images/logo.jpg",
            "width": "400",
            "height": "400",
            "caption": "Tansu Şahal Salamura Logo"
        },
        "image": "https://tansusahalsalamura.com.tr/images/logo.jpg",
        "description": "Doğal salamura, turşu ve zeytinyağı üreticisi. Alaşehir'den tüm Türkiye'ye kaliteli, kimyasal katkısız ürünler.",
        "foundingDate": "2020",
        "founder": {
            "@type": "Person",
            "name": "Tansu Şahal"
        },
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "Hacıbey, Eski İzmir Cd. no69",
            "addressLocality": "Alaşehir",
            "addressRegion": "Manisa",
            "postalCode": "45600",
            "addressCountry": "TR"
        },
        "contactPoint": [
            {
                "@type": "ContactPoint",
                "telephone": "+90 (536) 035 92 20",
                "contactType": "customer service",
                "availableLanguage": "Turkish"
            },
            {
                "@type": "ContactPoint",
                "telephone": "+90 (536) 705 58 69",
                "contactType": "customer service",
                "availableLanguage": "Turkish"
            }
        ],
        "sameAs": [
            "https://www.facebook.com/tansusahalsalamura",
            "https://www.instagram.com/tansusahalsalamura"
        ]
    }
    </script>

    <!-- Product Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Product",
        "@id": "https://tansusahalsalamura.com.tr/#products",
        "name": "Doğal Salamura ve Turşu Ürünleri",
        "description": "Geleneksel yöntemlerle üretilen %100 doğal salamura, turşu ve zeytinyağı ürünleri",
        "brand": {
            "@type": "Brand",
            "name": "Tansu Şahal Salamura",
            "logo": "https://tansusahalsalamura.com.tr/images/logo.jpg"
        },
        "manufacturer": {
            "@type": "Organization",
            "name": "Tansu Şahal Salamura"
        },
        "category": "Gıda",
        "offers": {
            "@type": "AggregateOffer",
            "priceCurrency": "TRY",
            "availability": "https://schema.org/InStock",
            "seller": {
                "@type": "Organization",
                "name": "Tansu Şahal Salamura"
            }
        }
    }
    </script>
</head>
<body>
    <!-- Hata mesajları -->
    <?php if (isset($_GET['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Erişim Engellendi!</strong>
        <?php
        switch($_GET['error']) {
            case 'no_session':
                echo 'Giriş yapmadan admin paneline erişemezsiniz. Lütfen önce giriş yapın.';
                break;
            case 'user_not_found':
                echo 'Kullanıcı hesabınız bulunamadı. Lütfen tekrar giriş yapın.';
                break;
            case 'not_admin':
                $role = $_GET['role'] ?? 'bilinmiyor';
                echo "Admin paneline erişim yetkiniz yok. Mevcut rolünüz: $role";
                break;
            case 'unauthorized':
            default:
                echo 'Admin paneline erişim yetkiniz bulunmamaktadır. Lütfen admin hesabınızla giriş yapın.';
                break;
        }
        ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <!-- Floating vegetables background -->
    <div class="floating-veggies">
        <div class="veggie">🥒</div>
        <div class="veggie">🥬</div>
        <div class="veggie">🥦</div>
        <div class="veggie">🌿</div>
        <div class="veggie">🥒</div>
        <div class="veggie">🥬</div>
        <div class="veggie">🥦</div>
        <div class="veggie">🌿</div>
        <div class="veggie">🥒</div>
        <div class="veggie">🥬</div>
    </div>

    <nav class="navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm sticky-top main-navbar">
        <div class="container">
            <a class="navbar-brand fw-bold fs-4 d-flex align-items-center" href="/">
                <img src="images/logo.jpg" alt="Tansu Şahal Salamura Logo" style="height: 50px; margin-right: 10px;">
                <span class="brand-text">
                    <span class="brand-line-1">Tansu Şahal</span>
                    <span class="brand-line-2">Salamura</span>
                </span>
            </a>

            <button class="navbar-toggler" type="button" onclick="toggleIndependentMobileMenu()" aria-label="Menüyü aç/kapat">
                <span class="navbar-toggler-icon"></span>
                <span class="menu-text">Menü</span>
            </button>
            
            <div class="collapse navbar-collapse" id="mainNav">
                <!-- Desktop Menu -->
                <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
                    <li class="nav-item"><a class="nav-link" href="/">Ana Sayfa</a></li>
                    <li class="nav-item"><a class="nav-link" href="#products">Tüm Ürünler</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about-us-section">Hakkımızda</a></li>
                    <li class="nav-item"><a class="nav-link" href="blog.php">Blog</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact-section">İletişim</a></li>
                </ul>
                <div class="d-flex align-items-center navbar-user-info">
                    <span id="userGreeting" class="navbar-user-greeting me-2" style="display:none;"></span>
                    <div class="auth-links-group">
                        <a href="login.php" id="loginLink" class="text-dark text-decoration-none auth-link">Giriş Yap</a>
                        <a href="register.php" id="registerLink" class="text-dark text-decoration-none auth-link">Kayıt Ol</a>
                        <a href="#" id="logoutLink" class="text-dark text-decoration-none auth-link" style="display:none;">Çıkış Yap</a>
                        <a href="admin.php" id="adminLink" class="text-dark text-decoration-none auth-link" style="display:none;">Admin</a>
                        <a href="favorites.php" class="btn btn-outline-danger position-relative rounded-pill px-2 py-1" aria-label="Favoriler">
                            <i class="fas fa-heart me-1"></i>
                            <span class="d-none d-md-inline">Favoriler</span>
                            <span id="favorites-item-count" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger border border-light">0</span>
                        </a>
                    </div>
                </div>
                

            </div>
        </div>
    </nav>

    <section class="hero-section text-center d-flex align-items-center">
        <div class="hero-overlay"></div>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <h1 class="display-3 fw-bolder mb-3 hero-title">
                        <span class="hero-title-line1">Tansu Şahal</span><br>
                        <span class="hero-title-line2">Salamura</span>
                    </h1>
                    <p class="lead text-white-75 mb-4 mx-auto hero-subtitle" style="max-width: 600px;">
                        🌿 Yöresel Usüller İle Hazırlanan Doğal ve Kaliteli El Emeği Ürünler 🌿
                    </p>
                    <a href="#products" class="btn btn-light btn-lg rounded-pill px-5 py-3 hero-button" aria-label="Ürünleri keşfet">
                        🌱 Şimdi Keşfet <i class="fas fa-arrow-right ms-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <main class="container my-5" id="main-content">
        <section id="products">
            <h2 class="text-center mb-5 display-5 fw-light section-title">Ürünlerimiz</h2>

            <!-- Kategori Filtreleme -->
            <div class="d-flex justify-content-center mb-4">
                <div class="btn-group category-filters" role="group" aria-label="Kategori Filtreleri">
                    <button type="button" class="btn btn-outline-primary category-btn active" data-category="all">
                        <i class="fas fa-th-large me-2"></i>Tümü <span class="badge bg-primary ms-2" id="count-all">0</span>
                    </button>
                </div>
            </div>

            <div class="row row-cols-2 row-cols-md-3 row-cols-lg-4 g-4 products-container">
            </div>
        </section>
    </main>

    <section id="about-us-section" class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5 display-5 fw-light section-title">🌿 Hakkımızda</h2>
            <div class="row align-items-center">
                <div class="col-md-6 mb-4 mb-md-0 text-center">
                    <img src="images/logo.jpg" class="img-fluid rounded shadow-lg" alt="Tansu Şahal Salamura Hakkımızda" style="max-height: 400px; object-fit: cover;">
                </div>
                <div class="col-md-6 ps-md-5">
                    <h3 class="fw-semibold mb-3 text-primary">🌱 Doğallığın ve Lezzetin Adresi</h3>
                    <p class="text-muted">
                        🥒 Yöresel Usüller İle Hazırlanan Ürünler<br>
                        💼 Kapıda Ödeme Seçeneği<br>
                        🚛 Tüm Türkiye Kargo Gönderim<br>
                        🌿 %100 Doğal ve Organik
                    </p>

                    <!-- WhatsApp İletişim -->
                    <div class="mt-4 p-3 bg-white rounded shadow-sm border-start border-success border-4">
                        <h5 class="text-success mb-3">
                            <i class="fab fa-whatsapp me-2"></i>
                            Hızlı İletişim
                        </h5>
                        <p class="text-muted small mb-3">
                            Sorularınız için WhatsApp üzerinden bize ulaşabilirsiniz. Hızlı ve güvenilir iletişim için tıklayın!
                        </p>

                        <!-- İlk WhatsApp Numarası -->
                        <a href="https://wa.me/905360359220?text=Merhaba, Tansu Şahal Salamura ürünleriniz hakkında bilgi almak istiyorum."
                           target="_blank"
                           class="btn btn-success btn-lg d-flex align-items-center justify-content-center gap-2 whatsapp-btn mb-2">
                            <i class="fab fa-whatsapp fs-4"></i>
                            <span>WhatsApp - +90 (536) 035 92 20</span>
                        </a>

                        <!-- İkinci WhatsApp Numarası -->
                        <a href="https://wa.me/905367055869?text=Merhaba, Tansu Şahal Salamura ürünleriniz hakkında bilgi almak istiyorum."
                           target="_blank"
                           class="btn btn-success btn-lg d-flex align-items-center justify-content-center gap-2 whatsapp-btn mb-2">
                            <i class="fab fa-whatsapp fs-4"></i>
                            <span>WhatsApp - +90 (536) 705 58 69</span>
                        </a>

                        <a href="https://www.instagram.com/tansusahalsalamura/"
                           target="_blank"
                           class="btn btn-lg d-flex align-items-center justify-content-center gap-2 instagram-btn">
                            <i class="fab fa-instagram fs-4"></i>
                            <span>Instagram'da Takip Et</span>
                        </a>

                        <small class="text-muted d-block mt-2 text-center">
                            <i class="fas fa-clock me-1"></i>
                            Mesai saatleri: 09:00 - 18:00
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="contact-section" class="py-5">
        <div class="container">
            <h2 class="text-center mb-5 display-5 fw-light section-title">📞 Bize Ulaşın</h2>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4 p-md-5">
                            <form id="contactForm">
                                <div class="row g-3">
                                    <div class="col-md-6 mb-3">
                                        <label for="contactName" class="form-label">👤 Adınız Soyadınız</label>
                                        <input type="text" class="form-control form-control-lg" id="contactName" name="name" required aria-describedby="nameHelp">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="contactEmail" class="form-label">📧 E-posta Adresiniz</label>
                                        <input type="email" class="form-control form-control-lg" id="contactEmail" name="email" required aria-describedby="emailHelp">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label for="contactSubject" class="form-label">📝 Konu</label>
                                        <input type="text" class="form-control form-control-lg" id="contactSubject" name="subject" required>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label for="contactMessage" class="form-label">💬 Mesajınız</label>
                                        <textarea class="form-control form-control-lg" id="contactMessage" name="message" rows="5" required></textarea>
                                    </div>
                                    <div class="col-12 text-center">
                                        <button type="submit" class="btn btn-primary btn-lg rounded-pill px-5 py-3">
                                            🌿 Mesajı Gönder
                                        </button>
                                    </div>
                                </div>
                            </form>
                            <div id="contactFormMessage" class="mt-4 text-center fw-medium"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row text-center mt-5 pt-4">
                <div class="col-md-4 mb-4">
                    <i class="fas fa-map-marker-alt fa-3x text-primary mb-3" aria-hidden="true"></i>
                    <h5 class="fw-medium">📍 Adresimiz</h5>
                    <p class="text-muted">Hacıbey, Eski İzmir Cd. no: 69, 45600 Alaşehir/Manisa</p>
                </div>
                <div class="col-md-4 mb-4">
                    <i class="fas fa-phone-alt fa-3x text-primary mb-3" aria-hidden="true"></i>
                    <h5 class="fw-medium">📞 Telefon</h5>
                    <p class="text-muted">
                        +90 (536) 035 92 20<br>
                        +90 (536) 705 58 69
                    </p>
                </div>
                <div class="col-md-4 mb-4">
                    <i class="fas fa-envelope fa-3x text-primary mb-3" aria-hidden="true"></i>
                    <h5 class="fw-medium">📧 E-posta</h5>
                    <p class="text-muted"><EMAIL></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Menu Backdrop -->
    <div id="mobileMenuBackdrop" class="mobile-menu-backdrop" onclick="closeIndependentMobileMenu()"></div>

    <!-- Independent Mobile Menu -->
    <div id="independentMobileMenu" class="independent-mobile-menu">
        <div class="mobile-menu-container">
            <button class="mobile-menu-close" type="button" onclick="closeIndependentMobileMenu()" aria-label="Menüyü kapat">
                <span class="sr-only">Kapat</span>
            </button>

            <div class="mobile-menu-header">
                <div class="brand-text">
                    <span class="brand-line-1">Tansu Şahal</span>
                    <span class="brand-line-2">Salamura</span>
                </div>
            </div>

            <ul class="mobile-nav-links">
                <li class="nav-item"><a class="nav-link active" href="/" onclick="closeIndependentMobileMenu()">🏠 Ana Sayfa</a></li>
                <li class="nav-item"><a class="nav-link" href="#products" onclick="closeIndependentMobileMenu()">🛍️ Tüm Ürünler</a></li>
                <li class="nav-item"><a class="nav-link" href="#about-us-section" onclick="closeIndependentMobileMenu()">🌿 Hakkımızda</a></li>
                <li class="nav-item"><a class="nav-link" href="blog.php" onclick="closeIndependentMobileMenu()">📝 Blog</a></li>
                <li class="nav-item"><a class="nav-link" href="#contact-section" onclick="closeIndependentMobileMenu()">📞 İletişim</a></li>
            </ul>

            <div class="mobile-user-section">
                <div id="mobileUserGreeting2" class="mobile-user-greeting" style="display:none;"></div>

                <div class="mobile-auth-buttons">
                    <a href="login.php" id="mobileLoginLink2" class="mobile-auth-button secondary" onclick="closeIndependentMobileMenu()">
                        <i class="fas fa-sign-in-alt"></i> Giriş Yap
                    </a>
                    <a href="register.php" id="mobileRegisterLink2" class="mobile-auth-button primary" onclick="closeIndependentMobileMenu()">
                        <i class="fas fa-user-plus"></i> Kayıt Ol
                    </a>
                    <a href="#" id="mobileLogoutLink2" class="mobile-auth-button secondary" style="display:none;" onclick="handleMobileLogout()">
                        <i class="fas fa-sign-out-alt"></i> Çıkış Yap
                    </a>
                    <a href="admin.php" id="mobileAdminLink2" class="mobile-auth-button primary" style="display:none;" onclick="closeIndependentMobileMenu()">
                        <i class="fas fa-cog"></i> Admin
                    </a>
                </div>

                <a href="favorites.php" class="mobile-favorites-button" aria-label="Favoriler" onclick="closeIndependentMobileMenu()">
                    <i class="fas fa-heart"></i> Favoriler
                    <span id="mobileFavoritesCount2" class="badge bg-white text-danger">0</span>
                </a>
            </div>

            <div class="mobile-menu-footer">
                <p>🌱 Doğallığın ve Lezzetin Adresi</p>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-white text-center p-4 mt-auto">
        <div class="container">
            <p class="mb-1">© 2025 Tansu Şahal Salamura. Tüm hakları saklıdır. 🌿</p>
            <p class="small">
                <a href="/gizlilik-politikasi.php" class="text-white-50">Gizlilik Politikası</a> |
                <a href="/kullanim-sartlari.php" class="text-white-50">Kullanım Şartları</a> |
                <a href="/sitemap.xml" class="text-white-50">Site Haritası</a>
            </p>
            <p class="small text-white-50 mt-2">
                <i class="fas fa-code me-1"></i>
                Web Tasarımı: <a href="mailto:<EMAIL>" class="text-white-50"><EMAIL></a>
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo getJSLink('header-updater.js'); ?>"></script>
    <script src="<?php echo getJSLink('scripts.js'); ?>"></script>
    <script src="<?php echo getJSLink('mobile-menu.js'); ?>"></script> <!-- Universal mobile menu system -->

    <script>
    // Aktif sayfa için nav-link'lere active class ekle
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname;
        const currentHash = window.location.hash;
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

        // Önce tüm active class'ları temizle
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        navLinks.forEach(link => {
            const href = link.getAttribute('href');

            // Blog sayfası kontrolü (önce blog kontrolü yap)
            if (currentPage.includes('blog.php') && href.includes('blog.php')) {
                link.classList.add('active');
            }
            // Ana sayfa kontrolü - sadece hash yoksa veya ana sayfa hash'i varsa
            else if ((currentPage === '/' || currentPage === '/index.php') && (href === '/' || href === 'index.php')) {
                // Eğer hash yoksa veya ana sayfa ile ilgili değilse Ana Sayfa'yı aktif yap
                if (!currentHash || currentHash === '#' || currentHash === '#home') {
                    link.classList.add('active');
                }
            }
            // Hash kontrolü - sadece ana sayfadayken ve hash varsa
            else if ((currentPage === '/' || currentPage === '/index.php') && href.startsWith('#') && currentHash === href) {
                link.classList.add('active');
            }
        });

        // Hash değişikliklerini dinle
        window.addEventListener('hashchange', function() {
            const newHash = window.location.hash;
            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');

                if (href.startsWith('#') && newHash === href) {
                    link.classList.add('active');
                } else if ((href === '/' || href === 'index.php') && (!newHash || newHash === '#' || newHash === '#home')) {
                    link.classList.add('active');
                }
            });
        });
    });
    </script>
</body>
</html>
