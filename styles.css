/* HAMBURGER MENU BUTTON STYLES */
.navbar-toggler {
  display: flex !important;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  font-weight: 500;
  transition: all 0.15s ease;
  border: none;
  outline: none;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 1000 !important;
  padding: 0.3rem 0.5rem;
  font-size: 0.8rem;
}

/* PERFORMANCE OPTIMIZATION - Bootstrap collapse override */
@media (max-width: 991.98px) {
  .collapse {
    transition: none !important;
  }

  .collapse.show {
    transition: none !important;
  }

  .navbar-collapse {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
}

@media (min-width: 992px) {
  .navbar-collapse {
    transition: none !important;
  }
}

/* CSS Variables for Green Theme */
:root {
  --primary-green: #2c5530;
  --secondary-green: #2c5530;
  --light-green: #90ee90;
  --dark-green: #1a3320;
  --accent-green: #2c5530;
  --bg-green: #f0fff0;
}

/* Global overflow control */
* {
  box-sizing: border-box;
}

/* Prevent horizontal scroll - ULTIMATE FIX */
*, *::before, *::after {
  box-sizing: border-box !important;
  max-width: 100% !important;
}

/* Force no horizontal scroll */
html, body {
  overflow-x: hidden !important;
  max-width: 100vw !important;
  position: relative !important;
}

/* Container overflow fix */
.container, .container-fluid, .navbar {
  overflow-x: hidden !important;
}

/* Mobile navbar collapse should not be constrained */
@media (max-width: 991.98px) {
  .navbar-collapse {
    overflow: visible !important;
    max-width: none !important;
  }
}

html {
  overflow-x: hidden !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

body,
.container-fluid {
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Global container centering (excluding navbar containers) */
.container:not(.navbar .container) {
  max-width: 1200px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
  box-sizing: border-box;
}

/* Ensure ALL content containers are properly centered (excluding navbar) */
main.container,
section:not(.navbar) .container,
.hero-section .container,
#about-us-section .container,
#contact-section .container {
  max-width: 1200px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

/* Center all main content sections */
#products,
#about-us-section,
#contact-section,
.hero-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Center product grid and all content grids */
.products-container,
.row {
  margin-left: auto !important;
  margin-right: auto !important;
  justify-content: center !important;
  max-width: 1200px;
}

/* Center hero section content specifically */
.hero-section .container {
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* Center footer content */
footer .container {
  max-width: 1200px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Mobile responsive centering */
@media (max-width: 768px) {
  .container:not(.navbar .container),
  main.container,
  section:not(.navbar) .container,
  .hero-section .container,
  #about-us-section .container,
  #contact-section .container,
  footer .container {
    max-width: 100% !important;
    margin-left: auto !important;
    margin-right: auto !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .products-container,
  .row {
    margin-left: auto !important;
    margin-right: auto !important;
    justify-content: center !important;
  }
}

body {
  margin: 0 !important;
  padding: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* Modern System Font Stack */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  background: linear-gradient(135deg, var(--bg-green) 0%, #e6ffe6 100%);
  color: #212529;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  padding-top: 80px !important;
}

/* Floating vegetables background */
.floating-veggies {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.veggie {
  position: absolute;
  font-size: 7rem;
  animation: float 7s ease-in-out infinite;
  opacity: 0.7;
    will-change: transform;
}

.veggie:nth-child(1) {
  left: 5%;
  animation-delay: 0s;
}
.veggie:nth-child(2) {
  left: 15%;
  animation-delay: 1s;
}
.veggie:nth-child(3) {
  left: 25%;
  animation-delay: 2s;
}
.veggie:nth-child(4) {
  left: 35%;
  animation-delay: 3s;
}
.veggie:nth-child(5) {
  left: 45%;
  animation-delay: 4s;
}
.veggie:nth-child(6) {
  left: 55%;
  animation-delay: 1s;
}
.veggie:nth-child(7) {
  left: 65%;
  animation-delay: 3s;
}
.veggie:nth-child(8) {
  left: 75%;
  animation-delay: 5s;
}
.veggie:nth-child(9) {
  left: 85%;
  animation-delay: 7s;
}
.veggie:nth-child(10) {
  left: 95%;
  animation-delay: 9s;
}

@keyframes float {
  0%,
  100% {
    transform: translate3d(0, 100vh, 0) rotate(0deg);
  }
  50% {
    transform: translate3d(0, -100px, 0) rotate(180deg);
  }
}

/* Responsive floating vegetables */
@media (max-width: 768px) {
  .veggie {
    font-size: 4rem !important;
      will-change: transform;
}
}

@media (max-width: 480px) {
  .veggie {
    font-size: 3rem !important;
      will-change: transform;
}
}

main.container,
section.container {
  flex-grow: 1;
  position: relative;
  z-index: 2;
  overflow-x: hidden;
  max-width: 1200px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
  width: 100%;
  box-sizing: border-box;
}

/* Modern Professional Navbar */
.navbar-light.bg-white {
  background: rgba(44, 85, 48, 0.95) !important;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(44, 85, 48, 0.3);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 0.4rem 0;
  transition: all 0.3s ease;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
  box-sizing: border-box !important;
}

.navbar-brand {
  letter-spacing: 0.5px;
  color: white !important;
  font-size: 1.25rem;
  font-weight: 700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Brand text responsive styling */
/* --- SALAMURA ALTA --- */
.brand-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  min-width: 110px;
  margin-left: 0.2rem;
  flex-shrink: 1;
}
.brand-line-1 {
  font-size: 1.5rem;
  font-weight: 800;
  color: white;
  letter-spacing: 0.01em;
  line-height: 1.1;
  z-index: 2;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}
.brand-line-2 {
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  letter-spacing: 0.2em;
  opacity: 1;
  text-transform: uppercase;
  margin-top: -0.2rem;
  margin-left: 0.05rem;
  z-index: 1;
  pointer-events: none;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}
@media (max-width: 767.98px) {
  .brand-text {
    min-width: 80px;
  }
  .brand-line-1 {
    font-size: 1rem;
  }
  .brand-line-2 {
    font-size: 0.6rem;
    font-weight: 600;
    color: white;
    opacity: 1;
    margin-top: 0.2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }
}

@media (max-width: 480px) {
  .brand-text {
    min-width: 70px;
  }
  .brand-line-1 {
    font-size: 0.9rem;
  }
  .brand-line-2 {
    font-size: 0.55rem;
  }
  
  .navbar-toggler {
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
    gap: 0.2rem;
  }
  
  .auth-links-group {
    gap: 0.5rem;
  }
  
  .auth-link,
  .btn-outline-danger {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
  }
}
/* --- END SALAMURA ALTA --- */

/* Desktop: show on same line */
@media (min-width: 768px) {
  .brand-text {
    flex-direction: row;
    gap: 0.5rem;
  }

  .brand-line-1::after {
    content: " ";
  }
}

/* Mobile: show on separate lines */
@media (max-width: 767.98px) {
  .brand-text {
    flex-direction: column;
    line-height: 1;
  }

  .brand-line-1 {
    font-size: 0.95rem;
  }

  .brand-line-2 {
    font-size: 0.7rem;
    font-weight: 600;
    color: white;
    opacity: 1;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }
}

.nav-link {
  font-weight: 500;
  color: var(--primary-green) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 0 0.2rem;
  padding: 0.5rem 0.8rem !important;
  position: relative;
  border-radius: 6px;
  font-size: 0.9rem;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-green) !important;
  background: linear-gradient(135deg, rgba(44, 85, 48, 0.15) 0%, rgba(44, 85, 48, 0.25) 100%);
  transform: translate3d(0, -1px, 0);
  box-shadow: 0 4px 12px rgba(44, 85, 48, 0.3);
}

/* Aktif sayfa için kalıcı gölge */
.nav-link.active {
  box-shadow: 0 4px 15px rgba(44, 85, 48, 0.4) !important;
  background: linear-gradient(135deg, rgba(44, 85, 48, 0.2) 0%, rgba(44, 85, 48, 0.3) 100%) !important;
}

.nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 4px;
  left: 50%;
  background: var(--primary-green);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 1px;
}

.nav-link:hover::after {
  width: 80%;
  left: 10%;
}

/* Navbar containers - Desktop */
.navbar-nav.mx-auto {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 50%, rgba(241, 245, 249, 0.95) 100%);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-left: 1rem !important;
  margin-right: 1rem !important;
}

/* Navbar User Info - Desktop */
.navbar-user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 50%, rgba(241, 245, 249, 0.95) 100%);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.navbar-user-greeting {
  font-size: 0.7rem;
  color: var(--primary-green) !important;
  font-weight: 600;
  white-space: nowrap !important;
  word-break: normal !important;
  width: auto !important;
  max-width: 150px;
  line-height: 1.2;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: inline-block !important;
  margin-bottom: 0;
  padding: 0.25rem 0.5rem;
  background: rgba(44, 85, 48, 0.1);
  border-radius: 0.375rem;
  text-align: left;
}

/* Orta boyutlu ekranlarda greeting'i gizle */
@media (max-width: 1200px) {
  .navbar-user-greeting {
    display: none !important;
  }
}

.auth-links-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-right: 0.5rem;
}

.auth-link {
  font-weight: 500;
  font-size: 0.85rem;
  white-space: nowrap;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  border: 1px solid rgba(44, 85, 48, 0.2);
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none !important;
  color: var(--primary-green) !important;
  box-shadow: none;
}

.auth-link:hover {
  background: rgba(44, 85, 48, 0.1);
  color: var(--primary-green) !important;
  border-color: rgba(44, 85, 48, 0.3);
  transform: translate3d(0, -1px, 0);
}

/* Admin butonu özel stil */
#adminLink {
  background: none !important;
  border: none !important;
  color: var(--primary-green) !important;
  font-size: 0.85rem;
  padding: 0.35rem 0.6rem;
  font-weight: 500;
}

#adminLink:hover {
  background-color: var(--light-green) !important;
  color: var(--dark-green) !important;
}

.btn-outline-danger {
  font-size: 0.85rem;
  padding: 0.4rem 0.8rem;
  white-space: nowrap;
  border: 1px solid rgba(220, 38, 38, 0.3);
  color: #dc2626;
  border-radius: 6px;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.btn-outline-danger:hover {
  background: #dc2626;
  border-color: #dc2626;
  color: white;
  transform: translate3d(0, -1px, 0);
}

.btn-outline-danger .badge {
  font-size: 0.7em;
  padding: 0.3em 0.5em;
  line-height: 1;
}

/* Hero Section - Green Theme with Background Image */
.hero-section {
  background: linear-gradient(135deg, rgba(44, 85, 48, 0.6) 0%, rgba(44, 85, 48, 0.5) 50%, rgba(44, 85, 48, 0.6) 100%),
    url("images/herosection.jpg");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  min-height: 100vh;
  color: #ffffff;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  z-index: 2;
}

/* Modern decorative border between header and hero */
.hero-section::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 10%,
    rgba(255, 255, 255, 0.8) 25%,
    rgba(255, 255, 255, 1) 50%,
    rgba(255, 255, 255, 0.8) 75%,
    rgba(255, 255, 255, 0.3) 90%,
    transparent 100%);
  z-index: 3;
  animation: shimmer 3s ease-in-out infinite;
}

/* Modern decorative border at bottom of hero section */
.hero-section::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 15%,
    rgba(255, 255, 255, 0.6) 30%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(255, 255, 255, 0.6) 70%,
    rgba(255, 255, 255, 0.2) 85%,
    transparent 100%);
  z-index: 3;
  animation: shimmerBottom 4s ease-in-out infinite reverse;
}

@keyframes shimmerBottom {
  0%, 100% {
    opacity: 0.6;
    transform: scaleX(0.98);
  }
  50% {
    opacity: 0.9;
    transform: scaleX(1.01);
  }
}

@keyframes shimmer {
  0%, 100% {
    opacity: 0.7;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.02);
  }
}

/* Dark overlay for hero section */
.hero-section .hero-overlay {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  pointer-events: none;
}

.hero-section .container {
  position: relative;
  z-index: 2;
  max-width: 1200px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.hero-section .hero-title {
  font-size: 4rem;
  text-shadow: 3px 3px 12px rgba(0, 0, 0, 0.7);
  animation: heroGlow 4s ease-in-out infinite alternate;
  font-weight: 800;
  line-height: 1.1;
}

/* Hero title line styling */
.hero-title-line1 {
  display: block;
  font-size: 4rem;
  font-weight: 800;
}

.hero-title-line2 {
  display: block;
  font-size: 3.5rem;
  font-weight: 700;
  margin-top: -3.5rem;
  opacity: 0.95;
}

@keyframes heroGlow {
  0% {
    text-shadow: 3px 3px 12px rgba(0, 0, 0, 0.7);
  }
  100% {
    text-shadow: 3px 3px 12px rgba(0, 0, 0, 0.7), 0 0 30px rgba(255, 255, 255, 0.4);
  }
}

.hero-section .hero-subtitle {
  font-size: 1.7rem;
  text-shadow: 3px 3px 9px rgba(0, 0, 0, 0.6);
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 500;
  margin-bottom: 2rem;
}

.hero-section .hero-button {
  font-weight: 700;
  font-size: 1.2rem;
  background: rgba(255, 255, 255, 0.95);
  color: var(--primary-green);
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.4s ease;
  position: relative;
  z-index: 3;
  backdrop-filter: blur(10px);
}

/* Şimdi Keşfet butonu hover efekti - Ana tema yeşil + beyaz yazı */
.hero-section .hero-button:hover,
.hero-section .btn.hero-button:hover,
.btn-light.hero-button:hover,
a.btn.btn-light.hero-button:hover {
  transform: translate3d(0, -5px, 0) scale(1.05) !important;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4) !important;
  background: #2c5530 !important; /* Ana tema yeşil */
  color: white !important; /* Beyaz yazı */
  border-color: #2c5530 !important;
  transition: all 0.4s ease !important;
}

/* Section Başlıkları - Green Theme */
.section-title {
  color: var(--dark-green);
  position: relative;
  padding-bottom: 15px;
  margin-bottom: 40px !important;
  font-weight: 600;
}

.section-title::after {
  content: "";
  position: absolute;
  display: block;
  width: 60px;
  height: 3px;
  background: var(--accent-green);
  bottom: 0;
  left: calc(50% - 30px);
}

/* Ürün Kartları - Green Theme */
.product-card {
  transition: transform 0.25s ease-in-out, box-shadow 0.25s ease-in-out;
  border: 3px solid var(--light-green);
  border-radius: 1rem;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  position: relative;
}

.product-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 1rem;
  padding: 3px;
  background: linear-gradient(45deg, var(--light-green), var(--accent-green), var(--primary-green));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  pointer-events: none;
  z-index: 1;
}

.product-card:hover {
  transform: translate3d(0, -10px, 0);
  box-shadow: 0 15px 35px rgba(34, 139, 34, 0.2) !important;
  border-color: var(--accent-green);
}

.product-card:hover::before {
  background: linear-gradient(45deg, var(--accent-green), var(--primary-green), var(--secondary-green));
}

.product-card .card-img-top {
  border-bottom: 1px solid #f0f0f0;
  transition: transform 0.3s ease;
}

.product-card:hover .card-img-top {
  transform: scale(1.05);
}

.product-card .card-body {
  padding: 1.25rem;
}

.product-card .card-title {
  font-size: 1.05rem;
  font-weight: 600;
  color: var(--dark-green);
  min-height: 2.6em;
}

.product-card .card-text_description {
  font-size: 0.85rem;
  color: #6c757d;
  min-height: 4em;
}

.product-card .card-text_stock small {
  font-size: 0.8rem;
  color: #888;
}

.product-card .text-primary {
  color: var(--primary-green) !important;
}

.product-card .add-to-cart-button {
  font-weight: 500;
  padding: 0.6rem 1rem;
  background: linear-gradient(45deg, var(--primary-green), var(--accent-green));
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.product-card .add-to-cart-button:hover {
  transform: translate3d(0, -2px, 0);
  box-shadow: 0 5px 15px rgba(34, 139, 34, 0.3);
}

.product-card .add-to-cart-button:disabled {
  background-color: #adb5bd;
  border-color: #adb5bd;
}

/* Mobil için ürün kartları optimizasyonu - Daha kompakt */
@media (max-width: 767.98px) {
  /* Bootstrap'ın row-cols-2 sistemini koruyoruz, sadece gap'i ayarlıyoruz */
  .products-container.row {
    --bs-gutter-x: 0.5rem;
    --bs-gutter-y: 0.5rem;
  }

  /* Kart içeriklerini optimize ediyoruz */
  .product-card {
    border-radius: 0.75rem;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .product-card .card-body {
    padding: 0.75rem 0.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .product-card .card-title {
    font-size: 0.85rem;
    font-weight: 600;
    line-height: 1.2;
    min-height: auto;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Açıklamayı tamamen kaldır */
  .product-card .card-text_description {
    display: none;
  }

  .product-card .card-text_stock {
    margin-bottom: 0.5rem;
  }

  .product-card .card-text_stock small {
    font-size: 0.7rem;
  }

  /* Video section'ı daha kompakt yap */
  .product-card .mt-2.mb-2 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.5rem !important;
  }

  .product-card .mt-2.mb-2 small {
    font-size: 0.7rem;
  }

  .product-card .text-primary {
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .product-card .d-grid.gap-2 {
    gap: 0.25rem !important;
  }

  .product-card .btn {
    font-size: 0.75rem;
    padding: 0.4rem 0.5rem;
    border-radius: 0.4rem;
  }

  .product-card .card-img-top {
    height: 160px;
    object-fit: cover;
    padding: 0.5rem !important;
  }

  /* Favoriler butonunu daha küçük yap */
  .product-card .favorite-btn {
    width: 25px !important;
    height: 25px !important;
    font-size: 1rem;
    top: 10px !important;
    right: 10px !important;
  }
}

/* Çok küçük ekranlar için ek optimizasyon */
@media (max-width: 575.98px) {
  .product-card .card-img-top {
    height: 140px;
    padding: 0.25rem !important;
  }

  .product-card .card-title {
    font-size: 0.8rem;
  }

  .product-card .text-primary {
    font-size: 0.9rem;
  }

  .product-card .btn {
    font-size: 0.7rem;
    padding: 0.35rem 0.4rem;
  }

  .product-card .card-body {
    padding: 0.5rem 0.4rem;
  }

  .product-card .favorite-btn {
    width: 28px !important;
    height: 28px !important;
    font-size: 0.7rem;
  }
}

/* Stokta yok mesajı */
.out-of-stock small {
  color: #e74c3c !important;
  font-weight: 500;
}

/* About Section - Keep original photo styling */
#about-us-section {
  background: linear-gradient(135deg, rgba(144, 238, 144, 0.1) 0%, rgba(50, 205, 50, 0.1) 100%);
  position: relative;
  z-index: 2;
}

#about-us-section .text-primary {
  color: var(--primary-green) !important;
}

/* Contact Section - Green Theme */
#contact-section {
  position: relative;
  z-index: 2;
  background: linear-gradient(135deg, var(--bg-green) 0%, rgba(144, 238, 144, 0.1) 100%);
}

#contact-section .card {
  border: 2px solid var(--light-green);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

#contact-section .form-control {
  border: 2px solid var(--light-green);
  border-radius: 15px;
  transition: all 0.3s ease;
}

#contact-section .form-control:focus {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 0.2rem rgba(34, 139, 34, 0.25);
}

#contact-section .btn-primary {
  background: linear-gradient(45deg, var(--primary-green), var(--accent-green));
  border: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

#contact-section .btn-primary:hover {
  transform: translate3d(0, -3px, 0);
  box-shadow: 0 10px 25px rgba(34, 139, 34, 0.3);
}

#contact-section .text-primary {
  color: var(--primary-green) !important;
}

#contact-section .fa-3x {
  color: var(--primary-green) !important;
  animation: iconBounce 2s ease-in-out infinite;
}

@keyframes iconBounce {
  0%,
  100% {
    transform: translate3d(0, 0px, 0);
  }
  50% {
    transform: translate3d(0, -5px, 0);
  }
}

/* Footer - Green Theme */
footer.bg-dark {
  background: linear-gradient(135deg, var(--dark-green) 0%, var(--primary-green) 100%) !important;
  position: relative;
  z-index: 2;
}

footer.bg-dark p {
  margin-bottom: 0.2rem;
}

footer.bg-dark a {
  transition: color 0.2s ease;
  text-decoration: underline;
  text-underline-offset: 4px; /* Altı çizgiyi yazıdan uzaklaştır */
  text-decoration-thickness: 1px; /* Çizgi kalınlığı */
}

footer.bg-dark a:hover {
  color: var(--light-green) !important;
  text-underline-offset: 6px; /* Hover'da daha da uzaklaştır */
  text-decoration-thickness: 2px; /* Hover'da çizgiyi kalınlaştır */
}

/* AUTH PAGES STYLES */

/* Auth wrapper for login/register pages */
main {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  padding: 2rem 1rem;
}

.auth-wrapper {
  display: flex;
  max-width: 1000px;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(107, 142, 35, 0.3);
}

.auth-illustration {
  flex: 1;
  background: linear-gradient(45deg, #228b22, #32cd32, #90ee90);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-height: 500px;
}

/* Character animations for auth pages */
.character {
  width: 200px;
  height: 200px;
  position: relative;
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%,
  100% {
    transform: translate3d(0, 0px, 0);
  }
  50% {
    transform: translate3d(0, -20px, 0);
  }
}

.character-body {
  width: 80px;
  height: 100px;
  background: #2e8b57;
  border-radius: 40px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border: 3px solid #fff;
}

.character-head {
  width: 60px;
  height: 60px;
  background: #98fb98;
  border-radius: 50%;
  position: absolute;
  left: 50%;
  top: 20%;
  transform: translateX(-50%);
  border: 3px solid #fff;
}

.character-eyes {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.eye {
  width: 8px;
  height: 8px;
  background: #2d5016;
  border-radius: 50%;
  display: inline-block;
  margin: 0 3px;
  animation: blink 3s infinite;
}

@keyframes blink {
  0%,
  90%,
  100% {
    transform: scaleY(1);
  }
  95% {
    transform: scaleY(0.1);
  }
}

.character-mouth {
  position: absolute;
  top: 35px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 10px;
  border: 2px solid #2d5016;
  border-top: none;
  border-radius: 0 0 20px 20px;
}

.character-arms {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translateX(-50%);
}

.arm {
  width: 30px;
  height: 8px;
  background: #2e8b57;
  border-radius: 4px;
  position: absolute;
  animation: wave 1.5s ease-in-out infinite alternate;
  border: 1px solid #fff;
}

.arm.left {
  left: -35px;
  transform-origin: right center;
}
.arm.right {
  right: -35px;
  transform-origin: left center;
}

@keyframes wave {
  0% {
    transform: rotate(-20deg);
  }
  100% {
    transform: rotate(20deg);
  }
}

.pickle-jar {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 80px;
  background: linear-gradient(to bottom, transparent 0%, #228b22 20%, #2e8b57 100%);
  border-radius: 5px 5px 15px 15px;
  border: 3px solid #fff;
  animation: wiggle 2s ease-in-out infinite;
}

.pickle-jar::before {
  content: "🥬";
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.5rem;
}

@keyframes wiggle {
  0%,
  100% {
    transform: rotate(-3deg);
  }
  50% {
    transform: rotate(3deg);
  }
}

.welcome-text {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
}

.welcome-text h3 {
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.welcome-text p {
  margin: 0;
  opacity: 0.9;
}

/* Register page specific styles */
.characters-container {
  display: flex;
  gap: 40px;
  align-items: center;
}

.character.friend1 {
  animation: bounce 2s ease-in-out infinite;
  width: 150px;
  height: 150px;
}

.character.friend2 {
  animation: bounce 2s ease-in-out infinite 0.5s;
  width: 150px;
  height: 150px;
}

.friend1 .character-body {
  background: #2e8b57;
  border: 3px solid #fff;
  width: 60px;
  height: 80px;
}

.friend2 .character-body {
  background: #228b22;
  border: 3px solid #fff;
  width: 60px;
  height: 80px;
}

.friend1 .character-head,
.friend2 .character-head {
  width: 50px;
  height: 50px;
  top: 25%;
}

.friend1 .eye,
.friend2 .eye {
  width: 6px;
  height: 6px;
  margin: 0 2px;
  animation: blink 4s infinite;
}

.friend1 .character-mouth,
.friend2 .character-mouth {
  top: 28px;
  width: 16px;
  height: 8px;
}

.friend1 .arm,
.friend2 .arm {
  width: 25px;
  height: 6px;
}

.friend1 .arm {
  background: #2e8b57;
  animation: wave1 1.8s ease-in-out infinite alternate;
}

.friend2 .arm {
  background: #228b22;
  animation: wave2 2.2s ease-in-out infinite alternate;
}

.arm.left {
  left: -30px;
}
.arm.right {
  right: -30px;
}

@keyframes wave1 {
  0% {
    transform: rotate(-25deg);
  }
  100% {
    transform: rotate(25deg);
  }
}

@keyframes wave2 {
  0% {
    transform: rotate(-30deg);
  }
  100% {
    transform: rotate(30deg);
  }
}

.high-five {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2rem;
  animation: highFive 3s ease-in-out infinite;
}

@keyframes highFive {
  0%,
  80%,
  100% {
    opacity: 0;
    transform: translateX(-50%) scale(0.5);
  }
  10%,
  70% {
    opacity: 1;
    transform: translateX(-50%) scale(1.2);
  }
}

.celebration-text {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
}

.celebration-text h3 {
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.celebration-text p {
  margin: 0;
  opacity: 0.9;
}

/* Auth container styles */
.auth-container {
  flex: 1;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.auth-container h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--dark-green);
  font-weight: 700;
  font-size: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--dark-green);
}

.form-control-lg {
  padding: 1rem 1.25rem;
  font-size: 1rem;
  border: 2px solid var(--light-green);
  border-radius: 15px;
  transition: all 0.3s ease;
}

.form-control-lg:focus {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 0.2rem rgba(34, 139, 34, 0.25);
  transform: translate3d(0, -2px, 0);
}

.btn-submit {
  width: 100%;
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 15px;
  background: linear-gradient(45deg, var(--primary-green), var(--accent-green));
  border: none;
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-submit:hover {
  transform: translate3d(0, -3px, 0);
  box-shadow: 0 10px 25px rgba(34, 139, 34, 0.4);
  background: linear-gradient(45deg, var(--secondary-green), var(--primary-green));
}

.btn-submit::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-submit:hover::before {
  left: 100%;
}

.message-area {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 15px;
  text-align: center;
  display: none;
  font-size: 0.9rem;
  font-weight: 500;
}

.message-success {
  background: linear-gradient(45deg, #d4edda, #c3e6cb);
  color: #155724;
  border: 2px solid #c3e6cb;
}

.message-error {
  background: linear-gradient(45deg, #f8d7da, #f5c6cb);
  color: #721c24;
  border: 2px solid #f5c6cb;
}

.auth-links {
  text-align: center;
  margin-top: 2rem;
  font-size: 1rem;
}

.auth-links a {
  color: var(--primary-green);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.auth-links a:hover {
  color: var(--secondary-green);
  text-decoration: underline;
}

/* Responsive Navbar - Mobil için özel düzenlemeler */
@media (max-width: 991.98px) {
  .navbar-brand {
    font-size: 1.1rem;
  }

  /* Greeting yazısını gizle */
  .navbar-user-greeting {
    display: none !important;
  }

  /* Navbar collapse container */
  .navbar-collapse {
    background: rgba(44, 85, 48, 0.98) !important;
    border-radius: 15px !important;
    margin-top: 1rem !important;
    padding: 1rem !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;
  }

  .navbar-nav {
    text-align: center !important;
    margin: 1rem 0 !important;
    padding: 1rem 0 !important;
    border-bottom: 1px solid var(--light-green) !important;
    display: flex !important;
    flex-direction: column !important;
    background: rgba(44, 85, 48, 0.98) !important;
    border-radius: 15px !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;
    z-index: 1000 !important;
    width: 100% !important;
  }

  /* Mobil menüde butonların düzeni */
  .nav-auth-buttons {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0 1rem;
  }

  .nav-auth-buttons .btn {
    width: 100%;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
  }

  .nav-auth-buttons .btn:last-child {
    margin-bottom: 0;
  }

  .navbar-nav .nav-item {
    margin: 0.5rem 0 !important;
    width: 100% !important;
    display: block !important;
  }

  .navbar-nav .nav-item .nav-link {
    color: white !important;
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
    display: block !important;
    padding: 0.75rem 1rem !important;
    border-radius: 0.375rem !important;
    margin: 0 !important;
    text-decoration: none !important;
  }

  /* Kayıt Ol ve Favoriler butonları için stil */
  .navbar-nav .nav-item.auth-nav-item {
    display: block;
    width: 100%;
    padding: 0 1rem;
  }

  /* Kayıt Ol butonu stili */
  .navbar-nav .btn-register {
    display: block;
    width: 100%;
    margin: 0.5rem 0;
    padding: 1rem;
    font-size: 1.1rem;
    background-color: var(--primary-green);
    color: white;
    border: none;
    border-radius: 10px;
    text-align: center;
    font-weight: 600;
  }

  /* Favoriler butonu stili */
  .navbar-nav .btn-favorites {
    display: block;
    width: 100%;
    margin: 0.5rem 0;
    padding: 1rem;
    font-size: 1.1rem;
    background-color: white;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
    border-radius: 10px;
    text-align: center;
    font-weight: 600;
  }

  .navbar-nav .nav-link {
    padding: 0.75rem 1rem !important;
    margin: 0;
    border-radius: 0.375rem;
    font-size: 1.2rem !important;
    color: white !important;
    font-weight: 600 !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    display: block !important;
  }

  .navbar-nav .nav-link:hover {
    background-color: var(--light-green);
    color: var(--primary-green) !important;
  }

  .navbar-user-info {
    width: 100%;
    flex-direction: column;
    align-items: center;
    gap: 0;
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(44, 85, 48, 0.95);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  
  .auth-links-group {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
  }

  /* Enhanced mobile button sizing - matching product detail */
  .auth-links-group .auth-link,
  .auth-links-group #adminLink {
    display: block;
    text-align: center;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.5rem;
    background-color: transparent;
    color: white !important;
    font-weight: 500;
    width: 100%;
    max-width: 280px;
  }

  .auth-links-group .auth-link:hover,
  .auth-links-group #adminLink:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white !important;
    border-color: rgba(255, 255, 255, 0.5);
  }

  .auth-links-group .btn-outline-danger {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border-radius: 0.5rem;
    background-color: transparent;
    width: 100%;
    max-width: 280px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border-color: rgba(220, 38, 38, 0.5);
    color: #ff6b6b;
  }

  .auth-links-group .btn-outline-danger:hover {
    background-color: rgba(220, 38, 38, 0.2);
    border-color: rgba(220, 38, 38, 0.7);
    color: #ff6b6b;
  }

  /* Auth pages responsive */
  .auth-wrapper {
    flex-direction: column;
    max-width: 500px;
  }

  .auth-illustration {
    min-height: 200px;
  }

  .character {
    width: 120px;
    height: 120px;
  }

  .characters-container {
    gap: 20px;
  }

  .character.friend1,
  .character.friend2 {
    width: 100px;
    height: 100px;
  }

  .auth-container {
    padding: 2rem 1.5rem;
  }
}

/* Çok küçük ekranlar için */
@media (max-width: 575.98px) {
  .navbar-brand {
    font-size: 1rem;
  }

  .floating-veggies .veggie {
    font-size: 1rem;
      will-change: transform;
}

  .navbar-user-info {
    margin-top: 0.5rem;
    padding: 0.75rem;
  }

  

  .auth-links-group {
    gap: 0.5rem;
    align-items: center;
  }

  .auth-link,
  #adminLink,
  .btn-outline-danger {
    padding: 0.6rem 0.75rem;
    font-size: 0.95rem;
    width: 100%;
    max-width: 260px;
    text-align: center;
  }

  .btn-outline-danger {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .hero-section {
    min-height: 60vh;
    padding: 3rem 0;
  }

  .hero-section .hero-title {
    font-size: 2.2rem;
  }

  .hero-title-line1 {
    font-size: 2.2rem !important;
  }

  .hero-title-line2 {
    font-size: 2rem !important;
    margin-top: -2rem !important;
  }

  .hero-section .hero-subtitle {
    font-size: 1rem;
  }

  .product-card .card-title {
    min-height: auto;
  }

  .product-card .card-text_description {
    min-height: auto;
    -webkit-line-clamp: 2;
  }

  .auth-wrapper {
    margin: 1rem;
  }

  .auth-container {
    padding: 1.5rem 1rem;
  }

  .auth-container h2 {
    font-size: 1.5rem;
  }
}

@media (min-width: 992px) {
  .navbar .container,
  .navbar-collapse {
    display: flex !important;
    align-items: center;
    justify-content: flex-start !important; /* Sola hizala */
    padding-left: 0.5rem;
  }
  .navbar-brand {
    margin-right: 1rem !important; /* Daha az boşluk */
    padding-left: 0.5rem;
    flex-shrink: 0;
  }
  .navbar-nav {
    flex: 1 1 auto;
    justify-content: center;
    gap: 2.2rem;
  }
  .auth-links-group {
    flex-shrink: 0;
    gap: 1.5rem;
  }
}

@media (max-width: 991.98px) {
  .main-navbar .container {
    display: flex;
    justify-content: flex-start; /* Sola hizala */
    align-items: center;
    padding-left: 0.5rem; /* Sola boşluk ekle */
  }
  .navbar-brand {
    padding-left: 0.5rem; /* Mobilde de sola boşluk ekle */
  }

  .navbar-toggler {
    margin-left: auto;
    margin-right: 2rem;
    padding: 0.4rem 0.8rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
    min-width: 100px;
    max-width: 140px;
    white-space: nowrap;
    overflow: visible;
  }
  
  .navbar-toggler:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translate3d(0, -1px, 0);
  }

  .navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    outline: none;
  }
  
  .navbar-toggler .navbar-toggler-icon {
    width: 20px;
    height: 2px;
    background: white;
    position: relative;
    margin-right: 0.5rem;
    display: inline-block;
  }

  .navbar-toggler .navbar-toggler-icon::before,
  .navbar-toggler .navbar-toggler-icon::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: white;
    left: 0;
  }
  
  .navbar-toggler .navbar-toggler-icon::before {
    top: -6px;
  }
  
  .navbar-toggler .navbar-toggler-icon::after {
    bottom: -6px;
  }
  
  .navbar-toggler .menu-text {
    font-size: 0.85rem;
    font-weight: 500;
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  }
  
  .navbar-toggler[aria-expanded="true"] i {
    transform: rotate(90deg);
    transition: transform 0.3s ease;
  }

  .navbar-user-info {
    flex-direction: column !important;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
  }

  .auth-links-group {
    flex-direction: column !important;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
  }

  .auth-link,
  #adminLink,
  .btn-outline-danger {
    width: auto !important;
    max-width: none !important;
    font-size: 0.85rem;
    padding: 0.4rem 0.6rem;
  }
}

/* ============================================ */
/* FAVORİLER SAYFASI İÇİN MODERN STİLLER */
/* ============================================ */

/* Favoriler sayfası için modern stiller */
.favorites-header {
  text-align: center;
  margin: 2rem 0;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.favorites-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.favorites-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.loading {
  text-align: center;
  padding: 2rem;
  font-size: 1.1rem;
  color: #666;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin: 2rem 0;
}

.empty-state i {
  font-size: 4rem;
  color: #bdc3c7;
  margin-bottom: 1.5rem;
}

.empty-state h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.empty-state p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* Modern product card güncellemeleri */
.product-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f8f9fa;
}

.product-card:hover {
  transform: translate3d(0, -8px, 0);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.product-image {
  position: relative;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.favorite-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
}

.favorite-btn.active {
  background: #e74c3c;
  color: white;
}

.favorite-btn:hover {
  transform: scale(1.1);
}

.product-info {
  padding: 1.5rem;
}

.product-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.product-description {
  color: #7f8c8d;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  font-size: 1.4rem;
  font-weight: bold;
  color: #27ae60;
  margin-bottom: 1rem;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.product-actions .btn {
  flex: 1;
  min-width: 120px;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  text-align: center;
}

.btn-primary {
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  transform: translate3d(0, -2px, 0);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-danger {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
  transform: translate3d(0, -2px, 0);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.message {
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 5px;
  font-weight: 500;
  display: none;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Modern Favorites Card Styles */
.favorite-card-modern {
  transition: all 0.3s ease !important;
  border: 2px solid var(--light-green) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
}

.favorite-card-modern:hover {
  border-color: var(--accent-green) !important;
}

.favorite-card-modern .card-img-top {
  border-bottom: 1px solid var(--light-green);
}

.favorite-card-modern .btn-outline-primary {
  border-color: var(--primary-green);
  color: var(--primary-green);
  transition: all 0.3s ease;
}

.favorite-card-modern .btn-outline-primary:hover {
  background-color: var(--primary-green);
  border-color: var(--primary-green);
  transform: translate3d(0, -2px, 0);
}

.favorite-card-modern .btn-warning {
  background: linear-gradient(45deg, #f27a1a, #e06c0d);
  border: none;
  transition: all 0.3s ease;
}

.favorite-card-modern .btn-warning:hover {
  transform: translate3d(0, -2px, 0);
  box-shadow: 0 5px 15px rgba(242, 122, 26, 0.3);
}

/* Favorites container enhancements */
.favorites-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Badge animations */
.badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Responsive favoriler */
@media (max-width: 768px) {
  .favorites-header h1 {
    font-size: 2rem;
  }

  .product-actions {
    flex-direction: column;
  }

  .product-actions .btn {
    flex: none;
    width: 100%;
  }
}

/* Favoriler sayfasına özel stiller - Bootstrap ile uyumlu */
.favorites-container {
  padding: 2rem;
  background-color: #fff;
  border-radius: 0.75rem;
  box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
}

.favorite-item {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e9ecef;
  padding: 1.25rem 0;
}

.favorite-item:last-child {
  border-bottom: none;
}

.favorite-item img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  margin-right: 1.5rem;
  border-radius: 0.375rem;
  border: 1px solid #dee2e6;
}

.favorite-item-details {
  flex-grow: 1;
}

.favorite-item-details h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #343a40;
}

.favorite-item-details p {
  margin: 0 0 0.25rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.favorite-item-price {
  font-weight: 700;
  font-size: 1.3rem;
  color: #0d6efd;
  margin-right: 1rem;
}

.favorite-item-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.favorite-item-remove button {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0.5rem;
  line-height: 1;
}

.favorite-item-remove button:hover {
  color: #a71d2a;
}

.favorites-summary {
  margin-top: 2.5rem;
  text-align: center;
  border-top: 2px solid #dee2e6;
  padding-top: 1.5rem;
}

.favorites-summary h3 {
  margin-bottom: 1rem;
  font-weight: 600;
  color: #343a40;
}

.favorites-actions button {
  padding: 0.75rem 1.5rem;
  font-size: 1.05em;
  cursor: pointer;
  border-radius: 0.5rem;
  margin: 0.5rem;
  font-weight: 500;
}

.empty-favorites-message {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
  font-size: 1.2rem;
}

.empty-favorites-message i {
  font-size: 3rem;
  display: block;
  margin-bottom: 1rem;
  color: #ced4da;
}

.btn-trendyol {
  background: #f27a1a;
  border: none;
  color: white;
}

.btn-trendyol:hover {
  background: #e06c0d;
  color: white;
}

/* Favoriler sayfası buton hover düzeltmeleri */
.favorite-card-modern .btn-outline-primary {
  border-color: var(--primary-green);
  color: var(--primary-green);
  transition: all 0.3s ease;
  background-color: transparent;
}

.favorite-card-modern .btn-outline-primary:hover {
  background-color: var(--primary-green) !important;
  border-color: var(--primary-green) !important;
  color: white !important;
  transform: translate3d(0, -2px, 0);
  box-shadow: 0 4px 12px rgba(34, 139, 34, 0.3);
}

.favorite-card-modern .btn-outline-primary:focus {
  background-color: var(--primary-green) !important;
  border-color: var(--primary-green) !important;
  color: white !important;
  box-shadow: 0 0 0 0.2rem rgba(34, 139, 34, 0.25);
}

.empty-favorites-message .btn {
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
  min-width: 200px;
}

@media (max-width: 768px) {
  .empty-favorites-message .btn {
    min-width: 180px;
    padding: 0.7rem 1.5rem;
  }
}

/* ===== ÜRÜN AÇIKLAMASI FORMATLI GÖRÜNÜM ===== */
.product-description-modern {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16px;
  padding: 24px;
  margin: 20px 0;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.product-description-modern::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #28a745 0%, #20c997 100%);
}

.description-content {
  color: #495057 !important;
  font-size: 15px !important;
  line-height: 1.8 !important;
  margin: 0 !important;
  text-align: left !important;
  font-weight: 400 !important;
  white-space: pre-line !important; /* Bu satır sonlarını korur */
  word-wrap: break-word !important;
}

/* Responsive */
@media (max-width: 768px) {
  .product-description-modern {
    padding: 16px;
    margin: 16px 0;
  }

  .description-content {
    font-size: 14px !important;
    line-height: 1.7 !important;
  }
}

/* ===== ÜRÜN DETAY SAYFASI MOBİL BUTON DÜZELTMELERİ ===== */

/* Mobile Action Buttons - Product Detail Page */
.mobile-action-buttons {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.mobile-quick-info {
  margin-top: 1rem;
}

/* Responsive Design - Mobil buton düzeltmeleri */
@media (max-width: 767.98px) {
  /* Ürün kartı içindeki boşlukları azalt */
  .product-card {
    padding: 0.5rem !important;
  }

  .product-card .card-body {
    padding: 0.5rem !important;
  }

  /* Stok ve video bilgisi arasındaki boşluğu azalt */
  .product-card .stock-info,
  .product-card .video-info {
    margin: 0.15rem 0 !important;
    padding: 0.15rem 0.2rem !important;
    font-size: 0.85rem !important;
    line-height: 1.2 !important;
  }

  /* Bilgi ikonlarının boyutunu küçült */
  .product-card .stock-info i,
  .product-card .video-info i {
    font-size: 0.85rem !important;
    margin-right: 0.2rem !important;
  }

  /* Ürün kartındaki tüm gereksiz boşlukları kaldır */
  .product-card .card-title {
    margin-bottom: 0.25rem !important;
  }

  .product-card .price-container {
    margin: 0.25rem 0 !important;
  }

  /* Mobile Action Buttons - Daha büyük ve verimli */
  .mobile-action-buttons .btn,
  .product-card .add-to-cart-button,
  .product-card .btn-details,
  .product-card .shopier-btn,
  .product-card .detail-btn {
    font-size: 1.25rem !important;
    padding: 1rem 1.5rem !important;
    border-radius: 30px !important;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 60px !important;
    margin: 0.4rem 0 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    font-weight: 700 !important;
    width: 100% !important;
    font-weight: 600 !important;
  }

  /* Favoriler butonunu diğerleriyle aynı yap */
  .mobile-action-buttons .favorite-btn-detail {
    border-radius: 25px !important;
    height: 54px !important;
    min-height: 54px !important;
    padding: 0.85rem 1.2rem !important;
    font-size: 1.1rem !important;
  }

  .mobile-action-buttons .favorite-btn-detail i {
    font-size: 1.1rem !important;
  }

  .mobile-quick-info .alert {
    padding: 0.4rem !important;
    font-size: 0.9rem !important;
    margin: 0.25rem 0 !important;
  }

  .mobile-quick-info .alert-heading {
    font-size: 0.85rem !important;
    margin-bottom: 0.5rem !important;
  }

  .mobile-quick-info .row.g-2 > * {
    padding: 0.2rem !important;
    font-size: 0.75rem !important;
  }
}

@media (max-width: 575.98px) {
  /* Mobile Action Buttons - Daha spesifik düzenlemeler */
  .mobile-action-buttons .btn {
    font-size: 0.75rem !important;
    padding: 0.4rem 0.2rem !important;
    height: 36px !important;
    min-height: 36px !important;
    border-radius: 20px !important;
  }

  .mobile-action-buttons .favorite-btn-detail {
    height: 36px !important;
    min-height: 36px !important;
    padding: 0.4rem 0.2rem !important;
    border-radius: 20px !important;
  }

  .mobile-quick-info .alert-heading {
    font-size: 0.8rem !important;
  }

  .mobile-quick-info .row.g-2 > * {
    font-size: 0.7rem !important;
  }
}

/* Product Detail Container */
.product-detail-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25px;
  padding: 2rem;
  margin: 0 auto 2rem auto;
  box-shadow: 0 20px 40px rgba(34, 139, 34, 0.15);
  border: 2px solid var(--light-green);
  backdrop-filter: blur(15px);
  max-width: 1100px;
}

/* Professional Image Gallery Styles */
.product-gallery {
  position: relative;
  background: white;
  border-radius: 20px;
  padding: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 2px solid var(--light-green);
}

.main-image-container {
  position: relative;
  background: #f8f9fa;
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 0.5rem;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-product-image {
  width: 100%;
  height: 400px;
  object-fit: contain;
  transition: transform 0.3s ease;
  cursor: zoom-in;
}

.main-product-image:hover {
  transform: scale(1.05);
}

/* Mobil için ürün detay sayfası düzeltmeleri */
@media (max-width: 767.98px) {
  .product-detail-container {
    padding: 0.75rem !important;
    margin: 0.25rem !important;
    border-radius: 15px !important;
  }

  .main-product-image {
    height: 250px !important;
  }

  .main-image-container {
    min-height: 250px !important;
    margin-bottom: 0.5rem !important;
  }

  .product-gallery {
    padding: 0.5rem !important;
    margin-bottom: 0.75rem !important;
  }
}

@media (max-width: 575.98px) {
  .product-detail-container {
    margin: 0.1rem !important;
    padding: 0.5rem !important;
  }

  .main-product-image {
    height: 220px !important;
  }

  .main-image-container {
    min-height: 220px !important;
  }
}

/* --- HEADER TEK SATIRDA KALSIN (MASAÜSTÜ) --- */
@media (min-width: 992px) {
  .navbar-nav {
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 2.2rem;
    align-items: center;
  }
  .navbar-nav .nav-item {
    display: inline-flex !important;
    align-items: center;
    margin-bottom: 0 !important;
  }
  .navbar-nav .nav-link {
    white-space: nowrap;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  .auth-links-group {
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 1.5rem;
    align-items: center;
  }
}

.navbar .container {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  overflow-x: hidden !important;
}
.navbar-brand {
  margin: 0 !important;
  padding: 0 0 0 1rem !important;
  flex-shrink: 0 !important;
}


.auth-links-group #logoutLink {
  padding: 0.2rem 0.6rem !important;
  font-size: 0.8rem !important;
  margin-left: 3.5rem;
  border-radius: 0.5rem;
  border: 1.5px solid var(--primary-green);
  background: #fff;
  color: var(--primary-green) !important;
  font-weight: 500;
  box-shadow: 0 1px 4px rgba(34,139,34,0.06);
  min-width: 70px;
  text-align: center;
}
.auth-links-group #logoutLink:hover {
  background-color: var(--light-green);
  color: var(--dark-green) !important;
  border-color: var(--accent-green);
  box-shadow: 0 4px 16px rgba(34,139,34,0.10);
  transform: translate3d(0, -2px, 0) scale(1.04);
}
#adminLink {
  background: var(--light-green) !important;
  border: 1.5px solid var(--primary-green) !important;
  color: var(--primary-green) !important;
  border-radius: 0.5rem !important;
  padding: 0.2rem 0.6rem !important;
  font-size: 0.8rem !important;
  font-weight: 500;
  transition: all 0.25s cubic-bezier(.4,0,.2,1);
  box-shadow: 0 1px 4px rgba(34,139,34,0.06);
  min-width: 70px;
  text-align: center;
}
#adminLink:hover {
  background: var(--primary-green) !important;
  color: #fff !important;
  border-color: var(--primary-green) !important;
}
/* --- END NAVBAR SPACING & BUTTON FIXES --- */

/* --- FAVORİLER BUTONU TAŞMA ÖNLEMESİ --- */
.auth-links-group .btn-outline-danger {
  padding: 0.2rem 0.6rem !important;
  font-size: 0.8rem !important;
  min-width: 75px;
  max-width: 90px;
  white-space: nowrap;
  overflow: visible;
  text-overflow: unset;
  margin-right: 0 !important;
  text-align: center;
  border-radius: 0.5rem;
  position: relative;
}
@media (max-width: 991.98px) {
  .auth-links-group .btn-outline-danger {
    max-width: 95px;
    font-size: 0.82rem !important;
    padding: 0.2rem 0.6rem !important;
    overflow: visible;
  }
}
@media (max-width: 575.98px) {
  .auth-links-group .btn-outline-danger {
    max-width: 85px;
    font-size: 0.80rem !important;
    padding: 0.2rem 0.5rem !important;
    overflow: visible;
  }
}
/* --- END FAVORİLER BUTONU TAŞMA ÖNLEMESİ --- */

/* --- PROFESSIONAL HAMBURGER MENU DESIGN --- */

/* Modern Hamburger Button */
.navbar-toggler {
  border: none !important;
  padding: 0 !important;
  width: 32px;
  height: 32px;
  position: relative;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
}

.navbar-toggler:focus {
  box-shadow: none !important;
  outline: none !important;
}

/* Modern Menu Button - Force Display */
.navbar-toggler {
  display: flex !important;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  outline: none;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 10000 !important;
}

/* Mobile Menu Overlay */
/* Completely override Bootstrap navbar collapse for mobile */
@media (max-width: 991.98px) {
  .navbar-collapse,
  .navbar-expand-lg .navbar-collapse {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    max-width: none !important;
    max-height: none !important;
    background: linear-gradient(135deg, rgba(44, 85, 48, 0.98), rgba(44, 85, 48, 0.95)) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    z-index: 99999 !important;
    transform: translateX(-100%) !important;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    display: none !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: center !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    box-shadow: none !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
  }
}

@media (max-width: 991.98px) {
  .navbar-collapse.show,
  .navbar-expand-lg .navbar-collapse.show {
    display: flex !important;
    transform: translateX(0) !important;
  }
}

/* Prevent body scroll when mobile menu is open */
body.mobile-menu-open {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
}

/* Independent Mobile Menu - Side panel from left */
.independent-mobile-menu {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 320px !important;
  height: 100vh !important;
  background: linear-gradient(135deg, rgba(44, 85, 48, 0.98), rgba(44, 85, 48, 0.95)) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  z-index: 2147483647 !important;
  transform: translateX(-100%) !important;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3) !important;
  border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.independent-mobile-menu.show {
  transform: translateX(0) !important;
}

/* Mobile menu backdrop for side panel */
.mobile-menu-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.5) !important;
  z-index: 2147483646 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: all 0.3s ease !important;
}

.mobile-menu-backdrop.show {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Mobile Menu Container for Side Panel */
@media (max-width: 991.98px) {
  .mobile-menu-container {
    width: 100% !important;
    height: 100% !important;
    padding: 2rem 1.5rem !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;
    position: relative !important;
    z-index: 2147483647 !important;
    text-align: left !important;
  }
}

/* Mobile Menu Header for Side Panel */
@media (max-width: 991.98px) {
  .mobile-menu-header {
    margin-bottom: 2rem !important;
    padding-bottom: 1.5rem !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    width: 100% !important;
    text-align: left !important;
  }
}

.mobile-menu-header .brand-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.mobile-menu-header .brand-line-1 {
  font-size: 2.5rem;
  font-weight: 800;
  color: white;
}

.mobile-menu-header .brand-line-2 {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.2em;
}

/* Mobile Navigation Links for Side Panel */
.mobile-nav-links {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
  width: 100%;
}

.mobile-nav-links .nav-item {
  margin-bottom: 1rem;
  width: 100%;
}

.mobile-nav-links .nav-link {
  font-size: 1rem !important;
  font-weight: 600 !important;
  color: white !important;
  text-decoration: none !important;
  padding: 0.8rem 1rem !important;
  border-radius: 8px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: block !important;
  position: relative !important;
  overflow: hidden !important;
  width: 100% !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6) !important;
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  margin-bottom: 0.5rem !important;
}

.mobile-nav-links .nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.mobile-nav-links .nav-link:hover::before {
  left: 100%;
}

.mobile-nav-links .nav-link:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  color: white !important;
  transform: translateX(5px) !important;
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 1) !important;
}

/* Mobile User Section */
.mobile-user-section {
  margin-top: auto;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
}

.mobile-user-greeting {
  display: block !important;
  font-size: 1rem;
  color: var(--dark-green);
  font-weight: 600;
  margin-bottom: 1.5rem;
  padding: 0.75rem 1rem;
  background: rgba(34, 139, 34, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(34, 139, 34, 0.1);
  text-align: left;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  animation: fadeInUp 0.5s ease-out;
  width: 100%;
}

.mobile-auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  width: 100%;
}

.mobile-auth-button {
  padding: 0.6rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.mobile-auth-button.primary {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-green);
  border: 2px solid rgba(255, 255, 255, 0.9);
}

.mobile-auth-button.primary:hover {
  background: white;
  border-color: white;
  color: var(--primary-green);
  transform: translate3d(0, -2px, 0);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.mobile-auth-button.secondary {
  background: transparent !important;
  color: white !important;
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8) !important;
}

.mobile-auth-button.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-color: rgba(255, 255, 255, 0.8);
  transform: translate3d(0, -2px, 0);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.mobile-favorites-button {
  padding: 0.8rem 1rem !important;
  font-size: 1rem !important;
  font-weight: 700 !important;
  border-radius: 8px !important;
  text-decoration: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 0.5rem !important;
  background: transparent !important;
  color: #ff6b6b !important;
  border: 2px solid rgba(220, 53, 69, 0.8) !important;
  position: relative !important;
  overflow: hidden !important;
  width: 100% !important;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8) !important;
}

.mobile-favorites-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.mobile-favorites-button:hover::before {
  left: 100%;
}

.mobile-favorites-button:hover {
  background: rgba(220, 53, 69, 0.2);
  border-color: #dc3545;
  color: white;
  transform: translate3d(0, -2px, 0);
  box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
}

/* Mobile Menu Footer */
.mobile-menu-footer {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  text-align: left;
  width: 100%;
}

.mobile-menu-footer p {
  color: white;
  font-size: 0.8rem;
  opacity: 0.8;
  margin: 0;
}

/* Close Button for Side Panel */
@media (max-width: 991.98px) {
  .mobile-menu-close {
    position: absolute !important;
    top: 1.5rem !important;
    right: 1.5rem !important;
    width: 40px !important;
    height: 40px !important;
    border: none !important;
    background: rgba(255, 255, 255, 0.2) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 2147483647 !important;
  }
}

.mobile-menu-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

.mobile-menu-close::before,
.mobile-menu-close::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-close::before {
  transform: rotate(45deg);
}

.mobile-menu-close::after {
  transform: rotate(-45deg);
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .navbar-collapse {
    width: 100%;
    max-width: 320px;
  }
  
  .navbar-toggler {
    margin-right: 1.5rem;
    padding: 0.35rem 0.5rem;
    max-width: 100px;
  }
  
  .navbar-toggler i {
    font-size: 1rem;
    margin-right: 0.3rem;
  }
  
  .navbar-toggler .menu-text {
    font-size: 0.8rem;
  }
  
  .mobile-menu-header .brand-line-1 {
    font-size: 1.8rem;
  }
  
  .mobile-menu-header .brand-line-2 {
    font-size: 0.9rem;
  }
  
  .mobile-nav-links .nav-link {
    font-size: 1rem;
    padding: 0.6rem 1rem;
  }
  
  .mobile-auth-button {
    padding: 0.5rem 0.8rem;
    font-size: 0.85rem;
  }
  
  .mobile-favorites-button {
    padding: 0.5rem 0.8rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 575.98px) {
  .navbar-collapse {
    width: 100%;
    max-width: 300px;
  }
  
  .navbar-toggler {
    margin-right: 1rem;
    padding: 0.3rem 0.4rem;
    max-width: 90px;
  }
  
  .navbar-toggler i {
    font-size: 0.9rem;
    margin-right: 0.25rem;
  }
  
  .navbar-toggler .menu-text {
    font-size: 0.75rem;
  }
  
  .mobile-menu-header .brand-line-1 {
    font-size: 1.6rem;
  }
  
  .mobile-menu-header .brand-line-2 {
    font-size: 0.8rem;
  }
  
  .mobile-nav-links .nav-link {
    font-size: 0.9rem;
    padding: 0.5rem 0.8rem;
  }
  
  .mobile-auth-button {
    padding: 0.4rem 0.7rem;
    font-size: 0.8rem;
  }
  
  .mobile-favorites-button {
    padding: 0.4rem 0.7rem;
    font-size: 0.8rem;
  }
}

/* Animation for menu items - PERFORMANCE OPTIMIZED */
.mobile-nav-links .nav-item {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  animation: none;
}

/* Mobile Dropdown Styles */
.mobile-dropdown {
  position: relative;
}

.mobile-dropdown-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.mobile-dropdown-menu {
  display: none;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  margin: 0.5rem 0;
  padding: 0.5rem 0;
  border-left: 3px solid var(--primary-green);
}

.mobile-dropdown-menu.show {
  display: block;
  animation: slideDown 0.3s ease-out;
}

.mobile-dropdown-menu .nav-link {
  padding: 0.5rem 1.5rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  border-left: none;
}

.mobile-dropdown-menu .nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translate3d(0, -10px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Desktop Dropdown Styles */
.dropdown-menu {
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
}

.dropdown-item {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
  color: white;
  transform: translateX(5px);
}

.dropdown-item i {
  width: 20px;
}

/* User Greeting Responsive Styles */
.navbar-user-greeting {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--primary-green) !important;
  white-space: nowrap;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-flex !important;
  align-items: center;
}

.navbar-user-info {
  flex-wrap: nowrap;
  gap: 0.5rem;
}

.auth-links-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: nowrap;
}

.auth-link {
  font-size: 0.9rem;
  white-space: nowrap;
}

/* Responsive adjustments for user info */
@media (max-width: 1200px) {
  .navbar-user-greeting {
    max-width: 120px;
    font-size: 0.85rem;
  }

  .auth-link {
    font-size: 0.85rem;
  }
}

@media (max-width: 992px) {
  .navbar-user-greeting {
    max-width: 100px;
    font-size: 0.8rem;
  }

  .auth-links-group {
    gap: 0.5rem;
  }

  .auth-link {
    font-size: 0.8rem;
  }
}

/* Mobile user greeting */
.mobile-user-greeting {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin: 1rem 0;
  text-align: center;
  font-weight: 500;
  color: white;
}

.mobile-nav-links .nav-item:nth-child(1) { animation-delay: 0s; }
.mobile-nav-links .nav-item:nth-child(2) { animation-delay: 0s; }
.mobile-nav-links .nav-item:nth-child(3) { animation-delay: 0s; }
.mobile-nav-links .nav-item:nth-child(4) { animation-delay: 0s; }

/* Hide desktop menu on mobile */
@media (max-width: 991.98px) {
  .navbar {
    overflow: visible !important;
    position: relative !important;
  }

  .navbar-nav.mx-auto {
    display: none !important;
  }

  .navbar-user-info {
    display: none !important;
  }
  
  /* Ensure hamburger menu button is visible on mobile */
  .navbar-toggler {
    display: flex !important;
    z-index: 10001 !important;
    position: relative !important;
  }
  
  /* Override Bootstrap's default behavior for mobile */
  .navbar-expand-lg .navbar-toggler {
    display: flex !important;
  }

  /* Force visibility on all mobile breakpoints */
  .navbar-expand-lg .navbar-toggler,
  .navbar-expand-md .navbar-toggler,
  .navbar-expand-sm .navbar-toggler {
    display: flex !important;
  }

  /* Completely override Bootstrap navbar collapse behavior */
  .navbar-expand-lg .navbar-collapse,
  .navbar-expand-md .navbar-collapse,
  .navbar-expand-sm .navbar-collapse,
  .navbar-expand .navbar-collapse {
    display: none !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 99999 !important;
  }

  .navbar-expand-lg .navbar-collapse.show,
  .navbar-expand-md .navbar-collapse.show,
  .navbar-expand-sm .navbar-collapse.show,
  .navbar-expand .navbar-collapse.show {
    display: flex !important;
  }
}

/* Show mobile menu only on mobile */
@media (min-width: 992px) {
  .navbar-toggler {
    display: none !important;
  }
  
  /* Override Bootstrap's default behavior */
  .navbar-expand-lg .navbar-toggler {
    display: none !important;
  }
  
  .navbar-collapse {
    position: static !important;
    width: auto !important;
    height: auto !important;
    background: transparent !important;
    backdrop-filter: none !important;
    transform: none !important;
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 0 !important;
    z-index: auto !important;
  }
  
  .mobile-menu-container {
    display: none !important;
  }
}

/* --- END PROFESSIONAL HAMBURGER MENU DESIGN --- */

/* --- ADMIN PANEL IMAGE PREVIEW STYLES --- */

/* Images Preview Container */
.images-preview-container {
  margin-top: 1rem !important;
  padding: 1.25rem !important;
  border: 2px dashed #90ee90 !important;
  border-radius: 12px !important;
  background: rgba(248, 250, 252, 0.5) !important;
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 1rem !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  min-height: 120px !important;
  transition: all 0.3s ease !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.images-preview-container:hover {
  border-color: var(--primary-green) !important;
  background: rgba(240, 255, 240, 0.3) !important;
}

/* Image Preview Item */
.image-preview-item {
  position: relative !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  flex-shrink: 0 !important;
  margin-bottom: 10px !important;
}

.image-preview-item:hover {
  transform: translate3d(0, -2px, 0) !important;
}

/* Image Preview */
.image-preview {
  width: 90px !important;
  height: 90px !important;
  object-fit: cover !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  border: 2px solid #90ee90 !important;
  border-radius: 8px !important;
  display: block !important;
}

.image-preview:hover {
  transform: scale(1.05) !important;
  border-color: var(--primary-green) !important;
}

/* Main Image Styling */
.image-preview.main-image {
  border: 3px solid var(--primary-green) !important;
  box-shadow: 0 0 10px rgba(34, 139, 34, 0.3) !important;
}

/* Image Remove Button */
.image-remove-btn {
  position: absolute !important;
  top: -5px !important;
  right: -5px !important;
  width: 24px !important;
  height: 24px !important;
  border: none !important;
  background: #dc3545 !important;
  color: white !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  font-size: 0.7rem !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3) !important;
  z-index: 10 !important;
}

.image-remove-btn:hover {
  background: #c82333 !important;
  transform: scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4) !important;
}

/* Main Image Badge */
.main-image-badge {
  position: absolute !important;
  bottom: 2px !important;
  left: 2px !important;
  background: var(--primary-green) !important;
  color: white !important;
  font-size: 0.6rem !important;
  font-weight: 700 !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  z-index: 5 !important;
}

/* Upload Progress */
.upload-progress {
  margin-top: 0.5rem !important;
}

.progress-bar-custom {
  background: linear-gradient(45deg, var(--primary-green), var(--accent-green)) !important;
  transition: width 0.3s ease !important;
}

/* Responsive Adjustments for Image Preview */
@media (max-width: 768px) {
  .image-preview-item {
    width: 80px !important;
    height: 80px !important;
  }
  
  .images-preview-container {
    gap: 0.5rem !important;
    padding: 0.75rem !important;
    min-height: 100px !important;
  }
  
  .image-remove-btn {
    width: 20px !important;
    height: 20px !important;
    font-size: 0.6rem !important;
  }
  
  .main-image-badge {
    font-size: 0.5rem !important;
    padding: 1px 4px !important;
  }
}

@media (max-width: 480px) {
  .image-preview-item {
    width: 60px !important;
    height: 60px !important;
  }
  
  .images-preview-container {
    min-height: 80px !important;
  }
}

/* --- END ADMIN PANEL IMAGE PREVIEW STYLES --- */

/* ===== WHATSAPP BUTTON STYLES ===== */
.whatsapp-btn {
  background: linear-gradient(45deg, #25d366, #128c7e) !important;
  border: none !important;
  color: white !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3) !important;
  text-decoration: none !important;
}

.whatsapp-btn:hover {
  background: linear-gradient(45deg, #128c7e, #075e54) !important;
  transform: translate3d(0, -3px, 0) !important;
  box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4) !important;
  color: white !important;
  text-decoration: none !important;
}

.whatsapp-btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25) !important;
  color: white !important;
}

.whatsapp-btn i {
  animation: whatsappPulse 2s ease-in-out infinite;
}

@keyframes whatsappPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Instagram Button Styles */
.instagram-btn {
  background: linear-gradient(45deg, #e4405f, #833ab4, #c13584) !important;
  border: none !important;
  color: white !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 15px rgba(228, 64, 95, 0.3) !important;
  text-decoration: none !important;
}

.instagram-btn:hover {
  background: linear-gradient(45deg, #c13584, #833ab4, #e4405f) !important;
  transform: translate3d(0, -3px, 0) !important;
  box-shadow: 0 8px 25px rgba(228, 64, 95, 0.4) !important;
  color: white !important;
  text-decoration: none !important;
}

.instagram-btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(228, 64, 95, 0.25) !important;
  color: white !important;
}

.instagram-btn i {
  animation: instagramPulse 2s ease-in-out infinite;
}

@keyframes instagramPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Social buttons responsive */
@media (max-width: 768px) {
  .whatsapp-btn,
  .instagram-btn {
    font-size: 0.9rem !important;
    padding: 0.75rem 1rem !important;
  }

  .whatsapp-btn i,
  .instagram-btn i {
    font-size: 1.2rem !important;
  }

  .theme-btn {
    font-size: 0.9rem !important;
    padding: 0.75rem 1rem !important;
  }
}

/* Theme Button - Ana tema yeşil gradient */
.theme-btn {
  background: linear-gradient(45deg, var(--primary-green), var(--accent-green)) !important;
  border: none !important;
  color: white !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3) !important;
  text-decoration: none !important;
}

.theme-btn:hover {
  background: linear-gradient(45deg, var(--secondary-green), var(--primary-green)) !important;
  transform: translate3d(0, -3px, 0) !important;
  box-shadow: 0 8px 25px rgba(44, 85, 48, 0.4) !important;
  color: white !important;
  text-decoration: none !important;
}

.theme-btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25) !important;
  color: white !important;
}

/* ===== BLOG DETAIL SHARE BUTTONS ===== */
.share-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(255,255,255,0.95);
  border-radius: 15px;
  border: 1px solid rgba(76, 175, 80, 0.2);
  flex-wrap: wrap;
}

.share-buttons h6 {
  margin: 0;
  color: var(--primary-green);
  font-weight: 600;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.share-btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  text-decoration: none;
  color: white;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(0,0,0,0.2);
  flex-shrink: 0;
  line-height: 1 !important;
  text-align: center !important;
  position: relative;
}

.share-btn i {
  line-height: 1 !important;
  font-style: normal !important;
  display: block !important;
  width: 100% !important;
  text-align: center !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  padding: 0 !important;
}

.share-btn:hover {
  transform: translate3d(0, -3px, 0);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
  color: white;
  text-decoration: none;
}

.share-btn.facebook { background: #3b5998; }
.share-btn.twitter { background: #1da1f2; }
.share-btn.linkedin { background: #0077b5; }
.share-btn.whatsapp { background: #25d366; }
.share-btn.copy { background: #6c757d; }

/* Mobile responsive */
@media (max-width: 768px) {
  .share-buttons {
    justify-content: center;
    text-align: center;
    padding: 1rem;
  }

  .share-buttons h6 {
    width: 100%;
    margin-bottom: 1rem;
    margin-right: 0;
    justify-content: center;
  }

  .share-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
    line-height: 1 !important;
  }

  .share-btn i {
    font-size: 1rem !important;
  }
}

@media (max-width: 576px) {
  .share-buttons {
    gap: 0.75rem;
    padding: 0.75rem;
  }

  .share-btn {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
    line-height: 1 !important;
  }

  .share-btn i {
    font-size: 0.9rem !important;
  }
}

/* Modern Kategori Filtreleme Tasarımı - Ana Temaya Uygun */
.category-filters {
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: center !important;
  gap: 12px !important;
  padding: 20px !important;
  background: linear-gradient(135deg, var(--bg-green) 0%, rgba(144, 238, 144, 0.1) 100%) !important;
  border-radius: 20px !important;
  box-shadow: 0 8px 32px rgba(44, 85, 48, 0.1) !important;
  margin-bottom: 2rem !important;
  border: 1px solid rgba(144, 238, 144, 0.3) !important;
}

.category-btn {
  position: relative !important;
  background: rgba(255, 255, 255, 0.95) !important;
  border: 2px solid rgba(44, 85, 48, 0.2) !important;
  border-radius: 16px !important;
  padding: 12px 20px !important;
  font-weight: 600 !important;
  font-size: 0.95rem !important;
  color: var(--primary-green) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 4px 16px rgba(44, 85, 48, 0.1) !important;
  text-decoration: none !important;
  overflow: hidden !important;
  min-width: 120px !important;
  text-align: center !important;
}

.category-btn::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(144, 238, 144, 0.4), transparent) !important;
  transition: left 0.6s !important;
}

.category-btn:hover::before {
  left: 100% !important;
}

.category-btn:hover {
  transform: translate3d(0, -3px, 0) scale(1.02) !important;
  box-shadow: 0 12px 32px rgba(44, 85, 48, 0.25) !important;
  border-color: var(--accent-green) !important;
  background: rgba(255, 255, 255, 1) !important;
  color: var(--secondary-green) !important;
}

.category-btn.active {
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 100%) !important;
  border-color: var(--primary-green) !important;
  color: white !important;
  transform: translate3d(0, -2px, 0) !important;
  box-shadow: 0 8px 24px rgba(44, 85, 48, 0.4) !important;
}

.category-btn.active:hover {
  transform: translate3d(0, -4px, 0) scale(1.02) !important;
  box-shadow: 0 16px 40px rgba(44, 85, 48, 0.5) !important;
  color: white !important;
  background: linear-gradient(135deg, var(--secondary-green) 0%, var(--dark-green) 100%) !important;
}

.category-btn .badge {
  font-size: 0.7rem !important;
  font-weight: 700 !important;
  padding: 4px 8px !important;
  border-radius: 12px !important;
  margin-left: 8px !important;
  background: rgba(44, 85, 48, 0.1) !important;
  color: var(--primary-green) !important;
  border: 1px solid rgba(44, 85, 48, 0.2) !important;
  transition: all 0.3s ease !important;
}

.category-btn.active .badge {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

.category-btn i {
  margin-right: 8px !important;
  font-size: 1rem !important;
  transition: transform 0.3s ease !important;
  color: inherit !important;
}

.category-btn:hover i {
  transform: scale(1.1) !important;
}

/* Responsive Tasarım */
@media (max-width: 992px) {
  .category-filters {
    gap: 10px !important;
    padding: 16px !important;
  }

  .category-btn {
    padding: 10px 16px !important;
    font-size: 0.9rem !important;
    min-width: 100px !important;
  }
}

@media (max-width: 768px) {
  .category-filters {
    gap: 8px !important;
    padding: 12px !important;
    border-radius: 16px !important;
  }

  .category-btn {
    padding: 8px 14px !important;
    font-size: 0.85rem !important;
    min-width: 90px !important;
    border-radius: 12px !important;
  }

  .category-btn .badge {
    font-size: 0.65rem !important;
    padding: 2px 6px !important;
    margin-left: 6px !important;
  }
}

@media (max-width: 576px) {
  .category-filters {
    gap: 6px !important;
    padding: 10px !important;
    margin: 0 10px 2rem 10px !important;
  }

  .category-btn {
    padding: 6px 12px !important;
    font-size: 0.8rem !important;
    min-width: 80px !important;
    border-radius: 10px !important;
  }

  .category-btn i {
    font-size: 0.9rem !important;
    margin-right: 6px !important;
  }

  .category-btn .badge {
    font-size: 0.6rem !important;
    padding: 2px 5px !important;
    margin-left: 4px !important;
  }
}

/* Çok küçük ekranlar için */
@media (max-width: 400px) {
  .category-btn {
    min-width: 70px !important;
    padding: 5px 10px !important;
    font-size: 0.75rem !important;
  }

  .category-btn i {
    display: none !important;
  }
}

/* Kartların tutarlı boyutta olması için - Bootstrap row-cols sistemini override et */
.products-container.row > .col {
  flex: 0 0 320px !important;
  width: 320px !important;
  max-width: 320px !important;
  min-width: 320px !important;
}

/* Kartların içeriğinin de tutarlı olması için */
.products-container .product-card {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.products-container .card-body {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
}

.products-container .card-body .btn {
  margin-top: auto !important;
}

/* Tek ürün olduğunda ortalanması için */
.products-container {
  justify-content: center !important;
}

/* Tablet boyutunda */
@media (max-width: 992px) {
  .products-container.row > .col {
    flex: 0 0 280px !important;
    width: 280px !important;
    max-width: 280px !important;
    min-width: 280px !important;
  }
}

/* Mobil boyutunda - 2'li sıralama */
@media (max-width: 768px) {
  .products-container.row > .col {
    flex: 0 0 calc(50% - 10px) !important;
    width: calc(50% - 10px) !important;
    max-width: calc(50% - 10px) !important;
    min-width: 200px !important;
  }

  /* Tek ürün olduğunda sabit genişlik */
  .products-container.row > .col:only-child {
    flex: 0 0 250px !important;
    width: 250px !important;
    max-width: 250px !important;
    min-width: 250px !important;
  }
}

/* Küçük mobil boyutunda - 2'li sıralama devam */
@media (max-width: 576px) {
  .products-container.row > .col {
    flex: 0 0 calc(50% - 8px) !important;
    width: calc(50% - 8px) !important;
    max-width: calc(50% - 8px) !important;
    min-width: 150px !important;
  }

  /* Tek ürün olduğunda sabit genişlik */
  .products-container.row > .col:only-child {
    flex: 0 0 220px !important;
    width: 220px !important;
    max-width: 220px !important;
    min-width: 220px !important;
  }
}