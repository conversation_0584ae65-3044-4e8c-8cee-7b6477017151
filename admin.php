<?php
session_start();

// MUTLAK GÜVENLİK KONTROLÜ - HİÇBİR ŞEY YAPILMADAN ÖNCE
require_once 'config/database.php';
require_once 'includes/functions.php';

// Basit test - session var mı?
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php?error=no_session');
    exit();
}

// Kullanıcı veritabanında var mı ve admin mi?
$database = new Database();
$db = $database->getConnection();
$user = getCurrentUser($db);

if (!$user) {
    header('Location: index.php?error=user_not_found');
    exit();
}

if ($user['role'] !== 'admin') {
    header('Location: index.php?error=not_admin&role=' . $user['role']);
    exit();
}

// Admin sayfası için özel CSP ayarları - SADECE ADMİN İSE
header("Content-Security-Policy: " .
    "default-src 'self'; " .
    "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; " .
    "style-src-elem 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; " .
    "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
    "img-src 'self' data: https: http: blob:; " .
    "connect-src 'self' https: http:; " .
    "media-src 'self' data: https: http: blob:;"
);
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Paneli - Tansu Şahal Salamura</title>
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="images/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="images/apple-touch-icon.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <link rel="stylesheet" href="styles.css?v=2.0.1">
    
    <style>
    /* INLINE CSS - EN GÜÇLÜ ÇÖZÜM */

    /* Alternative Font Stack - No External Dependencies */
    * {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
    }

    /* FontAwesome Fallback - Ensure icons are visible */
    .fas, .far, .fab, .fal, .fad {
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "FontAwesome", sans-serif !important;
        font-weight: 900 !important;
        font-style: normal !important;
        font-variant: normal !important;
        text-transform: none !important;
        line-height: 1 !important;
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
    }

    /* Icon visibility fix */
    i[class*="fa-"] {
        display: inline-block !important;
        font-style: normal !important;
        font-variant: normal !important;
        text-rendering: auto !important;
        -webkit-font-smoothing: antialiased !important;
    }

    /* Unicode fallback for critical icons */
    .fas.fa-cogs::before { content: "⚙️"; }
    .fas.fa-box::before { content: "📦"; }
    .fas.fa-warehouse::before { content: "🏪"; }
    .fas.fa-plus-circle::before { content: "➕"; }
    .fas.fa-tag::before { content: "🏷️"; }
    .fas.fa-folder::before { content: "📁"; }
    .fas.fa-save::before { content: "💾"; }
    .fas.fa-edit::before { content: "✏️"; }
    .fas.fa-trash::before { content: "🗑️"; }
    .fas.fa-eye::before { content: "👁️"; }
    .fas.fa-heart::before { content: "❤️"; }
    .fas.fa-bars::before { content: "☰"; }
    .fas.fa-times::before { content: "✖️"; }
    .fas.fa-check::before { content: "✅"; }
    .fas.fa-exclamation-triangle::before { content: "⚠️"; }

    /* Blog İşlemler Bölümü Düzenlemesi */
    .table td .btn-group {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 0 !important;
    }

    .table td .btn-group .btn {
        border-radius: 0 !important;
        border-right: none !important;
        min-width: 40px !important;
        height: 32px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 0.375rem 0.5rem !important;
    }

    .table td .btn-group .btn:first-child {
        border-top-left-radius: 0.375rem !important;
        border-bottom-left-radius: 0.375rem !important;
    }

    .table td .btn-group .btn:last-child {
        border-top-right-radius: 0.375rem !important;
        border-bottom-right-radius: 0.375rem !important;
        border-right: 1px solid !important;
    }

    .table td .btn-group .btn:hover {
        z-index: 2 !important;
        border-right: 1px solid !important;
    }

    /* İşlemler sütunu genişliği */
    .table th:last-child,
    .table td:last-child {
        width: 140px !important;
        min-width: 140px !important;
        max-width: 140px !important;
    }
    
    /* NAVBAR KORUMA */
    .navbar,
    .navbar *,
    .navbar-toggler,
    .navbar-collapse,
    .navbar-nav,
    .navbar-brand,
    .nav-link,
    .collapse,
    .collapsing,
    .show,
    .mobile-menu-container,
    .mobile-nav-links,
    .mobile-user-section {
        /* Navbar elementlerine dokunma */
    }
    
    /* ADMIN CONTAINER */
    .admin-container {
        display: block !important;
        width: 100% !important;
        max-width: 1400px !important;
        margin: 2rem auto !important;
        padding: 0 1rem !important;
        position: relative !important;
        z-index: 2 !important;
    }
    
    /* ADMIN HEADER */
    .admin-header {
        display: block !important;
        width: 100% !important;
        background: linear-gradient(135deg, #228b22 0%, #2e8b57 100%) !important;
        color: white !important;
        padding: 2rem !important;
        border-radius: 20px !important;
        margin-bottom: 2rem !important;
        box-shadow: 0 10px 30px rgba(34, 139, 34, 0.2) !important;
        text-align: center !important;
    }
    
    .admin-header h1 {
        font-size: 2.5rem !important;
        font-weight: 800 !important;
        margin: 0 !important;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3) !important;
    }
    
    /* STATS CONTAINER - ZORLA DİKEY */
    .stats-container-vertical {
        display: block !important;
        width: 100% !important;
        margin: 0 0 2rem 0 !important;
        padding: 0 !important;
    }
    
    .stat-card-vertical {
        display: block !important;
        width: 100% !important;
        max-width: 100% !important;
        padding: 1.5rem !important;
        background: rgba(255, 255, 255, 0.95) !important;
        border-radius: 15px !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
        border: 2px solid #90ee90 !important;
        transition: all 0.3s ease !important;
        margin: 0 0 1.5rem 0 !important;
        float: none !important;
        clear: both !important;
    }
    
    /* STAT CARD İÇERİKLERİ */
    .stat-icon {
        width: 60px !important;
        height: 60px !important;
        border-radius: 12px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 1.5rem !important;
        color: white !important;
        margin-bottom: 1rem !important;
    }
    
    .stat-icon.products {
        background: linear-gradient(45deg, #2563eb, #3b82f6) !important;
    }
    
    .stat-icon.stock {
        background: linear-gradient(45deg, #059669, #10b981) !important;
    }
    
    .stat-number {
        font-size: 2rem !important;
        font-weight: 800 !important;
        color: #1f2937 !important;
        margin: 0 !important;
    }
    
    .stat-label {
        color: #6b7280 !important;
        font-size: 0.9rem !important;
        font-weight: 500 !important;
        margin: 0 !important;
    }
    
    /* FORM SECTION */
    .form-section {
        display: block !important;
        width: 100% !important;
        margin: 2rem 0 !important;
        padding: 2rem !important;
        background: rgba(255, 255, 255, 0.95) !important;
        border-radius: 20px !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
        border: 2px solid #90ee90 !important;
    }
    
    /* FORM ROW VE COL'LARI DİKEY YAP */
    .form-row-vertical {
        display: block !important;
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .form-col-vertical {
        display: block !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 0 1rem 0 !important;
        padding: 0 !important;
        float: none !important;
    }
    
    /* TABLE CONTAINER */
    .table-container {
        display: block !important;
        width: 100% !important;
        margin: 2rem 0 !important;
        padding: 2rem !important;
        background: rgba(255, 255, 255, 0.95) !important;
        border-radius: 20px !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
        border: 2px solid #90ee90 !important;
        overflow-x: auto !important;
    }
    
    /* SECTION BAŞLIKLARI */
    .section-title {
        font-size: 1.5rem !important;
        font-weight: 700 !important;
        color: #2d5016 !important;
        margin-bottom: 1.5rem !important;
        padding-bottom: 0.5rem !important;
        border-bottom: 3px solid #32cd32 !important;
        text-align: left !important;
    }
    
    /* FORM KONTROLLERI */
    .form-control {
        border: 2px solid #e5e7eb !important;
        border-radius: 12px !important;
        padding: 0.75rem 1rem !important;
        transition: all 0.3s ease !important;
        width: 100% !important;
        display: block !important;
    }
    
    .form-control:focus {
        border-color: #228b22 !important;
        box-shadow: 0 0 0 0.2rem rgba(34, 139, 34, 0.25) !important;
    }
    
    /* BUTONLAR */
    .btn-primary {
        background: linear-gradient(45deg, #228b22, #32cd32) !important;
        border: none !important;
        box-shadow: 0 4px 15px rgba(34, 139, 34, 0.3) !important;
        color: white !important;
        padding: 0.75rem 2rem !important;
        border-radius: 10px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(34, 139, 34, 0.4) !important;
    }
    
    /* TABLO STİLLERİ */
    .table {
        width: 100% !important;
        margin-bottom: 0 !important;
        background: white !important;
    }
    
    .table thead th {
        background: linear-gradient(135deg, #228b22, #2e8b57) !important;
        color: white !important;
        font-weight: 600 !important;
        border: none !important;
        padding: 1rem !important;
    }
    
    .table tbody td {
        padding: 1rem !important;
        vertical-align: middle !important;
        border-bottom: 1px solid #e5e7eb !important;
    }
    
    .table tbody tr:hover {
        background: rgba(144, 238, 144, 0.1) !important;
    }
    
    /* RESİM ÖNİZLEME STİLLERİ */
    .images-preview-container {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 15px !important;
        margin-top: 15px !important;
        padding: 20px !important;
        border: 2px dashed #90ee90 !important;
        border-radius: 12px !important;
        min-height: 120px !important;
        align-items: flex-start !important;
        justify-content: flex-start !important;
        background: rgba(248, 250, 252, 0.5) !important;
        transition: all 0.3s ease !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .images-preview-container:hover {
        border-color: #228b22 !important;
        background: rgba(240, 255, 240, 0.3) !important;
    }

    .image-preview-item {
        position: relative !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        flex-shrink: 0 !important;
        margin-bottom: 10px !important;
    }

    .image-preview {
        width: 90px !important;
        height: 90px !important;
        object-fit: cover !important;
        border-radius: 8px !important;
        border: 2px solid #90ee90 !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        display: block !important;
    }

    .image-preview:hover {
        transform: scale(1.05) !important;
        border-color: #228b22 !important;
    }

    .image-preview.main-image {
        border-color: #228b22 !important;
        border-width: 3px !important;
        box-shadow: 0 0 10px rgba(34, 139, 34, 0.3) !important;
    }

    .image-remove-btn {
        position: absolute !important;
        top: -8px !important;
        right: -8px !important;
        background: #dc2626 !important;
        color: white !important;
        border: none !important;
        border-radius: 50% !important;
        width: 24px !important;
        height: 24px !important;
        font-size: 12px !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: all 0.3s ease !important;
        z-index: 10 !important;
        box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3) !important;
    }

    .image-remove-btn:hover {
        background: #b91c1c !important;
        transform: scale(1.1) !important;
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4) !important;
    }

    .main-image-badge {
        position: absolute !important;
        bottom: -8px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        background: #228b22 !important;
        color: white !important;
        font-size: 10px !important;
        font-weight: bold !important;
        padding: 3px 8px !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
        letter-spacing: 0.5px !important;
        text-transform: uppercase !important;
    }

    .upload-progress {
        margin-top: 10px !important;
    }

    .progress-bar-custom {
        background: linear-gradient(45deg, #228b22, #32cd32) !important;
    }

    /* BİLDİRİM SİSTEMİ */
    .notification-container {
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        z-index: 9999 !important;
        max-width: 350px !important;
    }

    .notification-bubble {
        background: linear-gradient(135deg, #228b22 0%, #2e8b57 100%) !important;
        color: white !important;
        padding: 1rem 1.25rem !important;
        border-radius: 15px !important;
        margin-bottom: 0.5rem !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2) !important;
        transform: translateX(400px) !important;
        opacity: 0 !important;
        transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
    }

    .notification-bubble.show {
        transform: translateX(0) !important;
        opacity: 1 !important;
    }

    .notification-bubble.success {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%) !important;
    }

    .notification-bubble.error {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%) !important;
    }

    .notification-bubble.info {
        background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    }

    /* RESPONSIVE */
    @media (max-width: 768px) {
        .admin-container {
            padding: 0 0.5rem !important;
        }
        
        .admin-header {
            padding: 1.5rem !important;
        }
        
        .admin-header h1 {
            font-size: 2rem !important;
        }
        
        .form-section,
        .table-container {
            padding: 1.5rem !important;
        }
        
        .stat-card-vertical {
            padding: 1rem !important;
        }

        .image-preview {
            width: 70px !important;
            height: 70px !important;
        }

        .images-preview-container {
            justify-content: center !important;
            gap: 10px !important;
            padding: 15px !important;
            min-height: 100px !important;
        }

        .image-remove-btn {
            width: 20px !important;
            height: 20px !important;
            font-size: 10px !important;
        }

        .main-image-badge {
            font-size: 8px !important;
            padding: 2px 6px !important;
        }

        .notification-container {
            top: 10px !important;
            right: 10px !important;
            max-width: 280px !important;
        }
    }

    /* Formatting Toolbar Styles */
    .formatting-toolbar {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 8px !important;
        padding: 10px !important;
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 5px !important;
        margin-bottom: 10px !important;
    }

    .formatting-toolbar .btn {
        font-size: 12px !important;
        padding: 5px 8px !important;
        border-radius: 4px !important;
        margin: 0 !important;
    }

    .formatting-toolbar .btn:hover {
        background-color: var(--primary-green) !important;
        border-color: var(--primary-green) !important;
        color: white !important;
        transform: none !important;
        box-shadow: none !important;
    }

    .formatting-toolbar .btn-group {
        margin-bottom: 5px !important;
    }

    #blogContent {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        line-height: 1.6 !important;
        font-size: 14px !important;
    }

    .formatting-toolbar .btn-outline-secondary {
        border-color: #6c757d !important;
        color: #6c757d !important;
    }

    .formatting-toolbar .btn-outline-secondary:hover {
        background-color: #228b22 !important;
        border-color: #228b22 !important;
        color: white !important;
    }

    /* Content Editor with Live Preview */
    .content-editor-container {
        border: 1px solid #dee2e6 !important;
        border-radius: 8px !important;
        overflow: hidden !important;
    }

    .editor-tabs {
        display: flex !important;
        background: #f8f9fa !important;
        border-bottom: 1px solid #dee2e6 !important;
    }

    .tab-btn {
        background: none !important;
        border: none !important;
        padding: 12px 20px !important;
        cursor: pointer !important;
        font-size: 14px !important;
        color: #6c757d !important;
        transition: all 0.3s ease !important;
        border-bottom: 3px solid transparent !important;
    }

    .tab-btn:hover {
        background: #e9ecef !important;
        color: #228b22 !important;
    }

    .tab-btn.active {
        color: #228b22 !important;
        border-bottom-color: #228b22 !important;
        background: white !important;
    }

    .editor-content {
        position: relative !important;
        min-height: 400px !important;
    }

    .editor-pane, .preview-pane {
        display: none !important;
        padding: 0 !important;
    }

    .editor-pane.active, .preview-pane.active {
        display: block !important;
    }

    .editor-pane.split, .preview-pane.split {
        display: block !important;
        width: 50% !important;
        float: left !important;
    }

    .editor-pane textarea {
        border: none !important;
        border-radius: 0 !important;
        resize: vertical !important;
        min-height: 400px !important;
    }

    .preview-pane {
        background: white !important;
        min-height: 400px !important;
        overflow-y: auto !important;
    }

    .preview-content {
        padding: 20px !important;
        line-height: 1.6 !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    }

    /* Preview Content Styling */
    .preview-content h1 {
        color: #228b22 !important;
        font-size: 2rem !important;
        font-weight: 700 !important;
        margin: 1.5rem 0 1rem 0 !important;
        border-bottom: 2px solid #228b22 !important;
        padding-bottom: 0.5rem !important;
    }

    .preview-content h2 {
        color: #2e8b57 !important;
        font-size: 1.5rem !important;
        font-weight: 600 !important;
        margin: 1.25rem 0 0.75rem 0 !important;
    }

    .preview-content h3 {
        color: #32cd32 !important;
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        margin: 1rem 0 0.5rem 0 !important;
    }

    .preview-content p {
        margin: 0.75rem 0 !important;
        text-align: justify !important;
    }

    .preview-content strong {
        color: #228b22 !important;
        font-weight: 700 !important;
    }

    .preview-content em {
        color: #2e8b57 !important;
        font-style: italic !important;
    }

    .preview-content ul, .preview-content ol {
        margin: 1rem 0 !important;
        padding-left: 2rem !important;
    }

    .preview-content li {
        margin: 0.5rem 0 !important;
    }

    .preview-content blockquote {
        background: #f8f9fa !important;
        border-left: 4px solid #228b22 !important;
        margin: 1rem 0 !important;
        padding: 1rem 1.5rem !important;
        font-style: italic !important;
        color: #6c757d !important;
    }

    .preview-content a {
        color: #228b22 !important;
        text-decoration: none !important;
    }

    .preview-content a:hover {
        text-decoration: underline !important;
    }

    /* Tags Preview Styles */
    .tags-preview {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 8px !important;
        margin-top: 10px !important;
    }

    .tag-item {
        background: linear-gradient(45deg, #228b22, #32cd32) !important;
        color: white !important;
        padding: 4px 12px !important;
        border-radius: 20px !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        display: inline-flex !important;
        align-items: center !important;
        text-decoration: none !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 2px 4px rgba(34, 139, 34, 0.2) !important;
    }

    .tag-item:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 8px rgba(34, 139, 34, 0.3) !important;
        color: white !important;
    }

    .tag-item i {
        margin-right: 4px !important;
        font-size: 10px !important;
    }

    /* Category Modal Styles */
    .modal-content {
        border-radius: 15px !important;
        border: none !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
    }

    .modal-header {
        background: linear-gradient(135deg, #228b22, #32cd32) !important;
        color: white !important;
        border-radius: 15px 15px 0 0 !important;
        border-bottom: none !important;
    }

    .modal-header .btn-close {
        filter: invert(1) !important;
    }

    .modal-title {
        font-weight: 600 !important;
    }

    /* Floating Navigation Styles */
    .floating-nav {
        position: fixed !important;
        top: 50% !important;
        right: 20px !important;
        transform: translateY(-50%) !important;
        z-index: 1000 !important;
        transition: all 0.3s ease !important;
    }

    .floating-nav-toggle {
        width: 50px !important;
        height: 50px !important;
        background: linear-gradient(135deg, #228b22, #32cd32) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        cursor: pointer !important;
        box-shadow: 0 4px 15px rgba(34, 139, 34, 0.3) !important;
        transition: all 0.3s ease !important;
        font-size: 18px !important;
    }

    .floating-nav-toggle:hover {
        transform: scale(1.1) !important;
        box-shadow: 0 6px 20px rgba(34, 139, 34, 0.4) !important;
    }

    .floating-nav-menu {
        position: absolute !important;
        right: 60px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        background: white !important;
        border-radius: 15px !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
        min-width: 200px !important;
        opacity: 0 !important;
        visibility: hidden !important;
        transition: all 0.3s ease !important;
        border: 2px solid #90ee90 !important;
    }

    .floating-nav-menu.active {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(-50%) translateX(-10px) !important;
    }

    .floating-nav-header {
        background: linear-gradient(135deg, #228b22, #32cd32) !important;
        color: white !important;
        padding: 12px 15px !important;
        border-radius: 13px 13px 0 0 !important;
        font-weight: 600 !important;
        text-align: center !important;
    }

    .floating-nav-items {
        padding: 10px 0 !important;
    }

    .floating-nav-item {
        display: flex !important;
        align-items: center !important;
        padding: 12px 20px !important;
        color: #333 !important;
        text-decoration: none !important;
        transition: all 0.3s ease !important;
        border-left: 3px solid transparent !important;
    }

    .floating-nav-item:hover {
        background: rgba(34, 139, 34, 0.1) !important;
        border-left-color: #228b22 !important;
        color: #228b22 !important;
        text-decoration: none !important;
    }

    .floating-nav-item i {
        width: 20px !important;
        margin-right: 12px !important;
        text-align: center !important;
        font-size: 14px !important;
    }

    .floating-nav-item span {
        font-size: 14px !important;
        font-weight: 500 !important;
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
        .floating-nav {
            right: 10px !important;
        }

        .floating-nav-menu {
            right: 50px !important;
            min-width: 180px !important;
        }

        .floating-nav-toggle {
            width: 45px !important;
            height: 45px !important;
            font-size: 16px !important;
        }
    }
    </style>
</head>
<body>
    <!-- Floating vegetables background -->
    <div class="floating-veggies">
        <div class="veggie">🥒</div>
        <div class="veggie">🥬</div>
        <div class="veggie">🥦</div>
        <div class="veggie">🌿</div>
        <div class="veggie">🥒</div>
        <div class="veggie">🥒</div>
        <div class="veggie">🥬</div>
        <div class="veggie">🥦</div>
        <div class="veggie">🌿</div>
        <div class="veggie">🥒</div>
    </div>

    <!-- Bildirim Konteyneri -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- Navbar - HAMBURGER MENÜ KORUNMUŞ -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm sticky-top main-navbar">
        <div class="container">
            <a class="navbar-brand fw-bold fs-4 d-flex align-items-center" href="/">
                <img src="images/logo.jpg" alt="Tansu Şahal Salamura Logo" style="height: 50px; margin-right: 10px;">
                <span class="brand-text">
                    <span class="brand-line-1">Tansu Şahal</span>
                    <span class="brand-line-2">Salamura</span>
                </span>
            </a>
            
            <!-- HAMBURGER MENÜ BUTONU - KORUNMUŞ -->
            <button class="navbar-toggler" type="button" onclick="toggleIndependentMobileMenu()" aria-label="Menüyü aç/kapat">
                <span class="navbar-toggler-icon"></span>
                <span class="menu-text">Menü</span>
            </button>
            
            <!-- NAVBAR COLLAPSE - KORUNMUŞ -->
            <div class="collapse navbar-collapse" id="mainNav">
                <!-- Mobile Menu Container -->
                <div class="mobile-menu-container d-lg-none">
                    <!-- Mobile Menu Header -->
                    <div class="mobile-menu-header">
                        <div class="brand-text">
                            <span class="brand-line-1">Tansu Şahal</span>
                            <span class="brand-line-2">Salamura</span>
                        </div>
                    </div>

                    <!-- Mobile Navigation Links -->
                     <ul class="mobile-nav-links">
                        <li class="nav-item"><a class="nav-link" href="/" onclick="closeMobileMenu()">🏠 Ana Sayfa</a></li>
                        <li class="nav-item"><a class="nav-link" href="/#products" onclick="closeMobileMenu()">🛍️ Tüm Ürünler</a></li>
                        <li class="nav-item"><a class="nav-link" href="/#about-us-section" onclick="closeMobileMenu()">🌿 Hakkımızda</a></li>
                        <li class="nav-item"><a class="nav-link" href="blog.php" onclick="closeMobileMenu()">📝 Blog</a></li>
                        <li class="nav-item"><a class="nav-link" href="/#contact-section" onclick="closeMobileMenu()">📞 İletişim</a></li>
                    </ul>

                    <!-- Mobile User Section -->
                    <div class="mobile-user-section">
                        <div id="mobileUserGreeting" class="mobile-user-greeting" style="display:none;"></div>
                        
                        <div class="mobile-auth-buttons">
                            <a href="login.php" id="mobileLoginLink" class="mobile-auth-button primary" onclick="closeMobileMenu()">
                                <i class="fas fa-sign-in-alt"></i> Giriş Yap
                            </a>
                            <a href="register.php" id="mobileRegisterLink" class="mobile-auth-button secondary" onclick="closeMobileMenu()">
                                <i class="fas fa-user-plus"></i> Kayıt Ol
                            </a>
                            <a href="#" id="mobileLogoutLink" class="mobile-auth-button primary" style="display:none;" onclick="closeMobileMenu()">
                                <i class="fas fa-sign-out-alt"></i> Çıkış Yap
                            </a>
                            <a href="admin.php" id="mobileAdminLink" class="mobile-auth-button secondary" style="display:none;" onclick="closeMobileMenu()">
                                <i class="fas fa-cog"></i> Admin
                            </a>
                        </div>
                        
                        <a href="favorites.php" class="mobile-favorites-button" onclick="closeMobileMenu()">
                            <i class="fas fa-heart"></i> 
                            Favoriler 
                            <span id="mobileFavoritesCount" class="badge bg-light text-dark ms-auto">0</span>
                        </a>
                    </div>

                    <!-- Mobile Menu Footer -->
                    <div class="mobile-menu-footer">
                        <p>© 2025 Tansu Şahal Salamura</p>
                    </div>
                </div>

                <!-- Desktop Menu -->
                <ul class="navbar-nav mx-auto mb-2 mb-lg-0 d-none d-lg-flex">
                    <li class="nav-item"><a class="nav-link" href="/">Ana Sayfa</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#products">Tüm Ürünler</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#about-us-section">Hakkımızda</a></li>
                    <li class="nav-item"><a class="nav-link" href="/blog.php">Blog</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#contact-section">İletişim</a></li>
                </ul>
                
                <!-- Desktop User Info -->
                <div class="d-none d-lg-flex align-items-center navbar-user-info">
                    <span id="userGreeting" class="navbar-text navbar-user-greeting me-3" style="display:none;"></span>
                    <div class="auth-links-group">
                        <a href="login.php" id="loginLink" class="text-dark text-decoration-none auth-link">Giriş Yap</a>
                        <a href="register.php" id="registerLink" class="text-dark text-decoration-none auth-link">Kayıt Ol</a>
                        <a href="#" id="logoutLink" class="text-dark text-decoration-none auth-link" style="display:none;">Çıkış Yap</a>
                        <a href="admin.php" id="adminLink" class="text-dark text-decoration-none auth-link" style="display:none;">Admin</a>
                        <a href="favorites.php" class="btn btn-outline-danger position-relative rounded-pill px-3">
                            <i class="fas fa-heart me-1"></i> Favoriler
                            <span id="favorites-item-count" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger border border-light">0</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <main class="admin-container">
        <!-- Admin Header -->
        <div class="admin-header">
            <h1><i class="fas fa-cogs me-3"></i>Admin Paneli</h1>
            <p>Ürün yönetimi ve site kontrolü</p>
        </div>

        <!-- Stats Cards - BOOTSTRAP SINIFLARINI KALDIRDIM -->
        <div class="stats-container-vertical">
            <div class="stat-card-vertical">
                <div class="stat-icon products">
                    <i class="fas fa-box"></i>
                </div>
                <h3 class="stat-number" id="totalProducts">0</h3>
                <p class="stat-label">Toplam Ürün</p>
            </div>
            <div class="stat-card-vertical">
                <div class="stat-icon stock">
                    <i class="fas fa-warehouse"></i>
                </div>
                <h3 class="stat-number" id="totalStock">0</h3>
                <p class="stat-label">Toplam Stok</p>
            </div>
        </div>

        <!-- Message Area -->
        <div id="adminMessageArea" class="message-area" style="display:none;"></div>

        <!-- Product Form Section -->
        <section class="form-section" id="productSection">
            <h2 class="section-title">
                <i class="fas fa-plus-circle me-2"></i>
                Yeni Ürün Ekle / Düzenle
            </h2>
            
            <form id="productForm" novalidate>
                <input type="hidden" id="productId" name="id">
                
                <!-- BOOTSTRAP ROW/COL SINIFLARINI KALDIRDIM -->
                <div class="form-row-vertical">
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="name" class="form-label">
                                <i class="fas fa-tag me-2"></i>Ürün Adı
                            </label>
                            <input type="text" class="form-control form-control-lg" id="name" name="name" placeholder="Ürün adını girin">
                        </div>
                    </div>
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="category" class="form-label">
                                <i class="fas fa-folder me-2"></i>Kategori
                            </label>
                            <input type="text" class="form-control form-control-lg" id="category" name="category" placeholder="Salamura, Turşu, Zeytinyağı">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">
                        <i class="fas fa-align-left me-2"></i>Açıklama
                    </label>
                    <textarea class="form-control form-control-lg" id="description" name="description" rows="4" placeholder="Ürün açıklamasını girin"></textarea>
                </div>

                <div class="form-row-vertical">
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="price" class="form-label">
                                <i class="fas fa-lira-sign me-2"></i>Fiyat (TL)
                            </label>
                            <input type="number" class="form-control form-control-lg" id="price" name="price" step="0.01" min="0" placeholder="0.00">
                        </div>
                    </div>
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="stock" class="form-label">
                                <i class="fas fa-boxes me-2"></i>Stok Adedi
                            </label>
                            <input type="number" class="form-control form-control-lg" id="stock" name="stock" min="0" placeholder="0">
                        </div>
                    </div>
                </div>

                <!-- Çoklu Resim Yükleme Bölümü -->
                <div class="form-group">
                    <label for="images" class="form-label">
                        <i class="fas fa-images me-2"></i>Ürün Görselleri (Çoklu Seçim)
                    </label>
                    <input type="file" class="form-control form-control-lg" id="images" name="images[]" multiple accept="image/*">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Birden fazla görsel seçebilirsiniz. İlk seçilen görsel ana resim olarak kullanılır. 
                        Görsellere tıklayarak ana resmi değiştirebilirsiniz.
                    </small>
                    
                    <!-- Yükleme Progress Bar -->
                    <div id="uploadProgress" class="upload-progress" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar progress-bar-custom" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted mt-1 d-block">Görseller yükleniyor...</small>
                    </div>
                    
                    <!-- Görsel Önizleme Alanı -->
                    <div id="imagesPreview" class="images-preview-container">
                        <div class="text-muted text-center w-100">
                            <i class="fas fa-images fa-2x mb-2"></i>
                            <p class="mb-0">Seçilen görseller burada görünecek</p>
                        </div>
                    </div>
                </div>

                <div class="form-row-vertical">
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="videoUrl" class="form-label">
                                <i class="fas fa-video me-2"></i>Video URL
                            </label>
                            <input type="url" class="form-control form-control-lg" id="videoUrl" name="videoUrl" placeholder="https://www.youtube.com/watch?v=...">
                        </div>
                    </div>
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="videoFile" class="form-label">
                                <i class="fas fa-upload me-2"></i>Veya Video Dosyası Yükle
                            </label>
                            <input type="file" class="form-control form-control-lg" id="videoFile" name="videoFile" accept="video/*">
                            <small class="text-muted">Maksimum 100MB - MP4, AVI, MOV, WEBM formatları desteklenir</small>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="purchaseLink" class="form-label">
                        <i class="fas fa-shopping-cart me-2"></i>Satın Alma Linki
                    </label>
                    <input type="url" class="form-control form-control-lg" id="purchaseLink" name="purchaseLink" placeholder="https://www.shopier.com/...">
                </div>

                <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap; margin-top: 2rem;">
                    <button type="submit" class="btn btn-primary btn-lg px-5">
                        <i class="fas fa-save me-2"></i>Kaydet / Güncelle
                    </button>
                    <button type="button" id="clearFormButton" class="btn btn-outline-secondary btn-lg px-4">
                        <i class="fas fa-eraser me-2"></i>Formu Temizle
                    </button>
                </div>
            </form>
        </section>

        <!-- Blog Management Section -->
        <section class="form-section" id="blogSection">
            <h2 class="section-title">
                <i class="fas fa-blog me-2"></i>
                Blog Yönetimi
            </h2>
            
            <!-- Blog Stats -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="stat-card-vertical">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #9333ea, #a855f7) !important;">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <h3 class="stat-number" id="totalBlogPosts">0</h3>
                        <p class="stat-label">Toplam Blog Yazısı</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="stat-card-vertical">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #f59e0b, #f97316) !important;">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h3 class="stat-number" id="totalViews">0</h3>
                        <p class="stat-label">Toplam Görüntülenme</p>
                    </div>
                </div>
            </div>
            
            <!-- Blog Form -->
            <form id="blogForm" novalidate>
                <input type="hidden" id="blogPostId" name="id">
                
                <div class="form-row-vertical">
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="blogTitle" class="form-label">
                                <i class="fas fa-heading me-2"></i>Blog Başlığı
                            </label>
                            <input type="text" class="form-control form-control-lg" id="blogTitle" name="title" placeholder="Blog yazısı başlığını girin" required>
                        </div>
                    </div>
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="blogSlug" class="form-label">
                                <i class="fas fa-link me-2"></i>URL Slug (Otomatik oluşturulur)
                            </label>
                            <input type="text" class="form-control form-control-lg" id="blogSlug" name="slug" placeholder="url-slug">
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="blogExcerpt" class="form-label">
                        <i class="fas fa-quote-left me-2"></i>Özet (Excerpt)
                    </label>
                    <textarea class="form-control form-control-lg" id="blogExcerpt" name="excerpt" rows="3" placeholder="Blog yazısının kısa özetini girin..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="blogContent" class="form-label">
                        <i class="fas fa-edit me-2"></i>Blog İçeriği
                    </label>

                    <!-- Formatting Toolbar -->
                    <div class="formatting-toolbar mb-2">
                        <div class="btn-group" role="group" aria-label="Text formatting">
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatText('h1')" title="Büyük Başlık (H1)">
                                <i class="fas fa-heading"></i> H1
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatText('h2')" title="Orta Başlık (H2)">
                                <i class="fas fa-heading"></i> H2
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatText('h3')" title="Küçük Başlık (H3)">
                                <i class="fas fa-heading"></i> H3
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatText('bold')" title="Kalın">
                                <i class="fas fa-bold"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatText('italic')" title="İtalik">
                                <i class="fas fa-italic"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatText('underline')" title="Altı Çizili">
                                <i class="fas fa-underline"></i>
                            </button>
                        </div>
                        <div class="btn-group ms-2" role="group" aria-label="List formatting">
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatText('ul')" title="Madde İşaretli Liste">
                                <i class="fas fa-list-ul"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatText('ol')" title="Numaralı Liste">
                                <i class="fas fa-list-ol"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatText('quote')" title="Alıntı">
                                <i class="fas fa-quote-left"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatText('link')" title="Link">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                        <div class="btn-group ms-2" role="group" aria-label="Special formatting">
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('🌿')" title="Yaprak Emoji">
                                🌿
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('🌱')" title="Fide Emoji">
                                🌱
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('🍇')" title="Üzüm Emoji">
                                🍇
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('🥒')" title="Salatalık Emoji">
                                🥒
                            </button>
                        </div>
                    </div>

                    <!-- Content Editor with Live Preview -->
                    <div class="content-editor-container">
                        <div class="editor-tabs">
                            <button type="button" class="tab-btn active" onclick="switchTab('editor')" id="editorTab">
                                <i class="fas fa-edit"></i> Düzenle
                            </button>
                            <button type="button" class="tab-btn" onclick="switchTab('preview')" id="previewTab">
                                <i class="fas fa-eye"></i> Önizleme
                            </button>
                            <button type="button" class="tab-btn" onclick="switchTab('split')" id="splitTab">
                                <i class="fas fa-columns"></i> Yan Yana
                            </button>
                        </div>

                        <div class="editor-content">
                            <div class="editor-pane active" id="editorPane">
                                <textarea class="form-control form-control-lg" id="blogContent" name="content" rows="15" placeholder="Blog yazısının tam içeriğini girin..." required></textarea>
                            </div>

                            <div class="preview-pane" id="previewPane">
                                <div class="preview-content" id="previewContent">
                                    <p class="text-muted text-center">Önizleme burada görünecek...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-text">
                        <small class="text-muted">
                            💡 <strong>İpucu:</strong> Yukarıdaki butonları kullanarak metninizi formatlayabilirsiniz.
                            Başlık yapmak için metni seçin ve H1, H2 veya H3 butonlarına tıklayın.
                            <strong>Önizleme</strong> sekmesinde değişikliklerinizi canlı olarak görebilirsiniz.
                        </small>
                    </div>
                </div>
                
                <div class="form-row-vertical">
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="blogCategory" class="form-label">
                                <i class="fas fa-folder me-2"></i>Kategori
                            </label>
                            <div class="input-group">
                                <select class="form-control form-control-lg" id="blogCategory" name="category_id" required>
                                    <option value="">Kategori Seçin</option>
                                    <!-- Kategoriler JS ile doldurulacak -->
                                </select>
                                <button type="button" class="btn btn-outline-success" onclick="openCategoryModal()" title="Yeni Kategori Ekle">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="blogAuthor" class="form-label">
                                <i class="fas fa-user me-2"></i>Yazar
                            </label>
                            <input type="text" class="form-control form-control-lg" id="blogAuthor" name="author" placeholder="Yazar adı" value="Tansu Şahal">
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="blogFeaturedImage" class="form-label">
                        <i class="fas fa-image me-2"></i>Öne Çıkan Görsel
                    </label>
                    <input type="file" class="form-control form-control-lg" id="blogFeaturedImage" name="featured_image" accept="image/*">
                    <div id="blogImagePreview" class="mt-3" style="display: none;">
                        <img id="blogImagePreviewImg" src="" alt="Önizleme" style="max-width: 200px; border-radius: 10px;">
                        <button type="button" class="btn btn-sm btn-danger ms-2" onclick="removeBlogImage()">
                            <i class="fas fa-trash"></i> Kaldır
                        </button>
                    </div>
                </div>
                
                <div class="form-row-vertical">
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="blogTags" class="form-label">
                                <i class="fas fa-hashtag me-2"></i>Etiketler (hashtag formatında)
                            </label>
                            <input type="text" class="form-control form-control-lg" id="blogTags" name="tags" placeholder="#DoğalBeslenme #KatkısızYaşam #EvYapımıLezzetler #TarhanaKokusu #SalamuraZamanı #ReçelSeverler #YoğurtGüzelliği #KültüreMiras #SağlıklıTercihler #EvMutfağı">
                            <div class="form-text">
                                <small class="text-muted">
                                    💡 <strong>İpucu:</strong> Etiketleri #hashtag formatında yazın. Türkçe karakterler desteklenir.<br>
                                    📝 <strong>Örnek:</strong> #DoğalBeslenme #KatkısızYaşam #EvYapımıLezzetler #TarhanaKokusu #SalamuraZamanı
                                </small>
                            </div>
                            <div id="tagsPreview" class="tags-preview mt-2"></div>
                        </div>
                    </div>
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="blogReadingTime" class="form-label">
                                <i class="fas fa-clock me-2"></i>Okuma Süresi (dakika)
                            </label>
                            <input type="number" class="form-control form-control-lg" id="blogReadingTime" name="reading_time" min="1" placeholder="5">
                        </div>
                    </div>
                </div>
                
                <div class="form-row-vertical">
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <label for="blogStatus" class="form-label">
                                <i class="fas fa-toggle-on me-2"></i>Durum
                            </label>
                            <select class="form-control form-control-lg" id="blogStatus" name="status" required>
                                <option value="published">Yayınlandı</option>
                                <option value="draft">Taslak</option>
                                <option value="archived">Arşivlendi</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col-vertical">
                        <div class="form-group">
                            <div class="form-check" style="margin-top: 2rem;">
                                <input class="form-check-input" type="checkbox" id="blogFeatured" name="featured" value="1">
                                <label class="form-check-label" for="blogFeatured">
                                    <i class="fas fa-star me-2"></i>Öne Çıkan Yazı
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- SEO Fields -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-search me-2"></i>SEO Ayarları
                    </label>
                    <div class="border rounded p-3">
                        <div class="form-group">
                            <label for="blogMetaTitle" class="form-label">
                                <i class="fas fa-robot me-2"></i>Meta Başlık - Otomatik Oluşturulur
                            </label>
                            <input type="text" class="form-control" id="blogMetaTitle" name="meta_title" placeholder="Başlık yazdığınızda otomatik oluşturulacak..." readonly>
                            <div class="form-text">
                                <small class="text-success">
                                    🤖 <strong>Otomatik:</strong> Blog başlığından SEO-optimize edilmiş meta title oluşturulur.
                                    <button type="button" class="btn btn-link btn-sm p-0 ms-2" onclick="toggleBlogMetaTitleEdit()">
                                        <i class="fas fa-edit"></i> Manuel Düzenle
                                    </button>
                                </small>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="blogMetaDescription" class="form-label">
                                <i class="fas fa-robot me-2"></i>Meta Açıklama - Otomatik Oluşturulur
                            </label>
                            <textarea class="form-control" id="blogMetaDescription" name="meta_description" rows="2" placeholder="Özet yazdığınızda otomatik oluşturulacak..." readonly></textarea>
                            <div class="form-text">
                                <small class="text-success">
                                    🤖 <strong>Otomatik:</strong> Blog özeti ve başlığından SEO-optimize edilmiş meta description oluşturulur.
                                    <button type="button" class="btn btn-link btn-sm p-0 ms-2" onclick="toggleBlogMetaEdit()">
                                        <i class="fas fa-edit"></i> Manuel Düzenle
                                    </button>
                                </small>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="blogMetaKeywords" class="form-label">
                                <i class="fas fa-robot me-2"></i>Meta Anahtar Kelimeler - Otomatik Oluşturulur
                            </label>
                            <input type="text" class="form-control" id="blogMetaKeywords" name="meta_keywords" placeholder="Başlık ve etiketlerden otomatik oluşturulacak..." readonly>
                            <div class="form-text">
                                <small class="text-success">
                                    🤖 <strong>Otomatik:</strong> Blog başlığı, kategori ve etiketlerden SEO anahtar kelimeleri oluşturulur.
                                    <button type="button" class="btn btn-link btn-sm p-0 ms-2" onclick="toggleBlogMetaKeywordsEdit()">
                                        <i class="fas fa-edit"></i> Manuel Düzenle
                                    </button>
                                </small>
                            </div>
                            <div id="metaKeywordsPreview" class="tags-preview mt-2"></div>
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap; margin-top: 2rem;">
                    <button type="submit" class="btn btn-primary btn-lg px-5">
                        <i class="fas fa-save me-2"></i>Blog Yazısını Kaydet
                    </button>
                    <button type="button" id="clearBlogFormButton" class="btn btn-outline-secondary btn-lg px-4">
                        <i class="fas fa-eraser me-2"></i>Formu Temizle
                    </button>
                </div>
            </form>
        </section>

        <!-- Products Table Section -->
        <section class="table-container" id="existingProducts">
            <h2 class="section-title">
                <i class="fas fa-list me-2"></i>
                Mevcut Ürünler
            </h2>

            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th scope="col" style="width: 120px;">Resimler</th>
                            <th scope="col">Ürün Adı</th>
                            <th scope="col" class="text-end">Fiyat</th>
                            <th scope="col" class="text-center">Stok</th>
                            <th scope="col" class="text-center">Video</th>
                            <th scope="col" class="text-center" style="width: 150px;">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody id="productListBody">
                        <!-- Ürünler buraya JS ile eklenecek -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Blog Posts Table Section -->
        <section class="table-container" id="existingBlogs">
            <h2 class="section-title">
                <i class="fas fa-newspaper me-2"></i>
                Blog Yazıları
            </h2>

            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th scope="col" style="width: 100px;">Görsel</th>
                            <th scope="col">Başlık</th>
                            <th scope="col">Kategori</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">Görüntülenme</th>
                            <th scope="col" class="text-center">Tarih</th>
                            <th scope="col" class="text-center" style="width: 150px;">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody id="blogPostsListBody">
                        <!-- Blog yazıları buraya JS ile eklenecek -->
                    </tbody>
                </table>
            </div>
        </section>
    </main>

    <footer class="bg-dark text-white text-center p-4 mt-5">
        <div class="container">
            <p class="mb-0">© 2025 🥒 Tansu Şahal Salamura - Admin Paneli 🌿</p>
            <p class="small text-white-50 mt-2">
                <i class="fas fa-code me-1"></i>
                Web Tasarımı: <a href="mailto:<EMAIL>" class="text-white-50"><EMAIL></a>
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="header-updater.js"></script>
    <script src="mobile-menu.js"></script> <!-- Universal mobile menu system -->
    <script>
        // Production mode - disable debug logs
        if (window.location.hostname !== 'localhost') {
            console.log = function() {};
            console.debug = function() {};
        }



        // Intercept dynamic script/link creation
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(this, tagName);
            if (tagName.toLowerCase() === 'script' || tagName.toLowerCase() === 'link') {
                const originalSrcSetter = Object.getOwnPropertyDescriptor(element.constructor.prototype, 'src') ||
                                         Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'src');
                const originalHrefSetter = Object.getOwnPropertyDescriptor(element.constructor.prototype, 'href') ||
                                          Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'href');

                if (originalSrcSetter) {
                    Object.defineProperty(element, 'src', {
                        set: function(value) {
                            console.log('🔍 ELEMENT SRC:', value, typeof value);
                            if (value === 'Array' || value.toString() === 'Array') {
                                console.error('❌ INVALID ELEMENT SRC!');
                                console.error('Value:', value);
                                console.error('Stack:', new Error().stack);
                                debugger;
                            }
                            originalSrcSetter.set.call(this, value);
                        },
                        get: originalSrcSetter.get
                    });
                }

                if (originalHrefSetter) {
                    Object.defineProperty(element, 'href', {
                        set: function(value) {
                            console.log('🔍 ELEMENT HREF:', value, typeof value);
                            if (value === 'Array' || value.toString() === 'Array') {
                                console.error('❌ INVALID ELEMENT HREF!');
                                console.error('Value:', value);
                                console.error('Stack:', new Error().stack);
                                debugger;
                            }
                            originalHrefSetter.set.call(this, value);
                        },
                        get: originalHrefSetter.get
                    });
                }
            }
            return element;
        };
    </script>
    <script src="blog-admin.js"></script>
    <script src="admin-scripts.js"></script>
    <script>
        // Text Formatting Functions
        function formatText(type) {
            const textarea = document.getElementById('blogContent');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const selectedText = textarea.value.substring(start, end);

            if (!selectedText) {
                alert('Lütfen formatlamak istediğiniz metni seçin.');
                return;
            }

            let formattedText = '';

            switch(type) {
                case 'h1':
                    formattedText = `\n# ${selectedText}\n`;
                    break;
                case 'h2':
                    formattedText = `\n## ${selectedText}\n`;
                    break;
                case 'h3':
                    formattedText = `\n### ${selectedText}\n`;
                    break;
                case 'bold':
                    formattedText = `**${selectedText}**`;
                    break;
                case 'italic':
                    formattedText = `*${selectedText}*`;
                    break;
                case 'underline':
                    formattedText = `<u>${selectedText}</u>`;
                    break;
                case 'ul':
                    const ulItems = selectedText.split('\n').map(line => line.trim() ? `- ${line.trim()}` : '').filter(line => line);
                    formattedText = `\n${ulItems.join('\n')}\n`;
                    break;
                case 'ol':
                    const olItems = selectedText.split('\n').map((line, index) => line.trim() ? `${index + 1}. ${line.trim()}` : '').filter(line => line);
                    formattedText = `\n${olItems.join('\n')}\n`;
                    break;
                case 'quote':
                    formattedText = `\n> ${selectedText}\n`;
                    break;
                case 'link':
                    const url = prompt('Link URL\'sini girin:');
                    if (url) {
                        formattedText = `[${selectedText}](${url})`;
                    } else {
                        return;
                    }
                    break;
                default:
                    return;
            }

            // Replace selected text with formatted text
            textarea.value = textarea.value.substring(0, start) + formattedText + textarea.value.substring(end);

            // Set cursor position after formatted text
            const newCursorPos = start + formattedText.length;
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            textarea.focus();

            // Update preview if visible
            updatePreview();
        }

        function insertEmoji(emoji) {
            const textarea = document.getElementById('blogContent');
            const cursorPos = textarea.selectionStart;

            // Insert emoji at cursor position
            textarea.value = textarea.value.substring(0, cursorPos) + emoji + textarea.value.substring(cursorPos);

            // Set cursor position after emoji
            const newCursorPos = cursorPos + emoji.length;
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            textarea.focus();

            // Update preview if visible
            updatePreview();
        }

        // Live Preview Functions
        function switchTab(tab) {
            const editorPane = document.getElementById('editorPane');
            const previewPane = document.getElementById('previewPane');
            const editorTab = document.getElementById('editorTab');
            const previewTab = document.getElementById('previewTab');
            const splitTab = document.getElementById('splitTab');

            // Remove active classes
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            editorPane.classList.remove('active', 'split');
            previewPane.classList.remove('active', 'split');

            switch(tab) {
                case 'editor':
                    editorTab.classList.add('active');
                    editorPane.classList.add('active');
                    break;
                case 'preview':
                    previewTab.classList.add('active');
                    previewPane.classList.add('active');
                    updatePreview();
                    break;
                case 'split':
                    splitTab.classList.add('active');
                    editorPane.classList.add('split');
                    previewPane.classList.add('split');
                    updatePreview();
                    break;
            }
        }

        function updatePreview() {
            const content = document.getElementById('blogContent').value;
            const previewContent = document.getElementById('previewContent');

            if (!content.trim()) {
                previewContent.innerHTML = '<p class="text-muted text-center">Önizleme burada görünecek...</p>';
                return;
            }

            const formattedContent = formatBlogContentForPreview(content);
            previewContent.innerHTML = formattedContent;
        }

        function formatBlogContentForPreview(content) {
            if (!content) return '';

            // Convert markdown-style formatting to HTML
            let formatted = content
                // Convert \r\n to \n for consistency
                .replace(/\r\n/g, '\n')
                // Convert \r to \n for consistency
                .replace(/\r/g, '\n')

                // Convert headers
                .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                .replace(/^# (.*$)/gm, '<h1>$1</h1>')

                // Convert bold and italic
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')

                // Convert underline
                .replace(/<u>(.*?)<\/u>/g, '<u>$1</u>')

                // Convert lists
                .replace(/^- (.*$)/gm, '<li>$1</li>')
                .replace(/^\d+\. (.*$)/gm, '<li>$1</li>')

                // Convert quotes
                .replace(/^> (.*$)/gm, '<blockquote>$1</blockquote>')

                // Convert links
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')

                // Convert double line breaks to paragraphs
                .replace(/\n\n+/g, '</p><p>')
                // Convert single line breaks to <br>
                .replace(/\n/g, '<br>')

                // Wrap lists in ul tags
                .replace(/(<li>.*?<\/li>)(<br>)*(<li>.*?<\/li>)/g, function(match, p1, p2, p3) {
                    return p1 + p3; // Remove <br> between list items
                })
                .replace(/(<li>.*?<\/li>(<li>.*?<\/li>)*)/g, '<ul>$1</ul>')

                // Wrap in paragraph tags
                .replace(/^/, '<p>')
                .replace(/$/, '</p>')

                // Clean up empty paragraphs and fix formatting
                .replace(/<p><\/p>/g, '')
                .replace(/<p><br><\/p>/g, '')
                .replace(/<p>(<h[1-6]>.*?<\/h[1-6]>)<\/p>/g, '$1')
                .replace(/<p>(<ul>.*?<\/ul>)<\/p>/g, '$1')
                .replace(/<p>(<blockquote>.*?<\/blockquote>)<\/p>/g, '$1')
                .replace(/<br><\/p>/g, '</p>')
                .replace(/<p><br>/g, '<p>');

            return formatted;
        }

        // Category Management Functions
        function openCategoryModal() {
            console.log('openCategoryModal called');

            try {
                const modalElement = document.getElementById('categoryModal');
                console.log('Modal element:', modalElement);

                if (!modalElement) {
                    console.error('Category modal element not found!');
                    alert('Modal bulunamadı!');
                    return;
                }

                if (typeof bootstrap === 'undefined') {
                    console.error('Bootstrap not loaded!');
                    alert('Bootstrap yüklenmedi!');
                    return;
                }

                const modal = new bootstrap.Modal(modalElement);
                modal.show();

                // Clear form
                const form = document.getElementById('categoryForm');
                if (form) {
                    form.reset();
                }

                console.log('Modal opened successfully');
            } catch (error) {
                console.error('Error opening modal:', error);
                alert('Modal açılırken hata: ' + error.message);
            }
        }

        async function saveCategory() {
            const form = document.getElementById('categoryForm');
            const formData = new FormData(form);

            // Generate slug from name
            const name = formData.get('name');
            if (!name.trim()) {
                alert('Kategori adı gerekli!');
                return;
            }

            const slug = generateSlug(name);
            formData.set('slug', slug);

            try {
                const userInfo = getUserInfo();
                const response = await fetch('/api/blog/categories.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${userInfo.token}`,
                        'Content-Type': 'application/json; charset=utf-8',
                        'Accept': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify({
                        name: formData.get('name'),
                        slug: slug,
                        description: formData.get('description'),
                        meta_description: formData.get('meta_description')
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('categoryModal'));
                    modal.hide();

                    // Reload categories
                    await loadBlogCategories();

                    // Select the new category
                    const categorySelect = document.getElementById('blogCategory');
                    categorySelect.value = result.category.id;

                    showNotification('Kategori başarıyla eklendi!', 'success');
                } else {
                    throw new Error(result.message || 'Kategori eklenemedi');
                }
            } catch (error) {
                console.error('Category save error:', error);
                showNotification('Hata: ' + error.message, 'error');
            }
        }

        // Auto-generate slug for category and meta descriptions
        document.addEventListener('DOMContentLoaded', function() {
            const categoryName = document.getElementById('categoryName');
            const categorySlug = document.getElementById('categorySlug');
            const categoryDescription = document.getElementById('categoryDescription');

            if (categoryName && categorySlug) {
                categoryName.addEventListener('input', function() {
                    categorySlug.value = generateSlug(this.value);
                    updateCategoryMetaDescription();
                });
            }

            if (categoryDescription) {
                categoryDescription.addEventListener('input', updateCategoryMetaDescription);
            }

            // Blog meta auto-generation
            const blogTitle = document.getElementById('blogTitle');
            const blogExcerpt = document.getElementById('blogExcerpt');
            const blogTags = document.getElementById('blogTags');
            const blogCategory = document.getElementById('blogCategory');

            if (blogTitle) {
                blogTitle.addEventListener('input', updateAllBlogMeta);
            }

            if (blogExcerpt) {
                blogExcerpt.addEventListener('input', updateBlogMetaDescription);
            }

            if (blogTags) {
                blogTags.addEventListener('input', updateBlogMetaKeywords);
            }

            if (blogCategory) {
                blogCategory.addEventListener('change', updateBlogMetaKeywords);
            }
        });

        // Tags Management Functions
        function updateTagsPreview() {
            const tagsInput = document.getElementById('blogTags');
            const tagsPreview = document.getElementById('tagsPreview');

            if (!tagsInput || !tagsPreview) return;

            const tagsText = tagsInput.value.trim();
            if (!tagsText) {
                tagsPreview.innerHTML = '';
                return;
            }

            // Extract hashtags (including Turkish characters)
            const hashtags = tagsText.match(/#[a-zA-ZşğüöçıİŞĞÜÖÇ0-9]+/g) || [];

            if (hashtags.length === 0) {
                tagsPreview.innerHTML = '<small class="text-warning">⚠️ Geçerli hashtag bulunamadı. Türkçe karakterlerle örnek: #DoğalBeslenme #SalamuraZamanı</small>';
                return;
            }

            const tagsHtml = hashtags.map(tag =>
                `<span class="tag-item">
                    <i class="fas fa-hashtag"></i>${tag.substring(1)}
                </span>`
            ).join('');

            tagsPreview.innerHTML = tagsHtml;
        }

        function updateMetaKeywordsPreview() {
            const keywordsInput = document.getElementById('blogMetaKeywords');
            const keywordsPreview = document.getElementById('metaKeywordsPreview');

            if (!keywordsInput || !keywordsPreview) return;

            const keywordsText = keywordsInput.value.trim();
            if (!keywordsText) {
                keywordsPreview.innerHTML = '';
                return;
            }

            // Extract hashtags (including Turkish characters)
            const hashtags = keywordsText.match(/#[a-zA-ZşğüöçıİŞĞÜÖÇ0-9]+/g) || [];

            if (hashtags.length === 0) {
                keywordsPreview.innerHTML = '<small class="text-warning">⚠️ Geçerli hashtag bulunamadı. Türkçe karakterlerle örnek: #DoğalBeslenme #SalamuraZamanı</small>';
                return;
            }

            const keywordsHtml = hashtags.map(tag =>
                `<span class="tag-item">
                    <i class="fas fa-hashtag"></i>${tag.substring(1)}
                </span>`
            ).join('');

            keywordsPreview.innerHTML = keywordsHtml;
        }

        // Auto-update preview on content change
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('blogContent');
            if (textarea) {
                textarea.addEventListener('input', function() {
                    // Only update if preview is visible
                    const previewPane = document.getElementById('previewPane');
                    if (previewPane && (previewPane.classList.contains('active') || previewPane.classList.contains('split'))) {
                        updatePreview();
                    }
                });
            }

            // Auto-update tags preview
            const tagsInput = document.getElementById('blogTags');
            if (tagsInput) {
                tagsInput.addEventListener('input', updateTagsPreview);
                // Initial update
                updateTagsPreview();
            }

            // Auto-update meta keywords preview
            const metaKeywordsInput = document.getElementById('blogMetaKeywords');
            if (metaKeywordsInput) {
                metaKeywordsInput.addEventListener('input', updateMetaKeywordsPreview);
                // Initial update
                updateMetaKeywordsPreview();
            }
        });

        // Floating Navigation Functions
        function toggleFloatingNav() {
            const menu = document.getElementById('floatingNavMenu');
            menu.classList.toggle('active');
        }

        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Close floating nav
                const menu = document.getElementById('floatingNavMenu');
                menu.classList.remove('active');

                // Add highlight effect
                section.style.boxShadow = '0 0 20px rgba(34, 139, 34, 0.3)';
                setTimeout(() => {
                    section.style.boxShadow = '';
                }, 2000);
            } else {
                console.warn('Section not found:', sectionId);
            }
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });

            // Close floating nav
            const menu = document.getElementById('floatingNavMenu');
            menu.classList.remove('active');
        }

        function scrollToBottom() {
            window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth'
            });

            // Close floating nav
            const menu = document.getElementById('floatingNavMenu');
            menu.classList.remove('active');
        }

        // Close floating nav when clicking outside
        document.addEventListener('click', function(event) {
            const floatingNav = document.getElementById('floatingNav');
            const menu = document.getElementById('floatingNavMenu');

            if (floatingNav && !floatingNav.contains(event.target)) {
                menu.classList.remove('active');
            }
        });

        // Auto-hide floating nav on scroll (optional)
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            const floatingNav = document.getElementById('floatingNav');

            // Show nav
            floatingNav.style.opacity = '1';

            // Clear previous timeout
            clearTimeout(scrollTimeout);

            // Hide nav after scroll stops (optional - remove if you want it always visible)
            // scrollTimeout = setTimeout(() => {
            //     floatingNav.style.opacity = '0.7';
            // }, 2000);
        });

        // Auto SEO Functions
        function generateSEOTitle(title, maxLength = 60) {
            if (!title || title.trim() === '') return '';

            const brandName = 'Tansu Şahal Salamura';
            const separator = ' | ';

            // Clean the title
            let cleanTitle = title.trim();

            // Calculate available space for title
            const availableLength = maxLength - brandName.length - separator.length;

            if (cleanTitle.length <= availableLength) {
                return cleanTitle + separator + brandName;
            } else {
                // Truncate at word boundary
                let truncated = cleanTitle.substring(0, availableLength - 3);
                let lastSpace = truncated.lastIndexOf(' ');
                if (lastSpace > 0) {
                    truncated = truncated.substring(0, lastSpace);
                }
                return truncated + '...' + separator + brandName;
            }
        }

        function generateSEOKeywords(title, tags, category) {
            const keywords = new Set();

            // Add brand keywords
            keywords.add('#salamura');
            keywords.add('#tansu-sahal');

            // Extract keywords from title (improved Turkish character support)
            if (title) {
                const titleWords = title.toLowerCase()
                    .replace(/[^a-zA-ZşğüöçıİŞĞÜÖÇ0-9\s]/g, ' ')
                    .split(/\s+/)
                    .filter(word => word.length > 2);

                titleWords.forEach(word => {
                    if (word.length > 3) {
                        keywords.add('#' + word);
                    }
                });
            }

            // Add category as keyword
            if (category) {
                const categorySlug = generateSlug(category);
                keywords.add('#' + categorySlug);
            }

            // Add existing tags (including Turkish characters)
            if (tags) {
                const existingTags = tags.match(/#[a-zA-ZşğüöçıİŞĞÜÖÇ0-9]+/g) || [];
                existingTags.forEach(tag => keywords.add(tag));
            }

            // Add common food/health keywords
            const commonKeywords = ['#fermentasyon', '#probiyotik', '#doğal', '#sağlıklı', '#geleneksel'];
            commonKeywords.forEach(keyword => keywords.add(keyword));

            // Convert to array and limit to 10 keywords
            return Array.from(keywords).slice(0, 10).join(' ');
        }

        function generateSEODescription(text, title = '', maxLength = 155) {
            if (!text || text.trim() === '') return '';

            // Clean the text
            let cleanText = text
                .replace(/\n/g, ' ')
                .replace(/\s+/g, ' ')
                .trim();

            // Remove HTML tags if any
            cleanText = cleanText.replace(/<[^>]*>/g, '');

            // Add brand name
            const brandName = 'Tansu Şahal Salamura';

            // Create SEO optimized description
            let seoDesc = '';

            if (title && title.trim() !== '') {
                // If we have a title, use it as starting point
                seoDesc = `${title.trim()} - `;
                maxLength -= seoDesc.length;
            }

            // Add main content
            if (cleanText.length <= maxLength - brandName.length - 3) {
                seoDesc += cleanText + ' | ' + brandName;
            } else {
                // Truncate at word boundary
                let truncated = cleanText.substring(0, maxLength - brandName.length - 6);
                let lastSpace = truncated.lastIndexOf(' ');
                if (lastSpace > 0) {
                    truncated = truncated.substring(0, lastSpace);
                }
                seoDesc += truncated + '... | ' + brandName;
            }

            return seoDesc;
        }

        function updateBlogMetaTitle() {
            const title = document.getElementById('blogTitle')?.value || '';
            const metaTitle = document.getElementById('blogMetaTitle');

            if (metaTitle && !metaTitle.hasAttribute('data-manual')) {
                const autoTitle = generateSEOTitle(title);
                metaTitle.value = autoTitle;
            }
        }

        function updateBlogMetaKeywords() {
            const title = document.getElementById('blogTitle')?.value || '';
            const tags = document.getElementById('blogTags')?.value || '';
            const categorySelect = document.getElementById('blogCategory');
            const category = categorySelect?.options[categorySelect.selectedIndex]?.text || '';
            const metaKeywords = document.getElementById('blogMetaKeywords');

            if (metaKeywords && !metaKeywords.hasAttribute('data-manual')) {
                const autoKeywords = generateSEOKeywords(title, tags, category);
                metaKeywords.value = autoKeywords;
                updateMetaKeywordsPreview();
            }
        }

        function updateBlogMetaDescription() {
            const title = document.getElementById('blogTitle')?.value || '';
            const excerpt = document.getElementById('blogExcerpt')?.value || '';
            const metaDesc = document.getElementById('blogMetaDescription');

            if (metaDesc && !metaDesc.hasAttribute('data-manual')) {
                const autoDesc = generateSEODescription(excerpt, title);
                metaDesc.value = autoDesc;
            }
        }

        function updateAllBlogMeta() {
            updateBlogMetaTitle();
            updateBlogMetaKeywords();
            updateBlogMetaDescription();
        }

        function updateCategoryMetaDescription() {
            const name = document.getElementById('categoryName')?.value || '';
            const description = document.getElementById('categoryDescription')?.value || '';
            const metaDesc = document.getElementById('categoryMetaDescription');

            if (metaDesc && !metaDesc.hasAttribute('data-manual')) {
                const autoDesc = generateSEODescription(description, name);
                metaDesc.value = autoDesc;
            }
        }

        function toggleBlogMetaTitleEdit() {
            const metaTitle = document.getElementById('blogMetaTitle');
            const button = event.target.closest('button');

            if (metaTitle.hasAttribute('readonly')) {
                metaTitle.removeAttribute('readonly');
                metaTitle.setAttribute('data-manual', 'true');
                metaTitle.placeholder = 'Manuel olarak SEO başlığı yazın...';
                button.innerHTML = '<i class="fas fa-robot"></i> Otomatiğe Dön';
                button.parentElement.innerHTML = button.parentElement.innerHTML.replace('Otomatik:', 'Manuel:');
            } else {
                metaTitle.setAttribute('readonly', 'true');
                metaTitle.removeAttribute('data-manual');
                metaTitle.placeholder = 'Başlık yazdığınızda otomatik oluşturulacak...';
                button.innerHTML = '<i class="fas fa-edit"></i> Manuel Düzenle';
                button.parentElement.innerHTML = button.parentElement.innerHTML.replace('Manuel:', 'Otomatik:');
                updateBlogMetaTitle();
            }
        }

        function toggleBlogMetaKeywordsEdit() {
            const metaKeywords = document.getElementById('blogMetaKeywords');
            const button = event.target.closest('button');

            if (metaKeywords.hasAttribute('readonly')) {
                metaKeywords.removeAttribute('readonly');
                metaKeywords.setAttribute('data-manual', 'true');
                metaKeywords.placeholder = 'Manuel olarak anahtar kelimeler yazın...';
                button.innerHTML = '<i class="fas fa-robot"></i> Otomatiğe Dön';
                button.parentElement.innerHTML = button.parentElement.innerHTML.replace('Otomatik:', 'Manuel:');
            } else {
                metaKeywords.setAttribute('readonly', 'true');
                metaKeywords.removeAttribute('data-manual');
                metaKeywords.placeholder = 'Başlık ve etiketlerden otomatik oluşturulacak...';
                button.innerHTML = '<i class="fas fa-edit"></i> Manuel Düzenle';
                button.parentElement.innerHTML = button.parentElement.innerHTML.replace('Manuel:', 'Otomatik:');
                updateBlogMetaKeywords();
            }
        }

        function toggleBlogMetaEdit() {
            const metaDesc = document.getElementById('blogMetaDescription');
            const button = event.target.closest('button');

            if (metaDesc.hasAttribute('readonly')) {
                metaDesc.removeAttribute('readonly');
                metaDesc.setAttribute('data-manual', 'true');
                metaDesc.placeholder = 'Manuel olarak SEO açıklaması yazın...';
                button.innerHTML = '<i class="fas fa-robot"></i> Otomatiğe Dön';
                button.parentElement.innerHTML = button.parentElement.innerHTML.replace('Otomatik:', 'Manuel:');
            } else {
                metaDesc.setAttribute('readonly', 'true');
                metaDesc.removeAttribute('data-manual');
                metaDesc.placeholder = 'Özet yazdığınızda otomatik oluşturulacak...';
                button.innerHTML = '<i class="fas fa-edit"></i> Manuel Düzenle';
                button.parentElement.innerHTML = button.parentElement.innerHTML.replace('Manuel:', 'Otomatik:');
                updateBlogMetaDescription();
            }
        }

        function toggleManualMetaEdit() {
            const metaDesc = document.getElementById('categoryMetaDescription');
            const button = event.target.closest('button');

            if (metaDesc.hasAttribute('readonly')) {
                metaDesc.removeAttribute('readonly');
                metaDesc.setAttribute('data-manual', 'true');
                metaDesc.placeholder = 'Manuel olarak SEO açıklaması yazın...';
                button.innerHTML = '<i class="fas fa-robot"></i> Otomatiğe Dön';
                button.parentElement.innerHTML = button.parentElement.innerHTML.replace('Otomatik:', 'Manuel:');
            } else {
                metaDesc.setAttribute('readonly', 'true');
                metaDesc.removeAttribute('data-manual');
                metaDesc.placeholder = 'Açıklama yazdığınızda otomatik oluşturulacak...';
                button.innerHTML = '<i class="fas fa-edit"></i> Manuel Düzenle';
                button.parentElement.innerHTML = button.parentElement.innerHTML.replace('Manuel:', 'Otomatik:');
                updateCategoryMetaDescription();
            }
        }

        // Force initialization after all scripts load
        setTimeout(() => {
            console.log('All scripts loaded, checking functions...');
            console.log('loadBlogData available:', typeof loadBlogData);
            console.log('setupBlogEventListeners available:', typeof setupBlogEventListeners);
            console.log('updateStats available:', typeof updateStats);
        }, 1000);
    </script>
    <!-- Floating Quick Navigation -->
    <div class="floating-nav" id="floatingNav">
        <div class="floating-nav-toggle" onclick="toggleFloatingNav()">
            <i class="fas fa-bars"></i>
        </div>
        <div class="floating-nav-menu" id="floatingNavMenu">
            <div class="floating-nav-header">
                <h6><i class="fas fa-rocket me-2"></i>Hızlı Erişim</h6>
            </div>
            <div class="floating-nav-items">
                <a href="#productSection" class="floating-nav-item" onclick="scrollToSection('productSection')">
                    <i class="fas fa-box"></i>
                    <span>Ürün Ekleme</span>
                </a>
                <a href="#existingProducts" class="floating-nav-item" onclick="scrollToSection('existingProducts')">
                    <i class="fas fa-list"></i>
                    <span>Mevcut Ürünler</span>
                </a>
                <a href="#blogSection" class="floating-nav-item" onclick="scrollToSection('blogSection')">
                    <i class="fas fa-blog"></i>
                    <span>Blog Yönetimi</span>
                </a>
                <a href="#existingBlogs" class="floating-nav-item" onclick="scrollToSection('existingBlogs')">
                    <i class="fas fa-newspaper"></i>
                    <span>Mevcut Bloglar</span>
                </a>
                <a href="#" class="floating-nav-item" onclick="scrollToTop()">
                    <i class="fas fa-arrow-up"></i>
                    <span>Başa Dön</span>
                </a>
                <a href="#" class="floating-nav-item" onclick="scrollToBottom()">
                    <i class="fas fa-arrow-down"></i>
                    <span>Sona Git</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Category Modal -->
    <div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="categoryModalLabel">
                        <i class="fas fa-folder-plus me-2"></i>Yeni Kategori Ekle
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <div class="form-group mb-3">
                            <label for="categoryName" class="form-label">
                                <i class="fas fa-tag me-2"></i>Kategori Adı
                            </label>
                            <input type="text" class="form-control" id="categoryName" name="name" placeholder="Örn: Salamura Üretimi" required>
                        </div>
                        <div class="form-group mb-3">
                            <label for="categorySlug" class="form-label">
                                <i class="fas fa-link me-2"></i>URL Slug (Otomatik oluşturulur)
                            </label>
                            <input type="text" class="form-control" id="categorySlug" name="slug" placeholder="salamura-uretimi" readonly>
                        </div>
                        <div class="form-group mb-3">
                            <label for="categoryDescription" class="form-label">
                                <i class="fas fa-align-left me-2"></i>Açıklama
                            </label>
                            <textarea class="form-control" id="categoryDescription" name="description" rows="3" placeholder="Kategori açıklaması..."></textarea>
                        </div>
                        <div class="form-group mb-3">
                            <label for="categoryMetaDescription" class="form-label">
                                <i class="fas fa-robot me-2"></i>Meta Açıklama (SEO) - Otomatik Oluşturulur
                            </label>
                            <textarea class="form-control" id="categoryMetaDescription" name="meta_description" rows="2" placeholder="Açıklama yazdığınızda otomatik oluşturulacak..." readonly></textarea>
                            <div class="form-text">
                                <small class="text-success">
                                    🤖 <strong>Otomatik:</strong> Yukarıdaki açıklamadan SEO-optimize edilmiş meta description otomatik oluşturulur.
                                    <button type="button" class="btn btn-link btn-sm p-0 ms-2" onclick="toggleManualMetaEdit()">
                                        <i class="fas fa-edit"></i> Manuel Düzenle
                                    </button>
                                </small>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>İptal
                    </button>
                    <button type="button" class="btn btn-success" onclick="saveCategory()">
                        <i class="fas fa-save me-2"></i>Kategori Ekle
                    </button>
                </div>
            </div>
        </div>
    </div>


</body>
</html>
