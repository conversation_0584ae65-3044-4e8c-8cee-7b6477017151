<?php
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';

// Cache kontrol header'ları - API her zaman güncel olsun
header("Cache-Control: no-cache, no-store, must-revalidate, max-age=0");
header("Pragma: no-cache");
header("Expires: Thu, 01 Jan 1970 00:00:00 GMT");

setCorsHeaders();

$db = new Database();
$conn = $db->getConnection();

if (!$conn) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Veritabanı bağlantısı kurulamadı.']);
    exit;
}

try {
    // Kategori filtresi kontrolü
    $category = isset($_GET['category']) ? trim($_GET['category']) : '';

    if ($category && $category !== 'all') {
        $stmt = $conn->prepare("SELECT * FROM products WHERE category = ? ORDER BY id DESC");
        $stmt->execute([$category]);
    } else {
        $stmt = $conn->prepare("SELECT * FROM products ORDER BY id DESC");
        $stmt->execute();
    }

    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Her ürüne _id, price ve images ekle
    foreach ($products as &$product) {
        $product['_id'] = $product['id'];
        $product['price'] = $product['price'] * 100;
        // Çoklu resim desteği
        $imgStmt = $conn->prepare("SELECT image_url FROM product_images WHERE product_id = ?");
        $imgStmt->execute([$product['id']]);
        $product['images'] = $imgStmt->fetchAll(PDO::FETCH_COLUMN);
    }

    header('Content-Type: application/json');
    echo json_encode($products);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Ürünler alınamadı.', 'error' => $e->getMessage()]);
}
?>