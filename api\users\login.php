<?php
// Production mode - disable error display
ini_set('display_errors', 0);
error_reporting(0);

// <PERSON><PERSON>a yollarını kontrol et
if (!file_exists('../../config/database.php')) {
    echo json_encode([
        'success' => false,
        'message' => 'Sistem hatası'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

if (!file_exists('../../includes/functions.php')) {
    echo json_encode([
        'success' => false,
        'message' => 'Sistem hatası'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

require_once '../../config/database.php';
require_once '../../includes/functions.php';

header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// GET request için test endpoint'i
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    echo json_encode([
        'success' => true,
        'message' => 'Login API çalışıyor!',
        'method' => 'GET',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Sadece POST metodu desteklenir'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz JSON verisi'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

$email = htmlspecialchars(strip_tags(trim($input['email'] ?? '')));
$password = $input['password'] ?? '';

if (empty($email) || empty($password)) {
    echo json_encode([
        'success' => false,
        'message' => 'Email ve şifre gereklidir'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz email adresi'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // Rate limiting ve brute force koruması
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $identifier = $email . '_' . $clientIP;

    // Brute force kontrolü
    if (!checkBruteForce($identifier)) {
        http_response_code(429);
        echo json_encode([
            'success' => false,
            'message' => 'Çok fazla başarısız giriş denemesi. 15 dakika sonra tekrar deneyin.'
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }

    // Rate limiting
    enforceRateLimit($clientIP, 10, 300); // 5 dakikada 10 istek

    $database = new Database();
    $db = $database->getConnection();

    $stmt = $db->prepare("SELECT id, name, email, password, role FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();

    if (!$user || !password_verify($password, $user['password'])) {
        // Başarısız girişi kaydet
        recordFailedAttempt($identifier);

        echo json_encode([
        'success' => false,
        'message' => 'Email veya şifre hatalı'
    ], JSON_UNESCAPED_UNICODE);
    exit();
    }
    
    // Session token oluştur
    $token = bin2hex(random_bytes(32));
    $_SESSION['user_token'] = $token;
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['token_created'] = time();
    
    $response_data = [
        'id' => $user['id'],
        'name' => $user['name'],
        'email' => $user['email'],
        'role' => $user['role'],
        'token' => $token
    ];
    
    echo json_encode([
        'success' => true,
        'message' => 'Giriş başarılı',
        'data' => $response_data
    ], JSON_UNESCAPED_UNICODE);
    exit();
    
} catch(Exception $e) {
    error_log('Login error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Sunucu hatası'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}
?>
