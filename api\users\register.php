<?php
require_once '../../config/database.php';
require_once '../../includes/functions.php';

header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// GET request için test endpoint'i
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    echo json_encode([
        'success' => true,
        'message' => 'Register API çalışıyor!',
        'method' => 'GET',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Sadece POST metodu desteklenir'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

// JSON input al
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz JSON verisi'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

// Gerekli alanları kontrol et
$required_fields = ['name', 'email', 'password'];
foreach ($required_fields as $field) {
    if (!isset($input[$field]) || empty(trim($input[$field]))) {
        echo json_encode([
            'success' => false,
            'message' => "$field alanı gereklidir"
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

$name = htmlspecialchars(strip_tags(trim($input['name'])));
$email = htmlspecialchars(strip_tags(trim($input['email'])));
$password = $input['password'];

// Email validation
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz email adresi'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

// Password validation
if (strlen($password) < 6) {
    echo json_encode([
        'success' => false,
        'message' => 'Şifre en az 6 karakter olmalıdır'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Email kontrolü
    $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    
    if ($stmt->fetch()) {
        echo json_encode([
            'success' => false,
            'message' => 'Bu email adresi zaten kayıtlı'
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    // Kullanıcı oluştur
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    $stmt = $db->prepare("INSERT INTO users (name, email, password) VALUES (?, ?, ?)");
    
    if ($stmt->execute([$name, $email, $hashed_password])) {
        echo json_encode([
            'success' => true,
            'message' => 'Kayıt başarılı! Şimdi giriş yapabilirsiniz.'
        ], JSON_UNESCAPED_UNICODE);
        exit();
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Kayıt sırasında bir hata oluştu'
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
} catch(Exception $e) {
    error_log('Register error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Sunucu hatası'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}
?>
