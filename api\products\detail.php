<?php
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Cache kontrol header'ları - API her zaman güncel olsun
header("Cache-Control: no-cache, no-store, must-revalidate, max-age=0");
header("Pragma: no-cache");
header("Expires: Thu, 01 Jan 1970 00:00:00 GMT");

setCorsHeaders();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $product_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if ($product_id <= 0) {
            sendErrorResponse('Geçersiz ürün ID', 400);
        }
        
        $stmt = $db->prepare("SELECT * FROM products WHERE id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch();
        
        if (!$product) {
            sendErrorResponse('Ürün bulunamadı', 404);
        }
        
        // MongoDB uyumluluğu için _id ekle
        $product['_id'] = $product['id'];
        // Fiyatı cent cinsinden döndür
        $product['price'] = $product['price'] * 100;

        // Çoklu resim desteği
        $imgStmt = $db->prepare("SELECT image_url FROM product_images WHERE product_id = ?");
        $imgStmt->execute([$product['id']]);
        $product['images'] = $imgStmt->fetchAll(PDO::FETCH_COLUMN);

        sendJsonResponse($product);
        
    } else {
        sendErrorResponse('Desteklenmeyen HTTP metodu', 405);
    }
    
} catch(Exception $e) {
    logError('Product detail API error: ' . $e->getMessage());
    sendErrorResponse('Sunucu hatası');
}
?>
