<?php
// Production ayarları - Hataları gizle
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_error.log');
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);

// Content Security Policy - Comprehensive for all external resources
if (!headers_sent()) {
    header("Content-Security-Policy: " .
        "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; " .
        "style-src-elem 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; " .
        "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
        "img-src 'self' data: https: http:; " .
        "connect-src 'self' https:;"
    );
}

// Veritabanı sınıfı
class Database {
    private $host = 'zeus.wlsrv.com';
    private $db_name = 'tansusah_salamura';
    private $username = 'tansusah';
    private $password = '.3qp3pi4Qp.Tz(';
    private $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            // Bağlantı charset ile daha sağlam yapılır
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password,
                array(
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
                )
            );
            // Additional UTF-8 settings
            $this->conn->exec("SET CHARACTER SET utf8mb4");
            $this->conn->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
        } catch(PDOException $exception) {
            error_log("MySQL bağlantı hatası: " . $exception->getMessage());
            // Ekrana yazdırma, sadece logla!
        }

        return $this->conn;
    }
}

?>
