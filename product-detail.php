<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> Salam<PERSON></title>
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">

    <link rel="stylesheet" href="styles.css?v=2.0.1">
    <style>
        /* Product Detail Specific Styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, var(--bg-green) 0%, #e6ffe6 100%);
            color: var(--dark-green);
            line-height: 1.6;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* Main Content */
        main.container {
            flex-grow: 1;
            position: relative;
            z-index: 2;
            max-width: 1200px;
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        /* Product Detail Container */
        .product-detail-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            margin: 0 auto 2rem auto;
            box-shadow: 0 20px 40px rgba(34, 139, 34, 0.15);
            border: 2px solid var(--light-green);
            backdrop-filter: blur(15px);
            max-width: 1100px;
        }

        /* Professional Image Gallery Styles */
        .product-gallery {
            position: relative;
            background: white;
            border-radius: 20px;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid var(--light-green);
        }

        .main-image-container {
            position: relative;
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 0.5rem;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-product-image {
            width: 100%;
            height: 400px;
            object-fit: contain;
            transition: transform 0.3s ease;
            cursor: zoom-in;
        }

        .main-product-image:hover {
            transform: scale(1.05);
        }

        .gallery-nav-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: var(--primary-green);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .gallery-nav-btn:hover {
            background: var(--primary-green);
            color: white;
            transform: translateY(-50%) scale(1.1);
        }

        .gallery-nav-btn.prev {
            left: 15px;
        }

        .gallery-nav-btn.next {
            right: 15px;
        }

        .thumbnail-container {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding: 10px 0;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-green) #f1f1f1;
        }

        .thumbnail-container::-webkit-scrollbar {
            height: 6px;
        }

        .thumbnail-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .thumbnail-container::-webkit-scrollbar-thumb {
            background: var(--primary-green);
            border-radius: 10px;
        }

        .thumbnail-item {
            flex-shrink: 0;
            width: 80px;
            height: 80px;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            border: 3px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .thumbnail-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .thumbnail-item.active {
            border-color: var(--primary-green);
            box-shadow: 0 0 0 2px rgba(34, 139, 34, 0.3);
        }

        .thumbnail-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-counter {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .fullscreen-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fullscreen-btn:hover {
            background: var(--primary-green);
            transform: scale(1.1);
        }

        /* Lightbox Modal */
        .lightbox-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 9999;
            align-items: center;
            justify-content: center;
        }

        .lightbox-modal.active {
            display: flex;
        }

        .lightbox-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
        }

        .lightbox-image {
            width: 100%;
            height: auto;
            max-height: 90vh;
            object-fit: contain;
        }

        .lightbox-close {
            position: absolute;
            top: -50px;
            right: 0;
            background: none;
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            padding: 10px;
        }

        .lightbox-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid rgba(255, 255, 255, 0.8);
            color: white;
            font-size: 2rem;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 50%;
            width: 70px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .lightbox-nav:hover {
            background: rgba(34, 139, 34, 0.9);
            border-color: rgba(255, 255, 255, 1);
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
        }

        .lightbox-nav.prev {
            left: 20px;
        }

        .lightbox-nav.next {
            right: 20px;
        }

        /* Mobil cihazlar için lightbox arrow'ları */
        @media (max-width: 768px) {
            .lightbox-nav {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
                padding: 10px 15px;
            }
            
            .lightbox-nav.prev {
                left: 10px;
            }
            
            .lightbox-nav.next {
                right: 10px;
            }
        }

        @media (max-width: 480px) {
            .lightbox-nav {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
                padding: 8px 12px;
            }
            
            .lightbox-nav.prev {
                left: 5px;
            }
            
            .lightbox-nav.next {
                right: 5px;
            }
        }

        .product-title {
            font-size: 2.2rem;
            font-weight: 800;
            color: #333;
            line-height: 1.2;
            margin: 0;
        }

        .product-price-display {
            font-size: 2.8rem;
            font-weight: 900;
            color: #007bff;
            margin: 0;
        }

        .btn-trendyol {
            background: linear-gradient(45deg, #6f42c1, #8b5cf6) !important;
            color: white !important;
            border: none !important;
            padding: 1.1rem 2.2rem !important;
            font-size: 1.1rem !important;
            font-weight: 700 !important;
            border-radius: 50px !important;
            text-decoration: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 0.6rem !important;
            box-shadow: 0 10px 25px rgba(111, 66, 193, 0.4) !important;
            transition: all 0.3s ease !important;
            width: 100%;
        }

        .btn-trendyol:hover {
            transform: translateY(-3px) scale(1.02) !important;
            box-shadow: 0 15px 35px rgba(111, 66, 193, 0.5) !important;
            color: white !important;
        }

        /* Loading State */
        .loading-placeholder {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--dark-green);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 300px;
        }

        .spinner-border {
            color: var(--primary-green) !important;
            width: 4rem !important;
            height: 4rem !important;
        }

        .product-description-compact {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1rem;
            border-left: 4px solid var(--primary-green);
            max-height: 200px;
            overflow-y: auto;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .product-description-compact::-webkit-scrollbar {
            width: 6px;
        }

        .product-description-compact::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .product-description-compact::-webkit-scrollbar-thumb {
            background: var(--primary-green);
            border-radius: 10px;
        }

        .product-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .info-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
        }

        .info-card-icon {
            font-size: 1.5rem;
            color: var(--primary-green);
            margin-bottom: 0.5rem;
        }

        .info-card-title {
            font-size: 0.85rem;
            font-weight: 600;
            color: #495057;
            margin: 0;
        }

        .info-card-value {
            font-size: 1.1rem;
            font-weight: 700;
            color: #212529;
            margin: 0;
        }

        /* Mobile Action Buttons */
        .mobile-action-buttons {
            margin-top: 1rem;
            margin-bottom: 1rem;
        }

        /* Masaüstünde favori butonundaki kalp ikonunu ortala */
        @media (min-width: 768px) {
            .mobile-action-buttons .favorite-btn-detail {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .mobile-action-buttons .favorite-btn-detail i {
                margin: 0 !important;
            }
        }

        .mobile-quick-info {
            margin-top: 1rem;
        }

        /* Responsive Design - Daha spesifik mobil düzenlemeler */
        @media (max-width: 767.98px) {
            /* Mobile Action Buttons - Tüm butonları aynı boyutta yap */
            .mobile-action-buttons .btn {
                font-size: 0.9rem !important;
                padding: 0.75rem 0.5rem !important;
                border-radius: 20px !important;
                height: 45px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                min-height: 45px !important;
                width: 100% !important;
            }

            /* Favoriler butonunu diğerleriyle aynı yap */
            .mobile-action-buttons .favorite-btn-detail {
                border-radius: 20px !important;
                height: 45px !important;
                min-height: 45px !important;
                padding: 0.75rem 0.5rem !important;
                font-size: 0.9rem !important;
                width: 100% !important;
            }

            .mobile-action-buttons .favorite-btn-detail i {
                font-size: 0.9rem !important;
            }

            .container {
                padding-left: 0.5rem !important;
                padding-right: 0.5rem !important;
            }

            .product-detail-container {
                padding: 0.75rem !important;
                margin: 0.25rem !important;
                border-radius: 15px !important;
            }
            
            .main-product-image {
                height: 250px !important;
            }
            
            .main-image-container {
                min-height: 250px !important;
                margin-bottom: 0.5rem !important;
            }
            
            .product-title {
                font-size: 1.4rem !important;
                margin-bottom: 0.75rem !important;
                line-height: 1.3 !important;
            }
            
            .product-price-display {
                font-size: 1.8rem !important;
            }

            .gallery-nav-btn {
                width: 35px !important;
                height: 35px !important;
                font-size: 0.9rem !important;
            }

            .thumbnail-item {
                width: 50px !important;
                height: 50px !important;
            }

            .product-gallery {
                padding: 0.5rem !important;
                margin-bottom: 0.75rem !important;
            }

            .mobile-action-buttons {
                margin-top: 0.75rem !important;
                margin-bottom: 0.75rem !important;
            }

            .mobile-quick-info {
                margin-top: 0.75rem !important;
            }

            .mobile-quick-info .alert {
                padding: 0.6rem !important;
                font-size: 0.8rem !important;
                margin-bottom: 0 !important;
            }

            .mobile-quick-info .alert-heading {
                font-size: 0.85rem !important;
                margin-bottom: 0.5rem !important;
            }

            .mobile-quick-info .row.g-2 > * {
                padding: 0.2rem !important;
                font-size: 0.75rem !important;
            }

            .product-info-grid {
                gap: 0.5rem !important;
                margin-bottom: 1rem !important;
            }

            .info-card {
                padding: 0.6rem !important;
            }

            .info-card-icon {
                font-size: 1.1rem !important;
                margin-bottom: 0.3rem !important;
            }

            .info-card-title {
                font-size: 0.75rem !important;
            }

            .info-card-value {
                font-size: 0.9rem !important;
            }

            .product-description-compact {
                max-height: 120px !important;
                padding: 0.6rem !important;
                font-size: 0.8rem !important;
                margin-bottom: 1rem !important;
            }

            .btn-trendyol {
                padding: 0.9rem 1.2rem !important;
                font-size: 0.95rem !important;
                margin-bottom: 1rem !important;
            }

            /* Breadcrumb mobilde daha küçük */
            .breadcrumb {
                font-size: 0.75rem !important;
                margin-bottom: 0.5rem !important;
                padding: 0.5rem 0 !important;
            }

            .breadcrumb-item + .breadcrumb-item::before {
                font-size: 0.7rem !important;
            }

            /* Kategori bilgisi mobilde */
            .text-muted {
                font-size: 0.8rem !important;
                margin-bottom: 0.75rem !important;
            }

            /* Video section mobilde */
            .ratio.ratio-16x9 {
                --bs-aspect-ratio: 56.25% !important;
            }

            /* Grid sistemini mobilde zorla tek sütun yap */
            .row.g-lg-5.g-md-4.g-3 > .col-lg-6,
            .row.g-lg-5.g-md-4.g-3 > .col-md-6 {
                flex: 0 0 100% !important;
                max-width: 100% !important;
            }

            /* Sol taraf padding'i kaldır */
            .ps-lg-3 {
                padding-left: 0 !important;
            }
        }

        @media (max-width: 575.98px) {
            /* Çok küçük ekranlar için */
            .mobile-action-buttons .btn {
                font-size: 0.85rem !important;
                padding: 0.7rem 0.4rem !important;
                height: 42px !important;
                min-height: 42px !important;
                border-radius: 20px !important;
            }

            .mobile-action-buttons .favorite-btn-detail {
                height: 42px !important;
                min-height: 42px !important;
                padding: 0.7rem 0.4rem !important;
                border-radius: 20px !important;
                font-size: 0.85rem !important;
            }

            .mobile-action-buttons .favorite-btn-detail i {
                font-size: 0.85rem !important;
            }

            main.container {
                padding-top: 0.5rem !important;
                padding-bottom: 0.5rem !important;
            }

            .product-detail-container {
                margin: 0.1rem !important;
                padding: 0.5rem !important;
            }

            .product-title {
                font-size: 1.2rem !important;
            }

            /* Mobile Action Buttons - Daha spesifik düzenlemeler */
            .mobile-action-buttons .btn {
                font-size: 0.75rem !important;
                padding: 0.4rem 0.2rem !important;
                height: 36px !important;
                min-height: 36px !important;
            }

            .mobile-action-buttons .favorite-btn-detail {
                height: 36px !important;
                min-height: 36px !important;
                padding: 0.4rem 0.2rem !important;
            }

            .mobile-quick-info .alert-heading {
                font-size: 0.8rem !important;
            }

            .mobile-quick-info .row.g-2 > * {
                font-size: 0.7rem !important;
            }

            .btn-trendyol {
                padding: 0.8rem 1rem !important;
                font-size: 0.9rem !important;
            }

            .main-product-image {
                height: 220px !important;
            }
            
            .main-image-container {
                min-height: 220px !important;
            }
        }

        /* Breadcrumb Overlay - Resmin üstüne yerleştir */
        .breadcrumb-overlay {
          position: absolute;
          top: 15px;
          left: 15px;
          right: 15px;
          z-index: 15;
          background: rgba(0, 0, 0, 0.7);
          border-radius: 10px;
          padding: 8px 12px;
          backdrop-filter: blur(10px);
        }

        .breadcrumb-overlay .breadcrumb {
          margin: 0;
          background: transparent;
          padding: 0;
          font-size: 0.8rem;
        }

        .breadcrumb-overlay .breadcrumb-item a {
          color: rgba(255, 255, 255, 0.9);
          text-decoration: none;
          font-weight: 500;
        }

        .breadcrumb-overlay .breadcrumb-item a:hover {
          color: white;
          text-decoration: underline;
        }

        .breadcrumb-overlay .breadcrumb-item.active {
          color: white;
          font-weight: 600;
        }

        .breadcrumb-overlay .breadcrumb-item + .breadcrumb-item::before {
          content: ">";
          color: rgba(255, 255, 255, 0.7);
          margin: 0 0.5rem;
        }

        /* Mobil için breadcrumb overlay */
        @media (max-width: 767.98px) {
          .breadcrumb-overlay {
            top: 10px;
            left: 10px;
            right: 10px;
            padding: 6px 10px;
          }
          
          .breadcrumb-overlay .breadcrumb {
            font-size: 0.7rem;
          }
          
          .breadcrumb-overlay .breadcrumb-item + .breadcrumb-item::before {
            margin: 0 0.3rem;
          }
        }

        @media (max-width: 575.98px) {
          .breadcrumb-overlay {
            top: 8px;
            left: 8px;
            right: 8px;
            padding: 5px 8px;
          }
          
          .breadcrumb-overlay .breadcrumb {
            font-size: 0.65rem;
          }
        }
    </style>
</head>
<body>
    <!-- Floating vegetables background -->
    <div class="floating-veggies">
        <div class="veggie">🥒</div>
        <div class="veggie">🥬</div>
        <div class="veggie">🥦</div>
        <div class="veggie">🌿</div>
        <div class="veggie">🥒</div>
        <div class="veggie">🥬</div>
        <div class="veggie">🥦</div>
        <div class="veggie">🌿</div>
        <div class="veggie">🥒</div>
        <div class="veggie">🥬</div>
    </div>

    <!-- NAVBAR - Ana sayfa ile birebir aynı -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm sticky-top main-navbar">
        <div class="container">
            <a class="navbar-brand fw-bold fs-4 d-flex align-items-center" href="/">
                <img src="images/logo.jpg" alt="Logo" style="height: 50px; margin-right: 10px;">
                <span class="brand-text">
                    <span class="brand-line-1">Tansu Şahal</span>
                    <span class="brand-line-2">Salamura</span>
                </span>
            </a>

            <button class="navbar-toggler" type="button" onclick="toggleIndependentMobileMenu()" aria-label="Menüyü aç/kapat">
                <span class="navbar-toggler-icon"></span>
                <span class="menu-text">Menü</span>
            </button>
            
            <div class="collapse navbar-collapse" id="mainNav">
                <!-- Desktop Menu -->
                <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
                    <li class="nav-item"><a class="nav-link" href="/">Ana Sayfa</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#products">Tüm Ürünler</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#about-us-section">Hakkımızda</a></li>
                    <li class="nav-item"><a class="nav-link" href="/blog.php">Blog</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#contact-section">İletişim</a></li>
                </ul>
                <div class="d-flex align-items-center navbar-user-info">
                    <span id="userGreeting" class="navbar-text navbar-user-greeting me-3" style="display:none;"></span>
                    <div class="auth-links-group">
                        <a href="login.php" id="loginLink" class="text-dark text-decoration-none auth-link">Giriş Yap</a>
                        <a href="register.php" id="registerLink" class="text-dark text-decoration-none auth-link">Kayıt Ol</a>
                        <a href="#" id="logoutLink" class="text-dark text-decoration-none auth-link" style="display:none;">Çıkış Yap</a>
                        <a href="admin.php" id="adminLink" class="text-dark text-decoration-none auth-link" style="display:none;">Admin</a>
                        <a href="favorites.php" class="btn btn-outline-danger position-relative rounded-pill px-3">
                            <i class="fas fa-heart me-1"></i> Favoriler
                            <span id="favorites-item-count" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger border border-light">0</span>
                        </a>
                    </div>
                </div>
                
                <!-- Mobile Menu -->
                <div class="mobile-menu-container">
                    <button class="mobile-menu-close" type="button" data-bs-toggle="collapse" data-bs-target="#mainNav" aria-label="Menüyü kapat">
                        <span class="sr-only">Kapat</span>
                    </button>
                    
                    <div class="mobile-menu-header">
                        <div class="brand-text">
                            <span class="brand-line-1">Tansu Şahal</span>
                            <span class="brand-line-2">Salamura</span>
                        </div>
                    </div>
                    
                    <ul class="mobile-nav-links">
                        <li class="nav-item"><a class="nav-link" href="/" onclick="closeMobileMenu()">🏠 Ana Sayfa</a></li>
                        <li class="nav-item"><a class="nav-link" href="/#products" onclick="closeMobileMenu()">🛍️ Tüm Ürünler</a></li>
                        <li class="nav-item"><a class="nav-link" href="/#about-us-section" onclick="closeMobileMenu()">🌿 Hakkımızda</a></li>
                        <li class="nav-item"><a class="nav-link" href="/blog.php" onclick="closeMobileMenu()">📝 Blog</a></li>
                        <li class="nav-item"><a class="nav-link" href="/#contact-section" onclick="closeMobileMenu()">📞 İletişim</a></li>
                    </ul>
                    
                    <div class="mobile-user-section">
                        <div id="mobileUserGreeting" class="mobile-user-greeting" style="display:none;"></div>
                        
                        <div class="mobile-auth-buttons">
                            <a href="login.php" id="mobileLoginLink" class="mobile-auth-button secondary" onclick="closeMobileMenu()">
                                <i class="fas fa-sign-in-alt"></i> Giriş Yap
                            </a>
                            <a href="register.php" id="mobileRegisterLink" class="mobile-auth-button primary" onclick="closeMobileMenu()">
                                <i class="fas fa-user-plus"></i> Kayıt Ol
                            </a>
                            <a href="#" id="mobileLogoutLink" class="mobile-auth-button secondary" style="display:none;" onclick="handleMobileLogout()">
                                <i class="fas fa-sign-out-alt"></i> Çıkış Yap
                            </a>
                            <a href="admin.php" id="mobileAdminLink" class="mobile-auth-button primary" style="display:none;" onclick="closeMobileMenu()">
                                <i class="fas fa-cog"></i> Admin
                            </a>
                        </div>
                        
                        <a href="favorites.php" class="mobile-favorites-button" aria-label="Favoriler" onclick="closeMobileMenu()">
                            <i class="fas fa-heart"></i> Favoriler
                            <span id="mobileFavoritesCount" class="badge bg-white text-danger">0</span>
                        </a>
                    </div>
                    
                    <div class="mobile-menu-footer">
                        <p>🌱 Doğallığın ve Lezzetin Adresi</p>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <main class="container">
        <!-- Product Detail Container -->
        <div class="product-detail-container">
            <!-- Loading Indicator -->
            <div class="loading-placeholder" id="loadingIndicator">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Yükleniyor...</span>
                </div>
                <p class="mt-4 fs-4">Ürün bilgileri yükleniyor...</p>
            </div>

            <!-- Product Detail Content (JS ile doldurulacak) -->
            <div id="product-detail-content" class="row g-lg-5 g-md-4 g-3 align-items-start">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </main>

    <!-- Lightbox Modal -->
    <div id="lightboxModal" class="lightbox-modal">
        <div class="lightbox-content">
            <button class="lightbox-close" onclick="closeLightbox()">&times;</button>
            <button class="lightbox-nav prev" onclick="previousImage()">&#8249;</button>
            <button class="lightbox-nav next" onclick="nextImage()">&#8250;</button>
            <img id="lightboxImage" class="lightbox-image" src="/placeholder.svg" alt="">
        </div>
    </div>

    <footer class="bg-dark text-white text-center p-4 mt-auto">
        <div class="container">
            <p class="mb-1">© 2025 Tansu Şahal Salamura. Tüm hakları saklıdır. 🌿</p>
            <p class="small"><a href="#" class="text-white-50">Gizlilik Politikası</a> | <a href="#" class="text-white-50">Kullanım Şartları</a></p>
            <p class="small text-white-50 mt-2">
                <i class="fas fa-code me-1"></i>
                Web Tasarımı: <a href="mailto:<EMAIL>" class="text-white-50"><EMAIL></a>
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="header-updater.js"></script>
    <script src="product-detail-scripts.js"></script>
    <script src="mobile-menu.js"></script> <!-- Universal mobile menu system -->
    
    <!-- Mobile Menu Kapatma Fonksiyonu -->
    <script>
    function closeMobileMenu() {
        const navbarCollapse = document.getElementById('mainNav');
        const navbarToggler = document.querySelector('.navbar-toggler');
        
        if (navbarCollapse && navbarCollapse.classList.contains('show')) {
            navbarToggler.click();
        }
    }
    </script>
</body>
</html>
