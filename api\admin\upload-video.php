<?php
require_once '../../config/database.php';
require_once '../../includes/functions.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendErrorResponse('Sadece POST metodu desteklenir', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Token kontrolü
    $token = getBearerToken();
    if (!$token || !validateSessionToken($token)) {
        sendErrorResponse('Geçersiz token', 401);
    }
    
    requireAdmin($db);
    
    if (!isset($_FILES['video'])) {
        sendErrorResponse('Video dosyası seçilmedi');
    }
    
    $video_file = $_FILES['video'];
    
    // Video dosya kontrolü
    $allowed_video_types = ['mp4', 'avi', 'mov', 'wmv', 'webm'];
    $file_extension = strtolower(pathinfo($video_file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_video_types)) {
        sendErrorResponse('Desteklenmeyen video formatı. Desteklenen: ' . implode(', ', $allowed_video_types));
    }
    
    // Dosya boyutu kontrolü (100MB)
    if ($video_file['size'] > 100 * 1024 * 1024) {
        sendErrorResponse('Video dosyası 100MB\'dan büyük olamaz');
    }
    
    // Upload dizini oluştur
    $upload_dir = '../../uploads/videos/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Benzersiz dosya adı oluştur
    $filename = uniqid('video_') . '_' . time() . '.' . $file_extension;
    $filepath = $upload_dir . $filename;
    
    if (move_uploaded_file($video_file['tmp_name'], $filepath)) {
        $video_url = '/uploads/videos/' . $filename;
        sendSuccessResponse(['videoUrl' => $video_url], 'Video başarıyla yüklendi');
    } else {
        sendErrorResponse('Video yüklenirken hata oluştu');
    }
    
} catch(Exception $e) {
    logError('Video upload error: ' . $e->getMessage());
    sendErrorResponse('Sunucu hatası');
}
?>
