<?php
/**
 * Dinamik Sitemap Generator
 * Blog yazıları ve ürünler için otomatik sitemap oluşturur
 */

require_once 'config/database.php';

header('Content-Type: application/xml; charset=utf-8');

// Cache sistemi - 1 saatte bir güncelle
$cacheFile = 'cache/sitemap.xml';
$cacheTime = 3600; // 1 saat

if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $cacheTime) {
    // Cache'den oku
    readfile($cacheFile);
    exit;
}

// Cache klasörü yoksa oluştur
if (!is_dir('cache')) {
    mkdir('cache', 0755, true);
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // Output buffering başlat (cache için)
    ob_start();

    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    // Ana sayfalar
    $staticPages = [
        ['url' => '', 'priority' => '1.0', 'changefreq' => 'weekly'],
        ['url' => 'blog.php', 'priority' => '0.9', 'changefreq' => 'daily'],
        ['url' => 'product-detail.php', 'priority' => '0.8', 'changefreq' => 'weekly'],
        ['url' => 'blog-detail.php', 'priority' => '0.8', 'changefreq' => 'weekly'],
        ['url' => 'favorites.php', 'priority' => '0.6', 'changefreq' => 'monthly'],
        ['url' => 'login.php', 'priority' => '0.4', 'changefreq' => 'monthly'],
        ['url' => 'register.php', 'priority' => '0.4', 'changefreq' => 'monthly'],
        ['url' => 'gizlilik-politikasi.php', 'priority' => '0.3', 'changefreq' => 'yearly'],
        ['url' => 'kullanim-sartlari.php', 'priority' => '0.3', 'changefreq' => 'yearly']
    ];
    
    foreach ($staticPages as $page) {
        echo "  <url>\n";
        echo "    <loc>https://tansusahalsalamura.com.tr/" . $page['url'] . "</loc>\n";
        echo "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
        echo "    <changefreq>" . $page['changefreq'] . "</changefreq>\n";
        echo "    <priority>" . $page['priority'] . "</priority>\n";
        echo "  </url>\n";
    }
    
    // Blog yazıları
    $blogQuery = "SELECT id, title, slug, created_at, updated_at FROM blog_posts WHERE status = 'published' ORDER BY created_at DESC";
    $blogStmt = $db->prepare($blogQuery);
    $blogStmt->execute();
    $blogPosts = $blogStmt->fetchAll();
    
    foreach ($blogPosts as $post) {
        $lastmod = $post['updated_at'] ? $post['updated_at'] : $post['created_at'];
        echo "  <url>\n";
        // Pretty URL kullan (slug varsa)
        if (!empty($post['slug'])) {
            echo "    <loc>https://tansusahalsalamura.com.tr/blog/" . $post['slug'] . "</loc>\n";
        } else {
            echo "    <loc>https://tansusahalsalamura.com.tr/blog-detail.php?id=" . $post['id'] . "</loc>\n";
        }
        echo "    <lastmod>" . date('Y-m-d', strtotime($lastmod)) . "</lastmod>\n";
        echo "    <changefreq>weekly</changefreq>\n";
        echo "    <priority>0.7</priority>\n";
        echo "  </url>\n";
    }
    
    // Ürünler (eğer ürün tablosu varsa)
    try {
        $productQuery = "SELECT id, name, created_at, updated_at FROM products WHERE status = 'active' ORDER BY created_at DESC";
        $productStmt = $db->prepare($productQuery);
        $productStmt->execute();
        $products = $productStmt->fetchAll();
        
        foreach ($products as $product) {
            $lastmod = $product['updated_at'] ? $product['updated_at'] : $product['created_at'];
            echo "  <url>\n";
            echo "    <loc>https://tansusahalsalamura.com.tr/product-detail.php?id=" . $product['id'] . "</loc>\n";
            echo "    <lastmod>" . date('Y-m-d', strtotime($lastmod)) . "</lastmod>\n";
            echo "    <changefreq>monthly</changefreq>\n";
            echo "    <priority>0.6</priority>\n";
            echo "  </url>\n";
        }
    } catch (Exception $e) {
        // Ürün tablosu yoksa sessizce devam et
    }
    
    echo '</urlset>';

    // Cache'e kaydet
    $content = ob_get_contents();
    file_put_contents($cacheFile, $content);

    // Output'u gönder
    ob_end_flush();

} catch (Exception $e) {
    // Buffer'ı temizle
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Hata durumunda statik backup sitemap'e yönlendir
    header('Location: /sitemap-static-backup.xml');
    exit;
}
?>
