<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// CORS ayarları
function setCorsHeaders() {
    header("Access-Control-Allow-Origin: *");
    header("Content-Type: application/json; charset=UTF-8");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Max-Age: 3600");
    header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
    
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

// JSON response gönder
function sendJsonResponse($data, $status_code = 200) {
    // Clean output buffer before sending JSON
    if (ob_get_level()) {
        ob_clean();
    }

    http_response_code($status_code);
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
    exit();
}

// Hata response gönder
function sendErrorResponse($message, $status_code = 400) {
    http_response_code($status_code);
    echo json_encode(['error' => $message], JSON_UNESCAPED_UNICODE);
    exit();
}

// Başarı response gönder
function sendSuccessResponse($data = [], $message = 'İşlem başarılı') {
    $response = ['success' => true, 'message' => $message];
    if (!empty($data)) {
        $response['data'] = $data;
    }
    sendJsonResponse($response);
}

// Input sanitize - XSS koruması
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

// HTML içeriği için güvenli escape
function escapeHtml($data) {
    return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}

// JSON output için güvenli escape
function escapeJson($data) {
    return json_encode($data, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE);
}

// Email validation
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Password hash
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Password verify
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// JWT benzeri session token oluştur
function generateSessionToken($user_id) {
    $token = bin2hex(random_bytes(32));
    $_SESSION['user_token'] = $token;
    $_SESSION['user_id'] = $user_id;
    $_SESSION['token_created'] = time();
    return $token;
}

// Session token doğrula
function validateSessionToken($token) {
    if (!isset($_SESSION['user_token']) || 
        !isset($_SESSION['user_id']) || 
        !isset($_SESSION['token_created'])) {
        return false;
    }
    
    // Token 24 saat geçerli
    if (time() - $_SESSION['token_created'] > 86400) {
        session_destroy();
        return false;
    }
    
    return $_SESSION['user_token'] === $token;
}

// Kullanıcı bilgilerini al
function getCurrentUser($db) {
    if (!isset($_SESSION['user_id'])) {
        return null;
    }
    
    try {
        $stmt = $db->prepare("SELECT id, name, email, role FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    } catch(Exception $e) {
        return null;
    }
}

// Admin kontrolü
function requireAdmin($db) {
    $user = getCurrentUser($db);
    if (!$user || $user['role'] !== 'admin') {
        sendErrorResponse('Bu işlem için admin yetkisi gereklidir', 403);
    }
    return $user;
}

// Authorization header'dan token al
function getBearerToken() {
    $headers = getallheaders();
    if (isset($headers['Authorization'])) {
        $matches = [];
        if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }
    return null;
}

// File upload helper
function handleFileUpload($file, $upload_dir = 'uploads/', $allowed_types = ['jpg', 'jpeg', 'png', 'gif']) {
    if (!isset($file['error']) || is_array($file['error'])) {
        throw new RuntimeException('Geçersiz dosya parametresi');
    }

    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_NO_FILE:
            throw new RuntimeException('Dosya seçilmedi');
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            throw new RuntimeException('Dosya boyutu çok büyük');
        default:
            throw new RuntimeException('Bilinmeyen dosya hatası');
    }

    if ($file['size'] > 10000000) { // 10MB
        throw new RuntimeException('Dosya boyutu 10MB\'dan büyük olamaz');
    }

    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mime_type = $finfo->file($file['tmp_name']);
    
    $allowed_mimes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg', 
        'png' => 'image/png',
        'gif' => 'image/gif'
    ];
    
    $ext = array_search($mime_type, $allowed_mimes, true);
    if ($ext === false) {
        throw new RuntimeException('Geçersiz dosya formatı');
    }

    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    $filename = sprintf('%s.%s', sha1_file($file['tmp_name']), $ext);
    $filepath = $upload_dir . $filename;

    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new RuntimeException('Dosya yüklenemedi');
    }

    return '/' . $filepath;
}

// Log function
function logError($message) {
    error_log(date('[Y-m-d H:i:s] ') . $message . PHP_EOL, 3, 'logs/error.log');
}

function validateInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// CSRF Token Fonksiyonları
function generateCSRFToken() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $token;
    return $token;
}

function validateCSRFToken($token) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

function requireCSRFToken() {
    $token = $_POST['csrf_token'] ?? $_GET['csrf_token'] ?? '';
    if (!validateCSRFToken($token)) {
        http_response_code(403);
        die(json_encode(['success' => false, 'message' => 'CSRF token geçersiz']));
    }
}

// Rate Limiting Fonksiyonları
function checkRateLimit($identifier, $maxRequests = 60, $timeWindow = 3600) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    $key = 'rate_limit_' . md5($identifier);
    $now = time();

    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['count' => 1, 'start_time' => $now];
        return true;
    }

    $data = $_SESSION[$key];

    // Zaman penceresi sıfırlandı mı?
    if ($now - $data['start_time'] > $timeWindow) {
        $_SESSION[$key] = ['count' => 1, 'start_time' => $now];
        return true;
    }

    // Limit aşıldı mı?
    if ($data['count'] >= $maxRequests) {
        return false;
    }

    // Sayacı artır
    $_SESSION[$key]['count']++;
    return true;
}

function enforceRateLimit($identifier, $maxRequests = 60, $timeWindow = 3600) {
    if (!checkRateLimit($identifier, $maxRequests, $timeWindow)) {
        http_response_code(429);
        die(json_encode([
            'success' => false,
            'message' => 'Çok fazla istek. Lütfen daha sonra tekrar deneyin.',
            'retry_after' => $timeWindow
        ]));
    }
}

// Brute Force Koruması
function checkBruteForce($identifier, $maxAttempts = 5, $lockoutTime = 900) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    $key = 'brute_force_' . md5($identifier);
    $now = time();

    if (!isset($_SESSION[$key])) {
        return true;
    }

    $data = $_SESSION[$key];

    // Lockout süresi doldu mu?
    if ($now - $data['last_attempt'] > $lockoutTime) {
        unset($_SESSION[$key]);
        return true;
    }

    // Max deneme sayısı aşıldı mı?
    return $data['attempts'] < $maxAttempts;
}

function recordFailedAttempt($identifier) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    $key = 'brute_force_' . md5($identifier);
    $now = time();

    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['attempts' => 1, 'last_attempt' => $now];
    } else {
        $_SESSION[$key]['attempts']++;
        $_SESSION[$key]['last_attempt'] = $now;
    }

    // Güvenlik loguna kaydet
    logSecurityEvent('FAILED_LOGIN', $identifier, $_SERVER['REQUEST_URI'] ?? '');
}

// Güvenlik Log Sistemi
function logSecurityEvent($eventType, $identifier, $details = '') {
    $logDir = __DIR__ . '/../logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    $logFile = $logDir . '/security.log';
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    $logEntry = sprintf(
        "[%s] %s | IP: %s | Identifier: %s | Details: %s | User-Agent: %s\n",
        $timestamp,
        $eventType,
        $ip,
        $identifier,
        $details,
        $userAgent
    );

    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Şüpheli aktivite tespiti
function detectSuspiciousActivity() {
    $suspiciousPatterns = [
        'script' => 'XSS_ATTEMPT',
        'union' => 'SQL_INJECTION_ATTEMPT',
        '../' => 'DIRECTORY_TRAVERSAL_ATTEMPT',
        'eval(' => 'CODE_INJECTION_ATTEMPT',
        'base64_decode' => 'PAYLOAD_ATTEMPT'
    ];

    $requestData = array_merge($_GET, $_POST);
    $requestString = json_encode($requestData);

    foreach ($suspiciousPatterns as $pattern => $eventType) {
        if (stripos($requestString, $pattern) !== false) {
            logSecurityEvent($eventType, $_SERVER['REMOTE_ADDR'] ?? 'unknown', $requestString);
            return true;
        }
    }

    return false;
}
?>
