<?php
// Start output buffering to catch any unwanted output
ob_start();

// Production mode - hide errors
error_reporting(0);
ini_set('display_errors', 0);

try {
    require_once __DIR__ . '/../../config/database.php';

    // Clean any previous output
    if (ob_get_level()) {
        ob_clean();
    }

    // UTF-8 encoding için
    mb_internal_encoding('UTF-8');
    mb_http_output('UTF-8');

    // Cache kontrol header'ları - API her zaman güncel olsun
    header("Cache-Control: no-cache, no-store, must-revalidate, max-age=0");
    header("Pragma: no-cache");
    header("Expires: Thu, 01 Jan 1970 00:00:00 GMT");

    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');

    $database = new Database();
    $db = $database->getConnection();
    
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? min(20, max(1, intval($_GET['limit']))) : 6;
    $category = isset($_GET['category']) && $_GET['category'] !== 'all' ? trim($_GET['category']) : null;
    $search = isset($_GET['search']) ? trim($_GET['search']) : null;
    $status = isset($_GET['status']) ? trim($_GET['status']) : 'published';
    
    $offset = ($page - 1) * $limit;
    
    // Base query
    $sql = "SELECT
                bp.id,
                bp.title,
                bp.slug,
                bp.excerpt,
                bp.content,
                bp.featured_image,
                bp.author,
                bp.meta_title,
                bp.meta_description,
                bp.meta_keywords,
                bp.reading_time,
                bp.view_count,
                bp.featured,
                bp.published_at,
                bp.created_at,
                bc.name as category_name,
                bc.slug as category_slug
            FROM blog_posts bp
            LEFT JOIN blog_categories bc ON bp.category_id = bc.id
            WHERE bp.status = :status";
    
    $params = [':status' => $status];
    
    // Add category filter
    if ($category) {
        $sql .= " AND bc.slug = :category";
        $params[':category'] = $category;
    }
    
    // Add search filter
    if ($search) {
        $sql .= " AND (bp.title LIKE :search OR bp.content LIKE :search OR bp.meta_keywords LIKE :search)";
        $params[':search'] = "%$search%";
    }
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM ($sql) as counted_posts";
    $countStmt = $db->prepare($countSql);
    $countStmt->execute($params);
    $totalPosts = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Add ordering and pagination
    $sql .= " ORDER BY bp.featured DESC, bp.published_at DESC LIMIT :limit OFFSET :offset";
    $params[':limit'] = $limit;
    $params[':offset'] = $offset;
    
    $stmt = $db->prepare($sql);
    
    // Bind parameters
    foreach ($params as $key => $value) {
        if ($key === ':limit' || $key === ':offset') {
            $stmt->bindValue($key, $value, PDO::PARAM_INT);
        } else {
            $stmt->bindValue($key, $value, PDO::PARAM_STR);
        }
    }
    
    $stmt->execute();
    $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process posts
    foreach ($posts as &$post) {
        // Truncate content for excerpt if needed
        if (empty($post['excerpt']) && !empty($post['content'])) {
            $post['excerpt'] = substr(strip_tags($post['content']), 0, 150) . '...';
        }
        
        // Format dates
        $post['published_at_formatted'] = date('d F Y', strtotime($post['published_at']));
        $post['created_at_formatted'] = date('d F Y', strtotime($post['created_at']));
        
        // Generate full URL for featured image
        if ($post['featured_image'] && strpos($post['featured_image'], 'http') !== 0) {
            $post['featured_image'] = '/' . ltrim($post['featured_image'], '/');
        }
        
        // Clean HTML from content for excerpt
        $post['plain_content'] = strip_tags($post['content']);

        // Fetch tags for this post
        $tagSql = "SELECT bt.name, bt.slug
                   FROM blog_tags bt
                   INNER JOIN blog_post_tags bpt ON bt.id = bpt.tag_id
                   WHERE bpt.post_id = ?";
        $tagStmt = $db->prepare($tagSql);
        $tagStmt->execute([$post['id']]);
        $post['tags'] = $tagStmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Calculate pagination
    $totalPages = ceil($totalPosts / $limit);
    $hasNext = $page < $totalPages;
    $hasPrev = $page > 1;
    
    $pagination = [
        'currentPage' => $page,
        'totalPages' => $totalPages,
        'totalPosts' => $totalPosts,
        'hasNext' => $hasNext,
        'hasPrev' => $hasPrev,
        'nextPage' => $hasNext ? $page + 1 : null,
        'prevPage' => $hasPrev ? $page - 1 : null
    ];
    
    echo json_encode([
        'success' => true,
        'posts' => $posts,
        'pagination' => $pagination,
        'filters' => [
            'category' => $category,
            'search' => $search,
            'page' => $page,
            'limit' => $limit
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_NUMERIC_CHECK);
    
} catch (Exception $e) {
    // Clean any output buffer
    if (ob_get_level()) {
        ob_clean();
    }

    error_log("Blog posts API error: " . $e->getMessage());
    http_response_code(500);

    // Ensure clean JSON output
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Sunucu hatası oluştu.',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
