document.addEventListener("DOMContentLoaded", () => {
  loadFavorites()
  updateFavoritesCount()
})

function loadFavorites() {
  const favorites = getFavorites()
  const favoritesItemsList = document.getElementById("favorites-items-list")
  const emptyMessage = document.querySelector(".empty-favorites-message")
  const favoritesCountElement = document.getElementById("favorites-total-count")
  const favoritesCountSummary = document.getElementById("favorites-total-count-summary")
  const favoritesSummary = document.querySelector(".favorites-summary")

  if (favoritesCountElement) {
    favoritesCountElement.textContent = favorites.length
  }
  if (favoritesCountSummary) {
    favoritesCountSummary.textContent = favorites.length
  }

  if (favorites.length === 0) {
    if (emptyMessage) emptyMessage.style.display = "block"
    if (favoritesItemsList) favoritesItemsList.innerHTML = ""
    if (favoritesSummary) favoritesSummary.style.display = "none"
    return
  }

  if (emptyMessage) emptyMessage.style.display = "none"
  if (favoritesSummary) favoritesSummary.style.display = "block"

  if (favoritesItemsList) {
    favoritesItemsList.innerHTML = ""

    const row = document.createElement("div")
    // Dynamic grid based on number of items
    let gridClass = "row g-4";
    if (favorites.length === 1) {
      gridClass += " justify-content-center"; // Center single item
    } else if (favorites.length === 2) {
      gridClass += " row-cols-1 row-cols-md-2"; // 2 items per row max
    } else {
      gridClass += " row-cols-1 row-cols-md-2 row-cols-lg-3"; // Standard grid
    }
    row.className = gridClass;

    favorites.forEach((product) => {
      const productCol = document.createElement("div")
      // Dynamic column class based on number of items
      let colClass = "col";
      if (favorites.length === 1) {
        colClass += " col-md-8 col-lg-6 col-xl-4"; // Single item: wider on all screens
      }
      productCol.className = colClass;

      const imageUrl = product.image || "/images/default-product.png"
      const stockInfo =
        product.stock > 0
          ? `<p class="card-text text-muted mb-2"><small>Stok: ${product.stock}</small></p>`
          : '<p class="card-text text-danger fw-bold mb-2"><small>Stok Tükendi</small></p>'

      productCol.innerHTML = `
        <div class="card h-100 shadow-sm favorite-card-modern">
          <div class="position-relative">
            <img src="${imageUrl}" class="card-img-top p-3" alt="${product.name}" style="height: 200px; object-fit: contain; background-color: #fff;">
            <button class="btn btn-danger btn-sm position-absolute favorite-btn" 
                    style="top: 10px; right: 10px; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;"
                    title="Favorilerden Kaldır"
                    onclick="removeFromFavorites('${product._id}')">
              <i class="fas fa-heart"></i>
            </button>
          </div>
          <div class="card-body d-flex flex-column text-center">
            <h5 class="card-title fs-6 mb-2">${product.name}</h5>
            <p class="card-text small text-muted mb-2">${(product.description || "").substring(0, 80)}${(product.description || "").length > 80 ? "..." : ""}</p>
            ${stockInfo}
            <div class="mt-auto">
              <p class="card-text fs-5 fw-bold text-primary mb-3">${(product.price / 100).toFixed(2)} TL</p>
              <div class="d-grid gap-2">
                <a href="product-detail.php?id=${product._id}" class="btn btn-outline-primary rounded-pill">
                  Detayları Gör <i class="fas fa-arrow-right ms-1"></i>
                </a>
                <a href="${product.purchaseLink || "https://www.shopier.com/tansusahalsalamura"}"
                   target="_blank"
                   class="btn rounded-pill"
                   style="background: linear-gradient(45deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
                  <i class="fas fa-shopping-cart me-1"></i>Satın Al
                </a>
              </div>
            </div>
          </div>
        </div>
      `

      row.appendChild(productCol)
    })

    favoritesItemsList.appendChild(row)
  }

  // Clear all favorites button
  const clearButton = document.querySelector(".clear-favorites-button")
  if (clearButton) {
    clearButton.onclick = () => {
      if (confirm("Tüm favorileri temizlemek istediğinizden emin misiniz?")) {
        clearAllFavorites()
      }
    }
  }
}

function getFavorites() {
  const favoritesString = localStorage.getItem("userFavorites")
  try {
    const favorites = favoritesString ? JSON.parse(favoritesString) : []
    return Array.isArray(favorites) ? favorites : []
  } catch (e) {
    return []
  }
}

function saveFavorites(favorites) {
  localStorage.setItem("userFavorites", JSON.stringify(favorites))
  updateFavoritesCount()
}

// Global function for removing from favorites
window.removeFromFavorites = function(productId) {
  console.log('Removing product from favorites:', productId);
  let favorites = getFavorites()
  console.log('Current favorites:', favorites);
  console.log('Favorites before filter:', favorites.length);

  // Check if productId exists in favorites
  const existingProduct = favorites.find(item => item._id === productId);
  console.log('Found existing product:', existingProduct);

  favorites = favorites.filter((item) => {
    console.log('Comparing:', String(item._id), 'vs', String(productId), 'Equal:', String(item._id) === String(productId));
    // Convert both to strings for comparison
    return String(item._id) !== String(productId);
  });

  saveFavorites(favorites)
  loadFavorites()

  // Show success message
  showMessage('Ürün favorilerden kaldırıldı!', 'success')
}

function clearAllFavorites() {
  localStorage.removeItem("userFavorites")
  updateFavoritesCount()
  loadFavorites()
}

// Show message function
function showMessage(message, type = 'info') {
  // Remove existing messages
  const existingMessages = document.querySelectorAll('.alert-message');
  existingMessages.forEach(msg => msg.remove());

  // Create new message
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show alert-message`;
  alertDiv.style.position = 'fixed';
  alertDiv.style.top = '100px';
  alertDiv.style.right = '20px';
  alertDiv.style.zIndex = '9999';
  alertDiv.style.minWidth = '300px';

  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

  document.body.appendChild(alertDiv);

  // Auto remove after 3 seconds
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.remove();
    }
  }, 3000);
}

function updateFavoritesCount() {
  const favorites = getFavorites()
  const totalItems = favorites.length
  const favoritesCountElements = document.querySelectorAll("#favorites-item-count, #favorites-total-count")
  favoritesCountElements.forEach((element) => {
    if (element) element.textContent = totalItems
  })
}
