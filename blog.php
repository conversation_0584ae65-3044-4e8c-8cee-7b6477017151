<?php
// <PERSON>ache kontrol header'ları - Blog say<PERSON><PERSON>ı her zaman güncel olsun
header("Cache-Control: no-cache, no-store, must-revalidate, max-age=0");
header("Pragma: no-cache");
header("Expires: Thu, 01 Jan 1970 00:00:00 GMT");

// Cache buster fonksiyonları
require_once 'includes/cache-buster.php';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="/">

    <!-- SEO Meta Tags -->
    <title>Blog - Salamura ve Turşu Üretimi Rehberi | Tansu Şahal Salamura</title>
    <meta name="description" content="Geleneksel salamura ve turşu üretimi hakkında uzman içerikleri. Ev yapımı tarifleri, fermentasyon teknikleri ve sağlıklı beslenme önerileri.">
    <meta name="keywords" content="salamura blog, turşu tarifleri, fermentasyon, probi<PERSON>tik, doğal beslenme, Alaşehir salamura, geleneksel yöntemler">
    <meta name="author" content="Tansu Şahal Salamura">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://tansusahalsalamura.com.tr/blog.php">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Blog - Salamura ve Turşu Üretimi Rehberi">
    <meta property="og:description" content="Geleneksel salamura ve turşu üretimi hakkında uzman içerikleri ve tarifleri">
    <meta property="og:image" content="https://tansusahalsalamura.com.tr/images/logo.jpg">
    <meta property="og:url" content="https://tansusahalsalamura.com.tr/blog.php">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="tr_TR">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Blog - Salamura ve Turşu Üretimi Rehberi">
    <meta name="twitter:description" content="Geleneksel salamura ve turşu üretimi hakkında uzman içerikleri">
    <meta name="twitter:image" content="https://tansusahalsalamura.com.tr/images/logo.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo getCSSLink(); ?>">

    <!-- Breadcrumb Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Ana Sayfa",
                "item": "https://tansusahalsalamura.com.tr/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Blog",
                "item": "https://tansusahalsalamura.com.tr/blog.php"
            }
        ]
    }
    </script>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Blog",
        "name": "Tansu Şahal Salamura Blog",
        "description": "Geleneksel salamura ve turşu üretimi hakkında uzman içerikleri",
        "url": "https://www.tansusahalsalamura.com/blog",
        "author": {
            "@type": "Organization",
            "name": "Tansu Şahal Salamura",
            "url": "https://www.tansusahalsalamura.com"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Tansu Şahal Salamura",
            "logo": {
                "@type": "ImageObject",
                "url": "https://www.tansusahalsalamura.com/images/logo.jpg"
            }
        }
    }
    </script>
    
    <style>
        /* Blog Specific Styles */
        .blog-header {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 100%);
            color: white;
            padding: 4rem 0 2rem 0;
            margin-top: 80px;
        }
        
        .blog-header h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .blog-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .category-filter {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .category-btn {
            background: transparent;
            border: 2px solid var(--light-green);
            color: var(--primary-green);
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            margin: 0.25rem;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .category-btn:hover,
        .category-btn.active {
            background: var(--primary-green);
            color: white;
            border-color: var(--primary-green);
            transform: translateY(-2px);
        }
        
        .blog-post-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 2px solid var(--light-green);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .blog-post-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(34,139,34,0.15);
        }
        
        .post-image {
            height: 200px;
            object-fit: cover;
            width: 100%;
        }
        
        .post-content {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        .post-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--dark-green);
            margin-bottom: 0.75rem;
            line-height: 1.4;
        }
        
        .post-excerpt {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1rem;
            flex-grow: 1;
        }
        
        .post-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #eee;
            font-size: 0.9rem;
            color: #888;
        }
        
        .post-category {
            background: var(--light-green);
            color: var(--dark-green);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-decoration: none;
        }
        
        .featured-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #ff6b35;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .search-box {
            position: relative;
            max-width: 400px;
            margin: 0 auto 2rem auto;
        }
        
        .search-input {
            width: 100%;
            padding: 1rem 3rem 1rem 1.5rem;
            border: 2px solid var(--light-green);
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(34,139,34,0.1);
        }
        
        .search-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--primary-green);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .pagination-custom {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 3rem 0;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .pagination-custom .page-link {
            border: 2px solid var(--light-green);
            color: var(--primary-green);
            padding: 0.75rem 1rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            min-width: 45px;
            text-align: center;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
        }

        /* Önceki/Sonraki butonları için özel stil */
        .pagination-custom .page-link.nav-button {
            min-width: 90px;
            padding: 0.75rem 1rem;
            height: 45px;
            font-size: 0.9rem;
        }

        /* Sayfa numaraları için özel stil */
        .pagination-custom .page-link.page-number {
            min-width: 45px;
            max-width: 45px;
            padding: 0.75rem 0.5rem;
            height: 45px;
            font-size: 0.95rem;
        }

        /* Tüm pagination butonları için ortak yükseklik */
        .pagination-custom .page-item {
            display: flex;
            align-items: center;
        }

        .pagination-custom .page-item .page-link,
        .pagination-custom .page-item span.page-link {
            height: 45px;
            line-height: 1;
        }

        .pagination-custom .page-link:hover {
            background: var(--primary-green);
            color: white;
            border-color: var(--primary-green);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(34,139,34,0.3);
        }

        .pagination-custom .page-item.active .page-link {
            background: var(--primary-green);
            color: white;
            border-color: var(--primary-green);
            box-shadow: 0 4px 12px rgba(34,139,34,0.3);
        }

        .pagination-custom .page-item.disabled .page-link {
            color: #ccc;
            border-color: #eee;
            background: #f8f9fa;
            cursor: not-allowed;
        }

        .pagination-custom .page-item.disabled .page-link:hover {
            transform: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .blog-sidebar {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid var(--light-green);
        }
        
        .sidebar-title {
            color: var(--primary-green);
            font-weight: 700;
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
        }
        
        .popular-post {
            display: flex;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .popular-post:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .popular-post-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 1rem;
        }
        
        .popular-post-content h6 {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--dark-green);
            margin-bottom: 0.25rem;
            line-height: 1.3;
        }
        
        .popular-post-date {
            font-size: 0.8rem;
            color: #888;
        }
        
        .tag-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 0.4rem;
            justify-content: flex-start;
            align-items: flex-start;
            line-height: 1.2;
        }

        .tag-item {
            background: var(--light-green);
            color: var(--primary-green);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            border: 1px solid var(--light-green);
            white-space: nowrap;
            margin-bottom: 0.2rem;
        }

        .tag-item:hover {
            background: var(--primary-green);
            color: white;
            border-color: var(--primary-green);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(34,139,34,0.25);
            text-decoration: none;
        }

        /* Responsive tag cloud */
        @media (max-width: 768px) {
            .tag-cloud {
                gap: 0.3rem;
            }

            .tag-item {
                font-size: 0.7rem;
                padding: 0.25rem 0.6rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm sticky-top main-navbar" style="z-index: 1030;">
        <div class="container">
            <a class="navbar-brand fw-bold fs-4 d-flex align-items-center" href="/">
                <img src="images/logo.jpg" alt="Tansu Şahal Salamura Logo" style="height: 50px; margin-right: 10px;">
                <span class="brand-text">
                    <span class="brand-line-1">Tansu Şahal</span>
                    <span class="brand-line-2">Salamura</span>
                </span>
            </a>
            
            <button class="navbar-toggler" type="button" onclick="toggleIndependentMobileMenu()" aria-label="Menüyü aç/kapat">
                <span class="navbar-toggler-icon"></span>
                <span class="menu-text">Menü</span>
            </button>
            
            <div class="collapse navbar-collapse" id="mainNav">
                <!-- Desktop Menu -->
                <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
                    <li class="nav-item"><a class="nav-link" href="/">Ana Sayfa</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#products">Tüm Ürünler</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#about-us-section">Hakkımızda</a></li>
                    <li class="nav-item"><a class="nav-link" href="blog.php">Blog</a></li>
                    <li class="nav-item"><a class="nav-link" href="/#contact-section">İletişim</a></li>
                </ul>
                <div class="d-flex align-items-center navbar-user-info">
                    <span id="userGreeting" class="navbar-user-greeting me-2" style="display:none;"></span>
                    <div class="auth-links-group">
                        <a href="login.php" id="loginLink" class="text-dark text-decoration-none auth-link">Giriş Yap</a>
                        <a href="register.php" id="registerLink" class="text-dark text-decoration-none auth-link">Kayıt Ol</a>
                        <a href="#" id="logoutLink" class="text-dark text-decoration-none auth-link" style="display:none;">Çıkış Yap</a>
                        <a href="admin.php" id="adminLink" class="text-dark text-decoration-none auth-link" style="display:none;">Admin</a>
                        <a href="favorites.php" class="btn btn-outline-danger position-relative rounded-pill px-2 py-1" aria-label="Favoriler">
                            <i class="fas fa-heart me-1"></i>
                            <span class="d-none d-md-inline">Favoriler</span>
                            <span id="favorites-item-count" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger border border-light">0</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Blog Header -->
    <section class="blog-header text-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <nav aria-label="breadcrumb" class="mb-4">
                        <ol class="breadcrumb justify-content-center mb-0" style="background: rgba(255,255,255,0.1); border-radius: 10px; padding: 0.75rem;">
                            <li class="breadcrumb-item"><a href="/" class="text-white">Ana Sayfa</a></li>
                            <li class="breadcrumb-item text-white active" aria-current="page">Blog</li>
                        </ol>
                    </nav>
                    
                    <h1>🌿 Salamura & Turşu Blog</h1>
                    <p class="blog-subtitle">
                        Geleneksel fermentasyon teknikleri, ev yapımı tarifleri ve sağlıklı beslenme rehberiniz. 
                        Alaşehir'in asırlık lezzetlerini keşfedin.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container my-5">
        <div class="row">
            <!-- Main Blog Content -->
            <div class="col-lg-8">
                <!-- Search & Filter Section -->
                <div class="category-filter">
                    <div class="search-box mb-4">
                        <input type="text" class="search-input" placeholder="Blog yazılarında ara..." id="searchInput">
                        <button class="search-btn" onclick="searchPosts()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <!-- Category filter buttons will be loaded here dynamically -->
                        <div class="text-center py-3">
                            <i class="fas fa-spinner fa-spin text-muted"></i>
                            <p class="mt-2 text-muted small">Kategoriler yükleniyor...</p>
                        </div>
                    </div>
                </div>

                <!-- Blog Posts Grid -->
                <div class="row g-4" id="blogPostsContainer">
                    <!-- Blog posts will be loaded here dynamically -->
                </div>

                <!-- Pagination Info -->
                <div class="text-center mb-3" id="paginationInfo" style="display: none;">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        <span id="paginationText"></span>
                    </small>
                </div>

                <!-- Pagination -->
                <nav aria-label="Blog pagination" class="pagination-custom">
                    <ul class="pagination" id="paginationContainer">
                        <!-- Pagination will be loaded here -->
                    </ul>
                </nav>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Popular Posts -->
                <div class="blog-sidebar">
                    <h5 class="sidebar-title">🔥 Popüler Yazılar</h5>
                    <div id="popularPostsContainer">
                        <!-- Popular posts will be loaded here -->
                    </div>
                </div>

                <!-- Categories Widget -->
                <div class="blog-sidebar">
                    <h5 class="sidebar-title">📂 Kategoriler</h5>
                    <div id="categoriesWidget">
                        <!-- Categories will be loaded here -->
                    </div>
                </div>

                <!-- Tags Cloud -->
                <div class="blog-sidebar">
                    <h5 class="sidebar-title">🏷️ Etiketler</h5>
                    <div class="tag-cloud" id="tagsContainer">
                        <!-- Tags will be loaded here -->
                    </div>
                </div>

                <!-- Newsletter Signup -->
                <div class="blog-sidebar">
                    <h5 class="sidebar-title">📬 Haber Bülteni</h5>
                    <p class="mb-3">Yeni blog yazılarımızdan haberdar olmak için e-posta adresinizi bırakın.</p>
                    <form id="newsletterForm">
                        <div class="mb-3">
                            <input type="email" class="form-control" placeholder="E-posta adresiniz" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-envelope me-2"></i>Abone Ol
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Menu Backdrop -->
    <div id="mobileMenuBackdrop" class="mobile-menu-backdrop" onclick="closeIndependentMobileMenu()"></div>

    <!-- Independent Mobile Menu -->
    <div id="independentMobileMenu" class="independent-mobile-menu">
        <div class="mobile-menu-container">
            <button class="mobile-menu-close" type="button" onclick="closeIndependentMobileMenu()" aria-label="Menüyü kapat">
                <span class="sr-only">Kapat</span>
            </button>

            <div class="mobile-menu-header">
                <div class="brand-text">
                    <span class="brand-line-1">Tansu Şahal</span>
                    <span class="brand-line-2">Salamura</span>
                </div>
            </div>

            <ul class="mobile-nav-links">
                <li class="nav-item"><a class="nav-link" href="/" onclick="closeIndependentMobileMenu()">🏠 Ana Sayfa</a></li>
                <li class="nav-item"><a class="nav-link" href="/#products" onclick="closeIndependentMobileMenu()">🛍️ Tüm Ürünler</a></li>
                <li class="nav-item"><a class="nav-link" href="/#about-us-section" onclick="closeIndependentMobileMenu()">🌿 Hakkımızda</a></li>
                <li class="nav-item"><a class="nav-link active" href="blog.php" onclick="closeIndependentMobileMenu()">📝 Blog</a></li>
                <li class="nav-item"><a class="nav-link" href="/#contact-section" onclick="closeIndependentMobileMenu()">📞 İletişim</a></li>
            </ul>

            <div class="mobile-user-section">
                <div id="mobileUserGreeting2" class="mobile-user-greeting" style="display:none;"></div>

                <div class="mobile-auth-buttons">
                    <a href="login.php" id="mobileLoginLink2" class="mobile-auth-button secondary" onclick="closeIndependentMobileMenu()">
                        <i class="fas fa-sign-in-alt"></i> Giriş Yap
                    </a>
                    <a href="register.php" id="mobileRegisterLink2" class="mobile-auth-button primary" onclick="closeIndependentMobileMenu()">
                        <i class="fas fa-user-plus"></i> Kayıt Ol
                    </a>
                    <a href="#" id="mobileLogoutLink2" class="mobile-auth-button secondary" style="display:none;" onclick="handleMobileLogout()">
                        <i class="fas fa-sign-out-alt"></i> Çıkış Yap
                    </a>
                    <a href="admin.php" id="mobileAdminLink2" class="mobile-auth-button primary" style="display:none;" onclick="closeIndependentMobileMenu()">
                        <i class="fas fa-cog"></i> Admin
                    </a>
                </div>

                <a href="favorites.php" class="mobile-favorites-button" aria-label="Favoriler" onclick="closeIndependentMobileMenu()">
                    <i class="fas fa-heart"></i> Favoriler
                    <span id="mobileFavoritesCount2" class="badge bg-white text-danger">0</span>
                </a>
            </div>

            <div class="mobile-menu-footer">
                <p>🌱 Doğallığın ve Lezzetin Adresi</p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center p-4 mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>🌿 Tansu Şahal Salamura</h5>
                    <p>Doğallığın ve lezzetin adresi</p>
                </div>
                <div class="col-md-6">
                    <h6>İletişim</h6>
                    <p>
                        📞 +90 (536) 035 92 20<br>
                        📞 +90 536 705 58 69<br>
                        📧 <EMAIL>
                    </p>
                </div>
            </div>
            <hr>
            <p>&copy; 2025 Tansu Şahal Salamura. Tüm hakları saklıdır.</p>
            <p class="small text-white-50 mt-2">
                <i class="fas fa-code me-1"></i>
                Web Tasarımı: <a href="mailto:<EMAIL>" class="text-white-50"><EMAIL></a>
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo getJSLink('header-updater.js'); ?>"></script>
    <script src="<?php echo getJSLink('blog-scripts.js'); ?>"></script>
    <script src="<?php echo getJSLink('blog-router.js'); ?>"></script> <!-- Enhanced router with fallback support -->
    <script src="<?php echo getJSLink('mobile-menu.js'); ?>"></script> <!-- Universal mobile menu system -->

    <script>
    function toggleMobileDropdown(event) {
        event.preventDefault();
        const dropdown = event.target.closest('.mobile-dropdown');
        const dropdownMenu = dropdown.querySelector('.mobile-dropdown-menu');
        const chevron = dropdown.querySelector('.fa-chevron-down, .fa-chevron-up');

        // Toggle dropdown
        dropdownMenu.classList.toggle('show');

        // Toggle chevron
        if (chevron) {
            chevron.classList.toggle('fa-chevron-down');
            chevron.classList.toggle('fa-chevron-up');
        }
    }

    // Aktif sayfa için nav-link'lere active class ekle
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname;
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

        // Önce tüm active class'ları temizle
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        navLinks.forEach(link => {
            const href = link.getAttribute('href');

            // Blog sayfası kontrolü - blog sayfasındayken sadece blog aktif olsun
            if (currentPage.includes('blog.php') && href.includes('blog.php')) {
                link.classList.add('active');
            }
            // Ana sayfa kontrolü - sadece ana sayfadayken
            else if ((currentPage === '/' || currentPage === '/index.php') && (href === '/' || href === 'index.php')) {
                link.classList.add('active');
            }
        });

        // ESC key to close mobile menu
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeIndependentMobileMenu();
            }
        });
    });
    </script>
</body>
</html>
