<?php
// Blog Categories API
error_reporting(0);
ini_set('display_errors', 0);

// Start output buffering and clean any previous output
ob_start();
if (ob_get_level() > 1) {
    ob_clean();
}

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            handleGetRequest($db);
            break;
        case 'POST':
            handlePostRequest($db);
            break;
        default:
            throw new Exception('Method not allowed');
    }

} catch (Exception $e) {
    error_log("Blog categories API error: " . $e->getMessage());
    sendJsonResponse([
        'success' => false,
        'message' => 'Sunucu hatası oluştu: ' . $e->getMessage()
    ], 500);
}

function handleGetRequest($db) {
    // Get all categories
    $sql = "SELECT
                bc.id,
                bc.name,
                bc.slug,
                bc.description,
                bc.meta_description,
                COUNT(bp.id) as post_count
            FROM blog_categories bc
            LEFT JOIN blog_posts bp ON bc.id = bp.category_id AND bp.status = 'published'
            GROUP BY bc.id, bc.name, bc.slug, bc.description, bc.meta_description
            ORDER BY bc.name ASC";

    $stmt = $db->prepare($sql);
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format categories
    foreach ($categories as &$category) {
        $category['id'] = (int)$category['id'];
        $category['post_count'] = (int)$category['post_count'];
    }

    // Ensure proper UTF-8 encoding
    foreach ($categories as &$category) {
        $category['name'] = mb_convert_encoding($category['name'], 'UTF-8', 'UTF-8');
        $category['description'] = mb_convert_encoding($category['description'], 'UTF-8', 'UTF-8');
    }

    sendJsonResponse([
        'success' => true,
        'categories' => $categories
    ]);
}
    
function handlePostRequest($db) {
    // Check admin authentication
    $user = getCurrentUser($db);
    if (!$user || $user['role'] !== 'admin') {
        sendJsonResponse([
            'success' => false,
            'message' => 'Yetkiniz bulunmamaktadır.'
        ], 403);
    }

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Geçersiz veri formatı.');
    }

    $name = trim($input['name'] ?? '');
    $slug = trim($input['slug'] ?? '');
    $description = trim($input['description'] ?? '');
    $metaDescription = trim($input['meta_description'] ?? '');

    if (empty($name)) {
        throw new Exception('Kategori adı gerekli.');
    }

    if (empty($slug)) {
        $slug = generateSlug($name);
    }

    // Check if slug exists
    $slugCheck = $db->prepare("SELECT id FROM blog_categories WHERE slug = :slug");
    $slugCheck->bindValue(':slug', $slug);
    $slugCheck->execute();
    if ($slugCheck->fetch()) {
        $slug .= '-' . time();
    }

    // Insert category
    $sql = "INSERT INTO blog_categories (name, slug, description, meta_description, created_at) VALUES (:name, :slug, :description, :meta_description, NOW())";
    $stmt = $db->prepare($sql);
    $stmt->bindValue(':name', $name);
    $stmt->bindValue(':slug', $slug);
    $stmt->bindValue(':description', $description);
    $stmt->bindValue(':meta_description', $metaDescription);

    if ($stmt->execute()) {
        $categoryId = $db->lastInsertId();

        // Get the created category
        $getCategory = $db->prepare("SELECT id, name, slug, description, meta_description FROM blog_categories WHERE id = :id");
        $getCategory->bindValue(':id', $categoryId, PDO::PARAM_INT);
        $getCategory->execute();
        $category = $getCategory->fetch(PDO::FETCH_ASSOC);

        $category['id'] = (int)$category['id'];

        sendJsonResponse([
            'success' => true,
            'message' => 'Kategori başarıyla eklendi.',
            'category' => $category
        ]);
    } else {
        throw new Exception('Kategori eklenirken hata oluştu.');
    }
}

function generateSlug($text) {
    // Turkish character replacements - more comprehensive
    $turkish = [
        'ş', 'Ş', 'ı', 'I', 'İ', 'ğ', 'Ğ', 'ü', 'Ü', 'ö', 'Ö', 'ç', 'Ç',
        'â', 'Â', 'î', 'Î', 'û', 'Û', 'ê', 'Ê', 'ô', 'Ô'
    ];
    $english = [
        's', 's', 'i', 'i', 'i', 'g', 'g', 'u', 'u', 'o', 'o', 'c', 'c',
        'a', 'a', 'i', 'i', 'u', 'u', 'e', 'e', 'o', 'o'
    ];

    // First normalize the text
    $slug = trim($text);

    // Replace Turkish characters
    $slug = str_replace($turkish, $english, $slug);

    // Convert to lowercase
    $slug = mb_strtolower($slug, 'UTF-8');

    // Replace spaces and special characters with hyphens
    $slug = preg_replace('/[^a-z0-9\-]/', '-', $slug);

    // Remove multiple consecutive hyphens
    $slug = preg_replace('/-+/', '-', $slug);

    // Remove hyphens from start and end
    $slug = trim($slug, '-');

    // Ensure slug is not empty
    if (empty($slug)) {
        $slug = 'kategori-' . time();
    }

    return $slug;
}
?>
