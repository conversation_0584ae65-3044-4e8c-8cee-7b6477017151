RewriteEngine On

# API routing
RewriteRule ^api/products/?$ api/products/index.php [L]
RewriteRule ^api/products/([0-9]+)/?$ api/products/detail.php?id=$1 [L]
RewriteRule ^api/products/detail/?$ api/products/detail.php [L]
RewriteRule ^api/admin/products/?$ api/admin/products.php [L]
RewriteRule ^api/admin/products/([0-9]+)$ api/admin/products.php [L]
RewriteRule ^api/users/register/?$ api/users/register.php [L]
RewriteRule ^api/users/login/?$ api/users/login.php [L]
RewriteRule ^api/admin/upload-video/?$ api/admin/upload-video.php [L]
RewriteRule ^api/admin/upload-image/?$ api/admin/upload-image.php [L]
RewriteRule ^api/contact/?$ api/contact.php [L]

# Blog API routing
RewriteRule ^api/blog/posts/?$ api/blog/posts.php [L]
RewriteRule ^api/blog/popular/?$ api/blog/popular.php [L]
RewriteRule ^api/blog/categories/?$ api/blog/categories.php [L]
RewriteRule ^api/blog/tags/?$ api/blog/tags.php [L]
RewriteRule ^api/blog/manage/?$ api/blog/manage.php [L]
RewriteRule ^api/blog/test/?$ api/blog/test.php [L]

# SEO and Search Engine Bot Access
# Allow robots.txt and sitemap files - Always accessible
RewriteRule ^robots\.txt$ robots.txt [L]
RewriteRule ^sitemap\.xml$ sitemap.xml [L]
RewriteRule ^sitemap-generator\.php$ sitemap-generator.php [L]

# Search Engine Bot Special Access Rules
# Allow search engine bots to access all public content
<IfModule mod_rewrite.c>
    # Google, Bing, Yandex botları için özel kurallar
    RewriteCond %{HTTP_USER_AGENT} (Googlebot|Bingbot|YandexBot|facebookexternalhit|Twitterbot|LinkedInBot|WhatsApp|Slackbot) [NC]
    RewriteRule ^(.*)$ - [E=SEARCH_BOT:1]
</IfModule>

# Blog post routing - main rule for pretty URLs
RewriteRule ^blog/([a-zA-Z0-9\-]+)/?$ blog-detail.php?slug=$1 [L,QSA]

# Allow direct access to API files
RewriteCond %{REQUEST_URI} ^/api/
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^ - [L]

# Static files
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Directory varsa direkt serve et
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# Default route to index.php
RewriteRule ^$ index.php [L]

# Error pages
ErrorDocument 404 /404.php
ErrorDocument 500 /500.php

# PHP error handling
php_flag display_errors Off
php_flag log_errors On
php_value error_log logs/php_error.log

# Security headers - XSS ve diğer saldırılara karşı koruma
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; connect-src 'self'; frame-ancestors 'none';"
</IfModule>

# Güvenlik - Hassas Dosyaları Engelle (Arama motoru botları hariç)
<FilesMatch "\.(sql|log|ini|conf|bak|old|tmp|env)$">
    Order allow,deny
    Deny from all
    # Arama motoru botları için istisna yok - güvenlik öncelikli
</FilesMatch>

# SEO dosyalarına özel erişim
<FilesMatch "^(robots\.txt|sitemap.*\.xml|sitemap-generator\.php)$">
    Order allow,deny
    Allow from all
    # Tüm arama motoru botlarına izin ver
    SetEnvIf User-Agent "Googlebot|Bingbot|YandexBot|facebookexternalhit|Twitterbot|LinkedInBot|WhatsApp|Slackbot" search_bot
    Allow from env=search_bot
</FilesMatch>

# Config ve Include Klasörlerini Engelle
<Files ~ "^(config|includes|scripts|database)/">
    Order allow,deny
    Deny from all
</Files>

# Admin sayfası için ek güvenlik - Direct access engelleme
<Files "admin.php">
    # Bu kural PHP seviyesinde kontrol edilecek
    # .htaccess seviyesinde session kontrolü yapılamaz
</Files>

# Tehlikeli HTTP metodlarını engelle - API için PUT ve DELETE'e izin ver
<LimitExcept GET POST HEAD PUT DELETE OPTIONS>
    Order allow,deny
    Deny from all
</LimitExcept>

# Suspicious request patterns engelle
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} proc/self/environ [OR]
RewriteCond %{QUERY_STRING} mosConfig_[a-zA-Z_]{1,21}(=|\%3D) [OR]
RewriteCond %{QUERY_STRING} base64_(en|de)code[^(]*\([^)]*\) [OR]
RewriteCond %{QUERY_STRING} (<|%3C)([^s]*s)+cript.*(>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC]
RewriteRule .* - [F]

# User-Agent filtering - Allow search engine bots
RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (libwww-perl|python|nikto|scan) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
# Allow major search engine bots
RewriteCond %{HTTP_USER_AGENT} !Googlebot [NC]
RewriteCond %{HTTP_USER_AGENT} !Bingbot [NC]
RewriteCond %{HTTP_USER_AGENT} !YandexBot [NC]
RewriteCond %{HTTP_USER_AGENT} !facebookexternalhit [NC]
RewriteCond %{HTTP_USER_AGENT} !Twitterbot [NC]
RewriteCond %{HTTP_USER_AGENT} !LinkedInBot [NC]
RewriteCond %{HTTP_USER_AGENT} !WhatsApp [NC]
RewriteCond %{HTTP_USER_AGENT} !Slackbot [NC]
RewriteRule .* - [F]

# CORS headers for API
<FilesMatch "\.(php)$">
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, Authorization"
</FilesMatch>

# File upload limits
php_value upload_max_filesize 100M
php_value post_max_size 100M
php_value max_execution_time 300
php_value max_input_time 300

# Video files serving
RewriteRule ^uploads/videos/(.*)$ uploads/videos/$1 [L]

# Allow video file types
<FilesMatch "\.(mp4|avi|mov|wmv|webm)$">
    Header set Content-Type video/mp4
    Header set Cache-Control "max-age=31536000, public"
</FilesMatch>

# SEO ve Performance İyileştirmeleri
# Gzip Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType text/html "access plus 5 minutes"
    ExpiresDefault "access plus 2 days"
</IfModule>

# ETags
<IfModule mod_headers.c>
    # Remove ETags
    Header unset ETag
    FileETag None

    # Cache Control for images
    <FilesMatch "\.(ico|pdf|flv|jpg|jpeg|png|gif|webp|js|css|swf)$">
        Header set Cache-Control "max-age=31536000, public"
    </FilesMatch>

    # Cache Control for HTML - Kısa cache süresi
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "max-age=300, public, must-revalidate"
    </FilesMatch>

    # PHP dosyaları için no-cache (dinamik içerik)
    <FilesMatch "\.php$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>

    # Blog ve dinamik sayfalar için özel no-cache
    <FilesMatch "(blog|admin|api).*\.php$">
        Header set Cache-Control "no-cache, no-store, must-revalidate, max-age=0"
        Header set Pragma "no-cache"
        Header set Expires "Thu, 01 Jan 1970 00:00:00 GMT"
    </FilesMatch>

    # CSS Cache Control for Production
    <FilesMatch "styles\.css$">
        Header set Cache-Control "max-age=86400, public, must-revalidate"
        Header set Expires "access plus 1 day"
    </FilesMatch>
</IfModule>
