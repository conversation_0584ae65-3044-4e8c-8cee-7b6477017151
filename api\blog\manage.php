<?php
// Blog Management API
error_reporting(0);
ini_set('display_errors', 0);

// Start output buffering and clean any previous output
ob_start();
if (ob_get_level() > 1) {
    ob_clean();
}

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';

// XSS koruması için blog input sanitization
function sanitizeBlogInput($data) {
    $sanitized = [];

    // Başlık - HTML tagları kaldır
    if (isset($data['title'])) {
        $sanitized['title'] = htmlspecialchars(strip_tags(trim($data['title'])), ENT_QUOTES, 'UTF-8');
    }

    // İçerik - Sadece güvenli HTML taglarına izin ver
    if (isset($data['content'])) {
        $sanitized['content'] = sanitizeHtmlContent($data['content']);
    }

    // Özet - HTML tagları kaldır
    if (isset($data['excerpt'])) {
        $sanitized['excerpt'] = htmlspecialchars(strip_tags(trim($data['excerpt'])), ENT_QUOTES, 'UTF-8');
    }

    // Meta alanları - HTML tagları kaldır
    if (isset($data['meta_title'])) {
        $sanitized['meta_title'] = htmlspecialchars(strip_tags(trim($data['meta_title'])), ENT_QUOTES, 'UTF-8');
    }

    if (isset($data['meta_description'])) {
        $sanitized['meta_description'] = htmlspecialchars(strip_tags(trim($data['meta_description'])), ENT_QUOTES, 'UTF-8');
    }

    if (isset($data['meta_keywords'])) {
        $sanitized['meta_keywords'] = htmlspecialchars(strip_tags(trim($data['meta_keywords'])), ENT_QUOTES, 'UTF-8');
    }

    // Diğer alanları kopyala
    foreach ($data as $key => $value) {
        if (!isset($sanitized[$key]) && !is_array($value)) {
            $sanitized[$key] = htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
        } elseif (!isset($sanitized[$key])) {
            $sanitized[$key] = $value;
        }
    }

    return $sanitized;
}

// HTML içerik için güvenli sanitization
function sanitizeHtmlContent($content) {
    // İzin verilen HTML tagları
    $allowedTags = '<p><br><strong><em><i><b><u><h1><h2><h3><h4><h5><h6><ul><ol><li><blockquote><a><img>';

    // Tehlikeli script taglarını kaldır
    $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi', '', $content);

    // JavaScript event handlerları kaldır
    $content = preg_replace('/on\w+\s*=\s*["\'][^"\']*["\']/i', '', $content);

    // JavaScript: protokolünü kaldır
    $content = preg_replace('/javascript:/i', '', $content);

    // İzin verilen tagları filtrele
    $content = strip_tags($content, $allowedTags);

    return $content;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check admin authentication
    $user = getCurrentUser($db);
    if (!$user || $user['role'] !== 'admin') {
        sendJsonResponse([
            'success' => false,
            'message' => 'Yetkiniz bulunmamaktadır.'
        ], 403);
    }
    
    $method = $_SERVER['REQUEST_METHOD'];

    // Handle method override for FormData compatibility
    if ($method === 'POST' && isset($_POST['_method'])) {
        $method = $_POST['_method'];
    }

    switch ($method) {
        case 'GET':
            handleGetRequest($db);
            break;
        case 'POST':
            handlePostRequest($db);
            break;
        case 'PUT':
            // Handle both real PUT and POST with method override
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST)) {
                // Method override via POST - use $_POST data directly
                $input = $_POST;
                if (!isset($input['id'])) {
                    $input['id'] = $_GET['id'] ?? null;
                }
                if (!isset($input['id']) || empty($input['id'])) {
                    throw new Exception('Blog yazısı ID\'si gerekli.');
                }
                updateBlogPost($db, $input);
            } else {
                // Real PUT request
                handlePutRequest($db);
            }
            break;
        case 'DELETE':
            handleDeleteRequest($db);
            break;
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'message' => 'Desteklenmeyen HTTP metodu.'
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    // Clean any output buffer
    if (ob_get_level()) {
        ob_clean();
    }

    error_log("Blog manage API error: " . $e->getMessage());
    http_response_code(500);

    // Ensure clean JSON output
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Sunucu hatası oluştu: ' . $e->getMessage(),
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

function handleGetRequest($db) {
    $action = $_GET['action'] ?? 'list';
    
    switch ($action) {
        case 'list':
            getBlogPosts($db);
            break;
        case 'categories':
            getBlogCategories($db);
            break;
        case 'stats':
            getBlogStats($db);
            break;
        case 'single':
            $id = $_GET['id'] ?? null;
            if ($id) {
                getSingleBlogPost($db, $id);
            } else {
                throw new Exception('Blog yazısı ID\'si gerekli.');
            }
            break;
        default:
            getBlogPosts($db);
            break;
    }
}

function handlePostRequest($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        // Handle form data
        $input = $_POST;
    }
    
    createBlogPost($db, $input);
}

function handlePutRequest($db) {
    // For PUT requests with FormData, PHP doesn't populate $_POST
    // We need to parse the raw input manually

    $input = [];

    // First try to get from $_POST (if available)
    if (!empty($_POST)) {
        $input = $_POST;
    } else {
        // Try to parse raw input
        $rawInput = file_get_contents('php://input');

        // Try JSON first
        $jsonInput = json_decode($rawInput, true);
        if ($jsonInput) {
            $input = $jsonInput;
        } else {
            // Try to parse as form data
            parse_str($rawInput, $parsedInput);
            if (!empty($parsedInput)) {
                $input = $parsedInput;
            }
        }
    }

    // Get ID from URL parameter as fallback
    if (!isset($input['id'])) {
        $input['id'] = $_GET['id'] ?? null;
    }

    if (!isset($input['id']) || empty($input['id'])) {
        throw new Exception('Blog yazısı ID\'si gerekli.');
    }

    updateBlogPost($db, $input);
}

function handleDeleteRequest($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $id = $input['id'] ?? $_GET['id'] ?? null;
    
    if (!$id) {
        throw new Exception('Blog yazısı ID\'si gerekli.');
    }
    
    deleteBlogPost($db, $id);
}

function getBlogPosts($db) {
    $status = $_GET['status'] ?? 'all';
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(50, max(1, intval($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    $sql = "SELECT 
                bp.id,
                bp.title,
                bp.slug,
                bp.excerpt,
                bp.featured_image,
                bp.author,
                bp.status,
                bp.featured,
                bp.view_count,
                bp.reading_time,
                bp.published_at,
                bp.created_at,
                bp.updated_at,
                bc.name as category_name,
                bc.slug as category_slug
            FROM blog_posts bp
            LEFT JOIN blog_categories bc ON bp.category_id = bc.id";
    
    $params = [];
    
    if ($status !== 'all') {
        $sql .= " WHERE bp.status = :status";
        $params[':status'] = $status;
    }
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM ($sql) as counted_posts";
    $countStmt = $db->prepare($countSql);
    $countStmt->execute($params);
    $totalPosts = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $sql .= " ORDER BY bp.created_at DESC LIMIT :limit OFFSET :offset";
    $params[':limit'] = $limit;
    $params[':offset'] = $offset;
    
    $stmt = $db->prepare($sql);
    
    foreach ($params as $key => $value) {
        if ($key === ':limit' || $key === ':offset') {
            $stmt->bindValue($key, $value, PDO::PARAM_INT);
        } else {
            $stmt->bindValue($key, $value, PDO::PARAM_STR);
        }
    }
    
    $stmt->execute();
    $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format dates and images
    foreach ($posts as &$post) {
        $post['created_at_formatted'] = date('d.m.Y H:i', strtotime($post['created_at']));
        $post['published_at_formatted'] = $post['published_at'] ? date('d.m.Y', strtotime($post['published_at'])) : null;
        
        if ($post['featured_image'] && strpos($post['featured_image'], 'http') !== 0) {
            $post['featured_image'] = '/' . ltrim($post['featured_image'], '/');
        }
    }
    
    $totalPages = ceil($totalPosts / $limit);
    
    echo json_encode([
        'success' => true,
        'posts' => $posts,
        'pagination' => [
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalPosts' => $totalPosts,
            'hasNext' => $page < $totalPages,
            'hasPrev' => $page > 1
        ]
    ], JSON_UNESCAPED_UNICODE);
}

function getBlogCategories($db) {
    $sql = "SELECT id, name, slug, description FROM blog_categories ORDER BY name";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'categories' => $categories
    ], JSON_UNESCAPED_UNICODE);
}

function getBlogStats($db) {
    // Total posts
    $totalPostsStmt = $db->prepare("SELECT COUNT(*) as total FROM blog_posts");
    $totalPostsStmt->execute();
    $totalPosts = $totalPostsStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Total views
    $totalViewsStmt = $db->prepare("SELECT SUM(view_count) as total FROM blog_posts");
    $totalViewsStmt->execute();
    $totalViews = $totalViewsStmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
    
    // Published posts
    $publishedPostsStmt = $db->prepare("SELECT COUNT(*) as total FROM blog_posts WHERE status = 'published'");
    $publishedPostsStmt->execute();
    $publishedPosts = $publishedPostsStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Draft posts
    $draftPostsStmt = $db->prepare("SELECT COUNT(*) as total FROM blog_posts WHERE status = 'draft'");
    $draftPostsStmt->execute();
    $draftPosts = $draftPostsStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo json_encode([
        'success' => true,
        'stats' => [
            'totalPosts' => $totalPosts,
            'totalViews' => $totalViews,
            'publishedPosts' => $publishedPosts,
            'draftPosts' => $draftPosts
        ]
    ], JSON_UNESCAPED_UNICODE);
}

function getSingleBlogPost($db, $id) {
    $sql = "SELECT 
                bp.*,
                bc.name as category_name,
                bc.slug as category_slug
            FROM blog_posts bp
            LEFT JOIN blog_categories bc ON bp.category_id = bc.id
            WHERE bp.id = :id";
    
    $stmt = $db->prepare($sql);
    $stmt->bindValue(':id', $id, PDO::PARAM_INT);
    $stmt->execute();
    $post = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$post) {
        throw new Exception('Blog yazısı bulunamadı.');
    }
    
    // Get tags
    $tagsStmt = $db->prepare("
        SELECT bt.name, bt.slug 
        FROM blog_tags bt
        JOIN blog_post_tags bpt ON bt.id = bpt.tag_id
        WHERE bpt.post_id = :id
    ");
    $tagsStmt->bindValue(':id', $id, PDO::PARAM_INT);
    $tagsStmt->execute();
    $tags = $tagsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    $post['tags'] = $tags;
    $post['tags_string'] = implode(', ', array_column($tags, 'name'));
    
    if ($post['featured_image'] && strpos($post['featured_image'], 'http') !== 0) {
        $post['featured_image'] = '/' . ltrim($post['featured_image'], '/');
    }
    
    echo json_encode([
        'success' => true,
        'post' => $post
    ], JSON_UNESCAPED_UNICODE);
}

function createBlogPost($db, $data) {
    // Validate required fields
    if (empty($data['title']) || empty($data['content'])) {
        throw new Exception('Başlık ve içerik alanları gereklidir.');
    }
    
    // Generate slug if not provided
    $slug = !empty($data['slug']) ? $data['slug'] : generateSlug($data['title']);
    
    // Check if slug exists and make it unique
    $originalSlug = $slug;
    $counter = 1;

    while (true) {
        $slugCheck = $db->prepare("SELECT id FROM blog_posts WHERE slug = :slug");
        $slugCheck->bindValue(':slug', $slug);
        $slugCheck->execute();

        if (!$slugCheck->fetch()) {
            break; // Slug is unique, exit loop
        }

        // Create new slug with counter
        $slug = $originalSlug . '-' . $counter;
        $counter++;

        // Prevent infinite loop
        if ($counter > 100) {
            $slug = $originalSlug . '-' . time();
            break;
        }
    }
    
    // Auto-generate reading time if not provided
    if (empty($data['reading_time'])) {
        $wordCount = str_word_count(strip_tags($data['content']));
        $data['reading_time'] = max(1, ceil($wordCount / 200)); // 200 words per minute
    }
    
    // Auto-generate excerpt if not provided
    if (empty($data['excerpt'])) {
        $data['excerpt'] = substr(strip_tags($data['content']), 0, 150) . '...';
    }
    
    // Handle image upload
    $featuredImage = null;
    if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
        $featuredImage = handleImageUpload($_FILES['featured_image']);
    } elseif (!empty($data['featured_image'])) {
        $featuredImage = $data['featured_image'];
    }
    
    // Set published_at if status is published
    $publishedAt = null;
    $status = $data['status'] ?? 'draft';
    if ($status === 'published') {
        $publishedAt = date('Y-m-d H:i:s');
    }
    
    $sql = "INSERT INTO blog_posts (
                title, slug, excerpt, content, category_id, author, 
                featured_image, status, featured, reading_time, 
                meta_title, meta_description, meta_keywords, 
                published_at, created_at, updated_at
            ) VALUES (
                :title, :slug, :excerpt, :content, :category_id, :author,
                :featured_image, :status, :featured, :reading_time,
                :meta_title, :meta_description, :meta_keywords,
                :published_at, NOW(), NOW()
            )";
    
    $stmt = $db->prepare($sql);
    $stmt->bindValue(':title', $data['title']);
    $stmt->bindValue(':slug', $slug);
    $stmt->bindValue(':excerpt', $data['excerpt']);
    $stmt->bindValue(':content', $data['content']);
    $stmt->bindValue(':category_id', $data['category_id'] ?: null, PDO::PARAM_INT);
    $stmt->bindValue(':author', $data['author'] ?: 'Tansu Şahal');
    $stmt->bindValue(':featured_image', $featuredImage);
    $stmt->bindValue(':status', $data['status'] ?: 'draft');
    $stmt->bindValue(':featured', isset($data['featured']) ? 1 : 0, PDO::PARAM_INT);
    $stmt->bindValue(':reading_time', $data['reading_time'], PDO::PARAM_INT);
    $stmt->bindValue(':meta_title', $data['meta_title'] ?: $data['title']);
    $stmt->bindValue(':meta_description', $data['meta_description'] ?: $data['excerpt']);
    $stmt->bindValue(':meta_keywords', $data['meta_keywords'] ?: '');
    $stmt->bindValue(':published_at', $publishedAt);
    
    if ($stmt->execute()) {
        $postId = $db->lastInsertId();
        
        // Handle tags
        if (!empty($data['tags'])) {
            handleTags($db, $postId, $data['tags']);
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Blog yazısı başarıyla oluşturuldu.',
            'post_id' => $postId,
            'slug' => $slug
        ], JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('Blog yazısı oluşturulurken hata oluştu.');
    }
}

function updateBlogPost($db, $data) {
    $id = $data['id'];
    
    // Get current post data
    $currentPost = $db->prepare("SELECT * FROM blog_posts WHERE id = :id");
    $currentPost->bindValue(':id', $id, PDO::PARAM_INT);
    $currentPost->execute();
    $current = $currentPost->fetch(PDO::FETCH_ASSOC);
    
    if (!$current) {
        throw new Exception('Blog yazısı bulunamadı.');
    }
    
    // Handle slug - regenerate from title if not provided
    if (!empty($data['slug'])) {
        $slug = $data['slug'];
    } else {
        // Regenerate slug from title if title changed or no slug provided
        $slug = generateSlug($data['title']);
    }

    // Always check if slug exists (except for current post) and make it unique
    $originalSlug = $slug;
    $counter = 1;

    while (true) {
        $slugCheck = $db->prepare("SELECT id FROM blog_posts WHERE slug = :slug AND id != :id");
        $slugCheck->bindValue(':slug', $slug);
        $slugCheck->bindValue(':id', $id, PDO::PARAM_INT);
        $slugCheck->execute();

        if (!$slugCheck->fetch()) {
            break; // Slug is unique, exit loop
        }

        // Create new slug with counter
        $slug = $originalSlug . '-' . $counter;
        $counter++;

        // Prevent infinite loop
        if ($counter > 100) {
            $slug = $originalSlug . '-' . time();
            break;
        }
    }
    
    // Handle image upload
    $featuredImage = $current['featured_image'];
    if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
        $featuredImage = handleImageUpload($_FILES['featured_image']);
        
        // Delete old image if exists
        if ($current['featured_image'] && file_exists('../../' . ltrim($current['featured_image'], '/'))) {
            unlink('../../' . ltrim($current['featured_image'], '/'));
        }
    } elseif (isset($data['featured_image'])) {
        // Handle featured_image properly
        if (is_array($data['featured_image'])) {
            $featuredImage = ''; // Set to empty string if array (shouldn't happen)
        } else {
            $featuredImage = $data['featured_image'];
        }
    }
    
    // Validate category_id
    $categoryId = $data['category_id'] ?? $current['category_id'];

    // Handle empty string as null
    if ($categoryId === '' || $categoryId === '0') {
        $categoryId = null;
    }

    // If we have a category_id, validate it exists
    if ($categoryId !== null) {
        $catCheck = $db->prepare("SELECT id FROM blog_categories WHERE id = :id");
        $catCheck->bindValue(':id', $categoryId, PDO::PARAM_INT);
        $catCheck->execute();
        $validCategory = $catCheck->fetch();

        if (!$validCategory) {
            // Invalid category_id, use first available category or null
            $firstCat = $db->query("SELECT id FROM blog_categories ORDER BY id LIMIT 1")->fetchColumn();
            $categoryId = $firstCat ?: null;
        }
    }

    // Set published_at if status changes to published
    $publishedAt = $current['published_at'];
    $newStatus = $data['status'] ?? $current['status'];
    if ($newStatus === 'published' && $current['status'] !== 'published') {
        $publishedAt = date('Y-m-d H:i:s');
    } elseif ($newStatus !== 'published') {
        $publishedAt = null;
    }
    
    $sql = "UPDATE blog_posts SET 
                title = :title,
                slug = :slug,
                excerpt = :excerpt,
                content = :content,
                category_id = :category_id,
                author = :author,
                featured_image = :featured_image,
                status = :status,
                featured = :featured,
                reading_time = :reading_time,
                meta_title = :meta_title,
                meta_description = :meta_description,
                meta_keywords = :meta_keywords,
                published_at = :published_at,
                updated_at = NOW()
            WHERE id = :id";
    
    $stmt = $db->prepare($sql);
    $stmt->bindValue(':id', $id, PDO::PARAM_INT);
    $stmt->bindValue(':title', $data['title'] ?? $current['title']);
    $stmt->bindValue(':slug', $slug);
    $stmt->bindValue(':excerpt', $data['excerpt'] ?? $current['excerpt']);
    $stmt->bindValue(':content', $data['content'] ?? $current['content']);
    $stmt->bindValue(':category_id', $categoryId, $categoryId === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
    $stmt->bindValue(':author', $data['author'] ?? $current['author']);
    $stmt->bindValue(':featured_image', $featuredImage);
    $stmt->bindValue(':status', $data['status'] ?? $current['status']);
    $stmt->bindValue(':featured', isset($data['featured']) ? 1 : 0, PDO::PARAM_INT);
    $stmt->bindValue(':reading_time', $data['reading_time'] ?? $current['reading_time'], PDO::PARAM_INT);
    $stmt->bindValue(':meta_title', $data['meta_title'] ?? $current['meta_title']);
    $stmt->bindValue(':meta_description', $data['meta_description'] ?? $current['meta_description']);
    $stmt->bindValue(':meta_keywords', $data['meta_keywords'] ?? $current['meta_keywords']);
    $stmt->bindValue(':published_at', $publishedAt);
    
    if ($stmt->execute()) {
        // Handle tags
        if (isset($data['tags'])) {
            // Delete existing tags
            $deleteTags = $db->prepare("DELETE FROM blog_post_tags WHERE post_id = :id");
            $deleteTags->bindValue(':id', $id, PDO::PARAM_INT);
            $deleteTags->execute();
            
            // Add new tags
            if (!empty($data['tags'])) {
                handleTags($db, $id, $data['tags']);
            }
        }
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Blog yazısı başarıyla güncellendi.',
            'slug' => $slug
        ]);
    } else {
        throw new Exception('Blog yazısı güncellenirken hata oluştu.');
    }
}

function deleteBlogPost($db, $id) {
    // Get post data to delete associated files
    $postStmt = $db->prepare("SELECT featured_image FROM blog_posts WHERE id = :id");
    $postStmt->bindValue(':id', $id, PDO::PARAM_INT);
    $postStmt->execute();
    $post = $postStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$post) {
        throw new Exception('Blog yazısı bulunamadı.');
    }
    
    // Delete associated tags
    $deleteTagsStmt = $db->prepare("DELETE FROM blog_post_tags WHERE post_id = :id");
    $deleteTagsStmt->bindValue(':id', $id, PDO::PARAM_INT);
    $deleteTagsStmt->execute();
    
    // Delete post
    $deleteStmt = $db->prepare("DELETE FROM blog_posts WHERE id = :id");
    $deleteStmt->bindValue(':id', $id, PDO::PARAM_INT);
    
    if ($deleteStmt->execute()) {
        // Delete associated image file
        if ($post['featured_image'] && file_exists('../../' . ltrim($post['featured_image'], '/'))) {
            unlink('../../' . ltrim($post['featured_image'], '/'));
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Blog yazısı başarıyla silindi.'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('Blog yazısı silinirken hata oluştu.');
    }
}

function handleTags($db, $postId, $tagsString) {
    if (empty($tagsString)) return;

    // Hashtag formatını destekle (#DoğalBeslenme #KatkısızYaşam formatında)
    $tags = [];

    // Önce hashtag formatını kontrol et
    if (strpos($tagsString, '#') !== false) {
        // Hashtag formatında - regex ile ayır
        preg_match_all('/#[a-zA-ZşğüöçıİŞĞÜÖÇ0-9]+/u', $tagsString, $matches);
        $tags = $matches[0]; // # işareti ile birlikte
    } else {
        // Virgülle ayrılmış format - eski sistem
        $tags = array_map('trim', explode(',', $tagsString));
        // # işareti ekle
        $tags = array_map(function($tag) {
            return strpos($tag, '#') === 0 ? $tag : '#' . $tag;
        }, $tags);
    }

    foreach ($tags as $tagName) {
        if (empty($tagName) || $tagName === '#') continue;

        // # işareti olmadan slug oluştur
        $cleanTagName = ltrim($tagName, '#');
        $tagSlug = generateSlug($cleanTagName);

        // Check if tag exists (name ile kontrol et)
        $tagStmt = $db->prepare("SELECT id FROM blog_tags WHERE name = :name");
        $tagStmt->bindValue(':name', $tagName);
        $tagStmt->execute();
        $tag = $tagStmt->fetch(PDO::FETCH_ASSOC);

        if (!$tag) {
            // Create new tag
            $createTagStmt = $db->prepare("INSERT INTO blog_tags (name, slug, created_at) VALUES (:name, :slug, NOW())");
            $createTagStmt->bindValue(':name', $tagName);
            $createTagStmt->bindValue(':slug', $tagSlug);
            $createTagStmt->execute();
            $tagId = $db->lastInsertId();
        } else {
            $tagId = $tag['id'];
        }

        // Link tag to post
        $linkStmt = $db->prepare("INSERT IGNORE INTO blog_post_tags (post_id, tag_id) VALUES (:post_id, :tag_id)");
        $linkStmt->bindValue(':post_id', $postId, PDO::PARAM_INT);
        $linkStmt->bindValue(':tag_id', $tagId, PDO::PARAM_INT);
        $linkStmt->execute();
    }
}

function handleImageUpload($file) {
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $maxSize = 5 * 1024 * 1024; // 5MB
    
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('Desteklenmeyen dosya formatı.');
    }
    
    if ($file['size'] > $maxSize) {
        throw new Exception('Dosya boyutu çok büyük (max 5MB).');
    }
    
    $uploadDir = '../../uploads/blog/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'blog_' . time() . '_' . uniqid() . '.' . $extension;
    $targetPath = $uploadDir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $targetPath)) {
        return 'uploads/blog/' . $filename;
    } else {
        throw new Exception('Dosya yüklenirken hata oluştu.');
    }
}

function generateSlug($text) {
    // For tags, preserve Turkish characters in slug for better search compatibility
    // Only replace problematic characters for URL safety
    $text = trim($text);

    // Remove # symbol if present at the beginning
    $text = ltrim($text, '#');

    // Convert to lowercase using UTF-8
    $text = mb_strtolower($text, 'UTF-8');

    // Replace spaces and special characters with hyphens, but keep Turkish chars
    $text = preg_replace('/[^a-zA-ZşğüöçıİŞĞÜÖÇ0-9]+/u', '-', $text);
    $text = preg_replace('/-+/', '-', $text); // Remove multiple hyphens
    $text = trim($text, '-');

    // Limit slug length to 50 characters to avoid MySQL key length issues
    if (mb_strlen($text, 'UTF-8') > 50) {
        $text = mb_substr($text, 0, 50, 'UTF-8');
        $text = rtrim($text, '-');
    }

    return $text;
}
?>
