document.addEventListener("DOMContentLoaded", () => {
  
  // <PERSON><PERSON> çal<PERSON>tır - gecikme yok
  updateHeaderUserInfo()
  updateFavoritesCount()

  // Hamburger menü dışına tıklayınca kapatma
  setupMobileMenuCloseOnOutsideClick()

  // Logout handler
  const logoutLink = document.getElementById("logoutLink")
  const mobileLogoutLink = document.getElementById("mobileLogoutLink")
  const mobileLogoutLink2 = document.getElementById("mobileLogoutLink2")
  const mobileLogoutLink3 = document.getElementById("mobileLogoutLink3")

  if (logoutLink) {
    logoutLink.addEventListener("click", (e) => {
      e.preventDefault()
      logout()
    })
  }

  if (mobileLogoutLink) {
    mobileLogoutLink.addEventListener("click", (e) => {
      e.preventDefault()
      logout()
    })
  }

  if (mobileLogoutLink2) {
    mobileLogoutLink2.addEventListener("click", (e) => {
      e.preventDefault()
      logout()
    })
  }

  if (mobileLogoutLink3) {
    mobileLogoutLink3.addEventListener("click", (e) => {
      e.preventDefault()
      logout()
    })
  }
})

function updateHeaderUserInfo() {
  const userInfo = getUserInfo()
  const userGreeting = document.getElementById("userGreeting")
  const mobileUserGreeting = document.getElementById("mobileUserGreeting")
  const loginLink = document.getElementById("loginLink")
  const registerLink = document.getElementById("registerLink")
  const logoutLink = document.getElementById("logoutLink")
  const adminLink = document.getElementById("adminLink")
  
  // Mobile menü elementleri
  const mobileLoginLink = document.getElementById("mobileLoginLink")
  const mobileRegisterLink = document.getElementById("mobileRegisterLink")
  const mobileLogoutLink = document.getElementById("mobileLogoutLink")
  const mobileAdminLink = document.getElementById("mobileAdminLink")

  if (userInfo && userInfo.token) {
    // Kullanıcı giriş yapmış
    
    if (userGreeting) {
      // Uzun isimleri kısalt
      const firstName = userInfo.name.split(' ')[0];
      const displayName = firstName.length > 10 ? firstName.substring(0, 10) + '...' : firstName;
      userGreeting.innerHTML = `<i class="fas fa-user-circle me-1"></i>${displayName}`;
      userGreeting.style.display = "inline-flex";
      userGreeting.style.alignItems = "center";

    }

    if (mobileUserGreeting) {
      mobileUserGreeting.textContent = `Merhaba, ${userInfo.name}!`
      mobileUserGreeting.style.display = "block"
    }

    if (loginLink) {
      loginLink.style.display = "none"
    }
    if (registerLink) {
      registerLink.style.display = "none"
    }
    if (logoutLink) {
      logoutLink.style.display = "inline"
    }

    // Mobile menü güncellemeleri
    if (mobileLoginLink) {
      mobileLoginLink.style.display = "none"
    }
    if (mobileRegisterLink) {
      mobileRegisterLink.style.display = "none"
    }
    if (mobileLogoutLink) {
      mobileLogoutLink.style.display = "block"
    }

    // Admin kontrolü
    if (adminLink && userInfo.role === "admin") {
      adminLink.style.display = "inline"
    }
    if (mobileAdminLink && userInfo.role === "admin") {
      mobileAdminLink.style.display = "block"
    }
  } else {
    // Kullanıcı giriş yapmamış

    if (userGreeting) {
      userGreeting.style.display = "none"
    }
    if (mobileUserGreeting) {
      mobileUserGreeting.style.display = "none"
    }
    if (loginLink) {
      loginLink.style.display = "inline"
    }
    if (registerLink) {
      registerLink.style.display = "inline"
    }
    if (logoutLink) {
      logoutLink.style.display = "none"
    }
    if (adminLink) {
      adminLink.style.display = "none"
    }

    // Mobile menü güncellemeleri
    if (mobileLoginLink) {
      mobileLoginLink.style.display = "block"
    }
    if (mobileRegisterLink) {
      mobileRegisterLink.style.display = "block"
    }
    if (mobileLogoutLink) {
      mobileLogoutLink.style.display = "none"
    }
    if (mobileAdminLink) {
      mobileAdminLink.style.display = "none"
    }
  }

  // Update independent mobile menu if it exists
  if (typeof updateMobileMenuUserInfo === 'function') {
    setTimeout(updateMobileMenuUserInfo, 100);
  }
}

function updateFavoritesCount() {
  const favorites = getFavorites()
  const totalItems = favorites.length
  const favoritesCountElement = document.getElementById("favorites-item-count")
  if (favoritesCountElement) {
    favoritesCountElement.textContent = totalItems
  }

  // Update independent mobile menu favorites count
  if (typeof updateMobileMenuUserInfo === 'function') {
    setTimeout(updateMobileMenuUserInfo, 50);
  }
}

function getUserInfo() {
  const userInfoString = localStorage.getItem("userInfo")
  try {
    return userInfoString ? JSON.parse(userInfoString) : null
  } catch (e) {
    localStorage.removeItem("userInfo")
    return null
  }
}

function getFavorites() {
  const favoritesString = localStorage.getItem("userFavorites")
  try {
    const favorites = favoritesString ? JSON.parse(favoritesString) : []
    return Array.isArray(favorites) ? favorites : []
  } catch (e) {
    return []
  }
}

function logout() {
  localStorage.removeItem("userInfo")
  updateHeaderUserInfo()
  updateFavoritesCount()

  // Admin sayfasındaysa ana sayfaya yönlendir
  if (window.location.pathname.includes("admin.php")) {
    window.location.href = "/"
  } else {
    // Sayfayı yenile
    window.location.reload()
  }
}

// Handle mobile logout - global function for all mobile menus
function handleMobileLogout() {
  // Close any open mobile menu
  if (typeof closeIndependentMobileMenu === 'function') {
    closeIndependentMobileMenu();
  } else if (typeof closeMobileMenu === 'function') {
    closeMobileMenu();
  }

  // Perform logout
  logout();
}

// Hamburger menü dışına tıklayınca kapatma fonksiyonu
function setupMobileMenuCloseOnOutsideClick() {
  const navbarCollapse = document.getElementById('mainNav')
  const navbarToggler = document.querySelector('.navbar-toggler')
  
  if (!navbarCollapse || !navbarToggler) return
  
  // Menü açık mı kontrol et
  const isMenuOpen = () => {
    return navbarCollapse.classList.contains('show')
  }
  
  // Menüyü kapat - PERFORMANCE OPTIMIZED
  const closeMenu = () => {
    if (isMenuOpen()) {
      navbarToggler.click()
    }
  }
  
  // Menüyü aç - PERFORMANCE OPTIMIZED
  const openMenu = () => {
    if (!isMenuOpen()) {
      navbarToggler.click()
    }
  }
  
  // Document'a click event listener ekle
  document.addEventListener('click', (e) => {
    // Eğer menü açık değilse hiçbir şey yapma
    if (!isMenuOpen()) return
    
    // Tıklanan element menü içinde mi kontrol et
    const isClickInsideMenu = navbarCollapse.contains(e.target)
    const isClickOnToggler = navbarToggler.contains(e.target)
    
    // Eğer menü dışına tıklandıysa ve toggler'a tıklanmadıysa menüyü kapat
    if (!isClickInsideMenu && !isClickOnToggler) {
      closeMenu()
    }
  })
  
  // ESC tuşu ile kapatma
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && isMenuOpen()) {
      closeMenu()
    }
  })
  

}

// Mobil menüyü kapat - global fonksiyon
function closeMobileMenu() {
  const navbarCollapse = document.getElementById('mainNav')
  const navbarToggler = document.querySelector('.navbar-toggler')
  
  if (navbarCollapse && navbarToggler && navbarCollapse.classList.contains('show')) {
    navbarToggler.click()
  }
}
