<?php
// UTF-8 encoding için
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    // Get tag slug or ID
    $tagIdentifier = $_GET['tag'] ?? $_GET['tag_slug'] ?? $_GET['tag_id'] ?? null;
    
    if (!$tagIdentifier) {
        throw new Exception('Tag identifier is required');
    }
    
    // Pagination parameters
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(50, max(1, intval($_GET['limit'] ?? 12)));
    $offset = ($page - 1) * $limit;
    
    // Determine if identifier is numeric (ID) or string (slug)
    $isId = is_numeric($tagIdentifier);
    $tagWhereField = $isId ? 'bt.id' : 'bt.slug';
    
    // First, get tag info
    if ($isId) {
        $tagSql = "SELECT id, name, slug FROM blog_tags WHERE id = ?";
    } else {
        $tagSql = "SELECT id, name, slug FROM blog_tags WHERE slug = ?";
    }
    $tagStmt = $db->prepare($tagSql);
    $tagStmt->execute([$tagIdentifier]);
    $tag = $tagStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tag) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Tag not found'
        ]);
        exit;
    }
    
    // Get posts with this tag
    $sql = "SELECT
                bp.id,
                bp.title,
                bp.slug,
                bp.excerpt,
                bp.content,
                bp.featured_image,
                bp.author,
                bp.meta_title,
                bp.meta_description,
                bp.meta_keywords,
                bp.reading_time,
                bp.view_count,
                bp.featured,
                bp.published_at,
                bp.created_at,
                bc.name as category_name,
                bc.slug as category_slug
            FROM blog_posts bp
            INNER JOIN blog_post_tags bpt ON bp.id = bpt.post_id
            LEFT JOIN blog_categories bc ON bp.category_id = bc.id
            WHERE bpt.tag_id = :tag_id AND bp.status = 'published'
            ORDER BY bp.published_at DESC
            LIMIT :limit OFFSET :offset";
    
    $stmt = $db->prepare($sql);
    $stmt->bindValue(':tag_id', $tag['id'], PDO::PARAM_INT);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    
    $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);



    // Get total count for pagination
    $countSql = "SELECT COUNT(DISTINCT bp.id)
                 FROM blog_posts bp
                 INNER JOIN blog_post_tags bpt ON bp.id = bpt.post_id
                 WHERE bpt.tag_id = :tag_id AND bp.status = 'published'";
    
    $countStmt = $db->prepare($countSql);
    $countStmt->bindValue(':tag_id', $tag['id'], PDO::PARAM_INT);
    $countStmt->execute();
    $totalPosts = $countStmt->fetchColumn();
    
    // Format posts
    foreach ($posts as &$post) {
        $post['id'] = (int)$post['id'];
        $post['featured'] = (bool)$post['featured'];
        $post['view_count'] = (int)($post['view_count'] ?? 0);
        $post['reading_time'] = (int)($post['reading_time'] ?? 5);
        
        // Format dates
        if ($post['published_at']) {
            $post['published_at_formatted'] = date('d.m.Y H:i', strtotime($post['published_at']));
        }
        if ($post['created_at']) {
            $post['created_at_formatted'] = date('d.m.Y H:i', strtotime($post['created_at']));
        }
        
        // Generate full URL for featured image
        if ($post['featured_image'] && strpos($post['featured_image'], 'http') !== 0) {
            $post['featured_image'] = '/' . ltrim($post['featured_image'], '/');
        }
        
        // Clean HTML from content for excerpt
        if (!empty($post['content'])) {
            $post['plain_content'] = strip_tags($post['content']);
        }
        
        // Fetch all tags for this post
        $tagSql = "SELECT bt.name, bt.slug 
                   FROM blog_tags bt 
                   INNER JOIN blog_post_tags bpt ON bt.id = bpt.tag_id 
                   WHERE bpt.post_id = ?";
        $tagStmt = $db->prepare($tagSql);
        $tagStmt->execute([$post['id']]);
        $post['tags'] = $tagStmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Calculate pagination
    $totalPages = ceil($totalPosts / $limit);
    
    // Prepare response
    $response = [
        'success' => true,
        'tag' => $tag,
        'posts' => $posts,
        'pagination' => [
            'currentPage' => $page,
            'totalPosts' => (int)$totalPosts,
            'totalPages' => $totalPages,
            'limit' => $limit,
            'hasNext' => $page < $totalPages,
            'hasPrev' => $page > 1
        ],

    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_NUMERIC_CHECK);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
