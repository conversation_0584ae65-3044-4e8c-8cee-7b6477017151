// Blog Admin JavaScript Functions

// Blog variables
let blogPosts = []
let blogCategories = []
let editingBlogPostId = null

function getUserInfo() {
    const userInfoString = localStorage.getItem("userInfo")
    try {
        return userInfoString ? JSON.parse(userInfoString) : null
    } catch (e) {
        localStorage.removeItem("userInfo")
        return null
    }
}

// Blog Data Loading Functions
async function loadBlogData() {
    try {
        await Promise.all([
            loadBlogPosts(),
            loadBlogCategories(),
            loadBlogStats()
        ])
    } catch (error) {
        console.error('Blog data loading error:', error)
        showNotification('Blog verileri yüklenirken hata olu<PERSON>tu: ' + error.message, 'error')
    }
}

async function loadBlogPosts() {
    try {
        // Try to load from admin API first (for edit capabilities)
        const userInfo = getUserInfo()
        if (userInfo && userInfo.token) {
            try {
                const response = await fetch('/api/blog/manage.php?action=list', {
                    headers: {
                        'Authorization': `Bearer ${userInfo.token}`,
                        'Content-Type': 'application/json'
                    }
                })

                if (response.ok) {
                    const data = await response.json()
                    if (data.success) {
                        blogPosts = data.posts
                        displayBlogPosts()
                        return
                    }
                }
            } catch (error) {
                // Silently fall back to public API
            }
        }

        // Fallback to public API
        const response = await fetch('/api/blog/posts.php?status=published&limit=50')
        const data = await response.json()
        
        if (data.success) {
            // Adapt public API response to admin format
            blogPosts = data.posts.map(post => ({
                ...post,
                created_at_formatted: formatDate(post.created_at || post.published_at),
                published_at_formatted: formatDate(post.published_at)
            }))
            displayBlogPosts()
        } else {
            throw new Error(data.message || 'Blog yazıları yüklenemedi')
        }
    } catch (error) {
        console.error('Blog posts loading error:', error)
        showNotification('Blog yazıları yüklenirken hata oluştu: ' + error.message, 'error')
    }
}

async function loadBlogCategories() {
    try {
        const response = await fetch('/api/blog/categories.php', {
            headers: {
                'Accept': 'application/json; charset=utf-8',
                'Content-Type': 'application/json; charset=utf-8'
            }
        })

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        console.log('Categories loaded:', data)

        if (data.success) {
            blogCategories = data.categories
            populateBlogCategorySelect()
            console.log('Categories populated:', blogCategories)
        } else {
            console.error('Categories API returned error:', data.message)
        }
    } catch (error) {
        console.error('Blog categories loading error:', error)
        // Fallback kategoriler
        blogCategories = [
            { id: 1, name: 'Salamura Üretimi', slug: 'salamura-uretimi' },
            { id: 2, name: 'Turşu Rehberi', slug: 'tursu-rehberi' },
            { id: 3, name: 'Doğal Beslenme', slug: 'dogal-beslenme' },
            { id: 4, name: 'Fermentasyon', slug: 'fermentasyon' }
        ]
        populateBlogCategorySelect()
        console.log('Using fallback categories')
    }
}

async function loadBlogStats() {
    try {
        // First try to get stats from admin API if user is logged in
        const userInfo = getUserInfo()

        if (userInfo && userInfo.token) {
            try {
                const adminResponse = await fetch('/api/blog/manage.php?action=list&limit=1000', {
                    headers: {
                        'Authorization': `Bearer ${userInfo.token}`,
                        'Content-Type': 'application/json'
                    }
                })

                if (adminResponse.ok) {
                    const adminText = await adminResponse.text()
                    const adminData = JSON.parse(adminText)

                    if (adminData.success && adminData.posts) {
                        const allPosts = adminData.posts
                        const publishedPosts = allPosts.filter(post => post.status === 'published')
                        const draftPosts = allPosts.filter(post => post.status === 'draft')
                        const totalViews = allPosts.reduce((sum, post) => sum + (parseInt(post.view_count) || 0), 0)

                        const stats = {
                            totalPosts: allPosts.length,
                            totalViews: totalViews,
                            publishedPosts: publishedPosts.length,
                            draftPosts: draftPosts.length
                        }

                        updateBlogStats(stats)
                        return
                    }
                }
            } catch (adminError) {
                // Silently fall back to public API
            }
        }

        // Fallback to public API
        const response = await fetch('/api/blog/posts-clean.php?limit=1000')

        if (!response.ok) {
            throw new Error(`Public API failed with status: ${response.status}`)
        }

        const responseText = await response.text()
        let data
        try {
            data = JSON.parse(responseText)
        } catch (parseError) {
            throw new Error('API yanıtı geçersiz JSON formatında')
        }

        if (data.success && data.posts && Array.isArray(data.posts)) {
            const totalPosts = data.posts.length
            const totalViews = data.posts.reduce((sum, post) => sum + (parseInt(post.view_count) || 0), 0)

            const stats = {
                totalPosts: totalPosts,
                totalViews: totalViews,
                publishedPosts: totalPosts, // Public API only shows published
                draftPosts: 0
            }

            updateBlogStats(stats)
        } else {
            throw new Error('API response contains no valid posts data')
        }
    } catch (error) {
        // Set default stats if all fails
        updateBlogStats({
            totalPosts: 0,
            totalViews: 0,
            publishedPosts: 0,
            draftPosts: 0
        })
    }
}

// Display Functions
function displayBlogPosts() {
    const tbody = document.getElementById('blogPostsListBody')
    if (!tbody) return

    if (blogPosts.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="fas fa-newspaper fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">Henüz blog yazısı bulunmuyor.</p>
                </td>
            </tr>
        `
        return
    }

    const postsHtml = blogPosts.map(post => `
        <tr>
            <td>
                <img src="${post.featured_image || '/images/logo.jpg'}" 
                     alt="${post.title}" 
                     class="img-thumbnail" 
                     style="width: 60px; height: 60px; object-fit: cover;">
            </td>
            <td>
                <div>
                    <h6 class="mb-1">${post.title}</h6>
                    <small class="text-muted">
                        ${post.slug}
                        ${post.featured ? '<span class="badge bg-warning ms-2">Öne Çıkan</span>' : ''}
                    </small>
                </div>
            </td>
            <td>
                <span class="badge bg-secondary">${post.category_name || 'Kategori Yok'}</span>
            </td>
            <td class="text-center">
                <span class="badge ${getBlogStatusClass(post.status)}">${getBlogStatusText(post.status)}</span>
            </td>
            <td class="text-center">
                <i class="fas fa-eye text-muted me-1"></i>
                ${post.view_count || 0}
            </td>
            <td class="text-center">
                <small>${post.created_at_formatted}</small>
            </td>
            <td class="text-center">
                <div class="btn-group" role="group" aria-label="Blog işlemleri">
                    <button class="btn btn-sm btn-outline-primary" onclick="editBlogPost(${post.id})" title="Düzenle">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="viewBlogPost('${post.slug}')" title="Görüntüle">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteBlogPost(${post.id})" title="Sil">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('')

    tbody.innerHTML = postsHtml
}

function populateBlogCategorySelect() {
    const select = document.getElementById('blogCategory')
    if (!select) {
        console.error('Blog category select element not found')
        return
    }

    select.innerHTML = '<option value="">Kategori Seçin</option>'

    if (!blogCategories || blogCategories.length === 0) {
        console.warn('No blog categories available')
        select.innerHTML += '<option value="" disabled>Kategori bulunamadı</option>'
        return
    }

    blogCategories.forEach(category => {
        const option = document.createElement('option')
        option.value = category.id
        // Türkçe karakterleri doğru göstermek için
        option.textContent = category.name || 'İsimsiz Kategori'
        select.appendChild(option)
        console.log('Added category option:', category.name)
    })

    console.log('Category select populated with', blogCategories.length, 'categories')
}

function updateBlogStats(stats) {
    const totalPostsEl = document.getElementById('totalBlogPosts')
    const totalViewsEl = document.getElementById('totalViews')

    if (totalPostsEl) {
        totalPostsEl.textContent = stats.totalPosts || 0
    }

    if (totalViewsEl) {
        totalViewsEl.textContent = stats.totalViews || 0
    }
}

// Event Listeners
function setupBlogEventListeners() {
    // Blog form submission
    const blogForm = document.getElementById('blogForm')
    if (blogForm) {
        blogForm.addEventListener('submit', handleBlogSubmit)
    }

    // Blog title auto-slug generation
    const blogTitle = document.getElementById('blogTitle')
    const blogSlug = document.getElementById('blogSlug')
    if (blogTitle && blogSlug) {
        blogTitle.addEventListener('input', () => {
            // Always generate slug from title, both in create and edit mode
            blogSlug.value = generateSlug(blogTitle.value)
        })
    }

    // Blog featured image preview
    const blogFeaturedImage = document.getElementById('blogFeaturedImage')
    if (blogFeaturedImage) {
        blogFeaturedImage.addEventListener('change', handleBlogImagePreview)
    }

    // Clear blog form button
    const clearBlogFormButton = document.getElementById('clearBlogFormButton')
    if (clearBlogFormButton) {
        clearBlogFormButton.addEventListener('click', clearBlogForm)
    }
}

// Form Handling
async function handleBlogSubmit(e) {
    e.preventDefault()
    
    const form = e.target
    const formData = new FormData(form)
    
    // Add featured checkbox value
    const featuredCheckbox = document.getElementById('blogFeatured')
    if (featuredCheckbox && featuredCheckbox.checked) {
        formData.set('featured', '1')
    }
    
    try {
        const userInfo = getUserInfo()
        let url = '/api/blog/manage.php'
        let method = 'POST'

        if (editingBlogPostId) {
            formData.append('id', editingBlogPostId)
            formData.append('_method', 'PUT') // Method override
            method = 'POST' // Use POST for FormData compatibility
            url = `/api/blog/manage.php?id=${editingBlogPostId}` // Add ID to URL as backup
        }
        
        // For PUT requests, we need to handle file uploads differently
        let body = formData
        let headers = {
            'Authorization': `Bearer ${userInfo.token}`
        }

        if (method === 'PUT') {
            // For PUT requests, always use FormData to handle both text and file data
            // The API will handle both $_FILES and form data properly
            body = formData
            // Don't set Content-Type for FormData, let browser set it with boundary
        }
        
        const response = await fetch(url, {
            method: method,
            headers: headers,
            body: body
        })

        const responseText = await response.text()

        let result
        try {
            result = JSON.parse(responseText)
        } catch (parseError) {
            throw new Error('Sunucudan geçersiz yanıt alındı. Lütfen tekrar deneyin.')
        }
        
        if (result.success) {
            showNotification(result.message, 'success')
            clearBlogForm()
            await loadBlogPosts()
            await loadBlogStats()
        } else {
            throw new Error(result.message || 'Blog yazısı kaydedilemedi')
        }
    } catch (error) {
        console.error('Blog submit error:', error)
        showNotification('Hata: ' + error.message, 'error')
    }
}

function handleBlogImagePreview(e) {
    const file = e.target.files[0]
    const preview = document.getElementById('blogImagePreview')
    const previewImg = document.getElementById('blogImagePreviewImg')
    
    if (file) {
        const reader = new FileReader()
        reader.onload = function(e) {
            previewImg.src = e.target.result
            preview.style.display = 'block'
        }
        reader.readAsDataURL(file)
    } else {
        preview.style.display = 'none'
    }
}

function removeBlogImage() {
    const preview = document.getElementById('blogImagePreview')
    const imageInput = document.getElementById('blogFeaturedImage')
    
    preview.style.display = 'none'
    imageInput.value = ''
}

function clearBlogForm() {
    const form = document.getElementById('blogForm')
    if (form) {
        form.reset()
        editingBlogPostId = null
        
        // Clear image preview
        const preview = document.getElementById('blogImagePreview')
        if (preview) {
            preview.style.display = 'none'
        }
        
        // Reset form title
        const submitBtn = form.querySelector('button[type="submit"]')
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Blog Yazısını Kaydet'
        }
    }
}

// CRUD Operations
async function editBlogPost(id) {
    try {
        const userInfo = getUserInfo()
        const response = await fetch(`/api/blog/manage.php?action=single&id=${id}`, {
            headers: {
                'Authorization': `Bearer ${userInfo.token}`,
                'Content-Type': 'application/json'
            }
        })
        
        const data = await response.json()
        
        if (data.success) {
            const post = data.post
            editingBlogPostId = id
            
            // Fill form with post data
            document.getElementById('blogTitle').value = post.title || ''
            document.getElementById('blogSlug').value = post.slug || ''
            document.getElementById('blogExcerpt').value = post.excerpt || ''
            document.getElementById('blogContent').value = post.content || ''
            document.getElementById('blogCategory').value = post.category_id || ''
            document.getElementById('blogAuthor').value = post.author || ''
            document.getElementById('blogTags').value = post.tags_string || ''
            document.getElementById('blogReadingTime').value = post.reading_time || ''
            document.getElementById('blogStatus').value = post.status || 'draft'
            document.getElementById('blogMetaTitle').value = post.meta_title || ''
            document.getElementById('blogMetaDescription').value = post.meta_description || ''
            document.getElementById('blogMetaKeywords').value = post.meta_keywords || ''
            
            // Set featured checkbox
            const featuredCheckbox = document.getElementById('blogFeatured')
            if (featuredCheckbox) {
                featuredCheckbox.checked = post.featured == 1
            }
            
            // Show image preview if exists
            if (post.featured_image) {
                const preview = document.getElementById('blogImagePreview')
                const previewImg = document.getElementById('blogImagePreviewImg')
                
                previewImg.src = post.featured_image
                preview.style.display = 'block'
            }
            
            // Update form title
            const submitBtn = document.querySelector('#blogForm button[type="submit"]')
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Blog Yazısını Güncelle'
            }
            
            // Scroll to form
            document.getElementById('blogSection').scrollIntoView({ behavior: 'smooth' })
            
        } else {
            throw new Error(data.message || 'Blog yazısı yüklenemedi')
        }
    } catch (error) {
        console.error('Edit blog post error:', error)
        showNotification('Blog yazısı düzenlenirken hata oluştu: ' + error.message, 'error')
    }
}

async function deleteBlogPost(id) {
    if (!confirm('Bu blog yazısını silmek istediğinizden emin misiniz?')) {
        return
    }
    
    try {
        const userInfo = getUserInfo()
        const response = await fetch('/api/blog/manage.php', {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${userInfo.token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: id })
        })
        
        const result = await response.json()
        
        if (result.success) {
            showNotification(result.message, 'success')
            await loadBlogPosts()
            await loadBlogStats()
        } else {
            throw new Error(result.message || 'Blog yazısı silinemedi')
        }
    } catch (error) {
        console.error('Delete blog post error:', error)
        showNotification('Blog yazısı silinirken hata oluştu: ' + error.message, 'error')
    }
}

function viewBlogPost(slug) {
    window.open(`/blog/${slug}`, '_blank')
}

// Utility Functions
function getBlogStatusClass(status) {
    switch (status) {
        case 'published':
            return 'bg-success'
        case 'draft':
            return 'bg-warning'
        case 'archived':
            return 'bg-secondary'
        default:
            return 'bg-light text-dark'
    }
}

function getBlogStatusText(status) {
    switch (status) {
        case 'published':
            return 'Yayında'
        case 'draft':
            return 'Taslak'
        case 'archived':
            return 'Arşivlendi'
        default:
            return status
    }
}

function generateSlug(text) {
    // Turkish character replacements
    const turkish = ['ş','Ş','ı','I','İ','ğ','Ğ','ü','Ü','ö','Ö','ç','Ç']
    const english = ['s','s','i','i','i','g','g','u','u','o','o','c','c']
    
    let slug = text
    turkish.forEach((char, index) => {
        slug = slug.replace(new RegExp(char, 'g'), english[index])
    })
    
    // Convert to lowercase and replace spaces and special chars with hyphens
    slug = slug.toLowerCase()
    slug = slug.replace(/[^a-z0-9]+/g, '-')
    slug = slug.replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    
    return slug
}

function formatDate(dateString) {
    if (!dateString) return 'Tarih yok'
    
    const date = new Date(dateString)
    const options = { 
        year: 'numeric', 
        month: '2-digit', 
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Europe/Istanbul'
    }
    return date.toLocaleDateString('tr-TR', options)
}

function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div')
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`
    notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;'
    notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `

    document.body.appendChild(notification)

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove()
        }
    }, 5000)
}
