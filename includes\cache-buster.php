<?php
/**
 * Cache Buster Functions
 * CSS ve JS dosyaları için otomatik versiyonlama
 */

function getCacheBusterVersion($filePath) {
    // Do<PERSON>a varsa son değişiklik tarihini kullan
    if (file_exists($filePath)) {
        return filemtime($filePath);
    }
    
    // Dosya yoksa timestamp kullan
    return time();
}

function getCSSVersion() {
    return getCacheBusterVersion('styles.css');
}

function getJSVersion($jsFile) {
    return getCacheBusterVersion($jsFile);
}

// CSS link tag'i oluştur
function getCSSLink() {
    $version = getCSSVersion();
    return "styles.css?v=" . $version;
}

// JS script tag'i oluştur
function getJSLink($jsFile) {
    $version = getJSVersion($jsFile);
    return $jsFile . "?v=" . $version;
}
?>
