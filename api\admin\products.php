<?php
require_once '../../config/database.php';
require_once '../../includes/functions.php';

setCorsHeaders();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Token kontrolü
    $token = getBearerToken();
    if (!$token || !validateSessionToken($token)) {
        sendErrorResponse('Geçersiz token', 401);
    }
    
    $admin = requireAdmin($db);
    
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            // Tüm ürünleri getir
            $stmt = $db->prepare("SELECT * FROM products ORDER BY created_at DESC");
            $stmt->execute();
            $products = $stmt->fetchAll();
            
            foreach ($products as &$product) {
                $product['_id'] = $product['id'];
                $product['price'] = $product['price'] * 100;
                // --- YENİ EKLENECEK KISIM ---
                // Her ürün için product_images tablosundan resimleri çek
                $imgStmt = $db->prepare("SELECT image_url FROM product_images WHERE product_id = ?");
                $imgStmt->execute([$product['id']]);
                $product['images'] = $imgStmt->fetchAll(PDO::FETCH_COLUMN);
                // Eğer hiç resim yoksa, ana resim olarak products tablosundaki image alanını ekle
                if (empty($product['images']) && !empty($product['image'])) {
                    $product['images'][] = $product['image'];
                }
                // --- YENİ KISIM SONU ---
            }
            
            sendJsonResponse($products);
            break;
            
        case 'POST':
            // Yeni ürün ekle
            $input = json_decode(file_get_contents('php://input'), true);
            $required_fields = ['name', 'description', 'price', 'stock'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field])) {
                    sendErrorResponse("$field alanı gereklidir");
                }
            }
            $name = sanitizeInput($input['name']);
            $description = sanitizeInput($input['description']);
            $price = floatval($input['price']);
            $stock = intval($input['stock']);
            // images dizisinin ilk elemanı ana resim olacak
            $images = $input['images'] ?? [];
            $image = isset($images[0]) ? sanitizeInput($images[0]) : '';
            $category = sanitizeInput($input['category'] ?? '');
            $video_url = sanitizeInput($input['videoUrl'] ?? '');
            $purchase_link = sanitizeInput($input['purchaseLink'] ?? '');
            // $images zaten yukarıda alındı
            $stmt = $db->prepare("
                INSERT INTO products (name, description, price, stock, image, category, video_url, purchase_link) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            if ($stmt->execute([$name, $description, $price, $stock, $image, $category, $video_url, $purchase_link])) {
                $product_id = $db->lastInsertId();
                // Çoklu resim desteği: product_images tablosuna ekle
                if (is_array($images)) {
                    $imgStmt = $db->prepare("INSERT INTO product_images (product_id, image_url) VALUES (?, ?)");
                    foreach ($images as $imgUrl) {
                        $imgStmt->execute([$product_id, $imgUrl]);
                    }
                }
                sendSuccessResponse(['id' => $product_id], 'Ürün başarıyla eklendi');
            } else {
                sendErrorResponse('Ürün eklenirken hata oluştu');
            }
            break;
            
        case 'PUT':
            // Ürün güncelle
            $product_id = $_GET['id'] ?? null;

            // Debug logging for ID
            error_log("PUT Request - Product ID from GET: " . var_export($product_id, true));
            error_log("PUT Request - is_numeric check: " . var_export(is_numeric($product_id), true));

            if (!$product_id || !is_numeric($product_id)) {
                error_log("PUT Request - Invalid product ID: " . var_export($product_id, true));
                sendErrorResponse('Geçersiz ürün ID - ID: ' . $product_id);
            }

            $raw_input = file_get_contents('php://input');
            $input = json_decode($raw_input, true);

            // Debug logging
            error_log("PUT Request Debug:");
            error_log("Product ID: " . $product_id);
            error_log("Raw input: " . $raw_input);
            error_log("Decoded input: " . print_r($input, true));

            if (json_last_error() !== JSON_ERROR_NONE) {
                sendErrorResponse('Geçersiz JSON verisi: ' . json_last_error_msg());
            }

            if (!$input) {
                sendErrorResponse('Boş veri gönderildi');
            }
            // Validate required fields
            $required_fields = ['name', 'description', 'price', 'stock'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field]) || $input[$field] === '') {
                    sendErrorResponse("$field alanı gereklidir");
                }
            }

            $name = sanitizeInput($input['name']);
            $description = sanitizeInput($input['description']);
            $price = floatval($input['price']);
            $stock = intval($input['stock']);
            // images dizisinin ilk elemanı ana resim olacak
            $images = $input['images'] ?? [];
            $image = isset($images[0]) ? sanitizeInput($images[0]) : '';
            $category = sanitizeInput($input['category'] ?? '');
            $video_url = sanitizeInput($input['videoUrl'] ?? '');
            $purchase_link = sanitizeInput($input['purchaseLink'] ?? '');
            // $images zaten yukarıda alındı
            $stmt = $db->prepare("
                UPDATE products 
                SET name=?, description=?, price=?, stock=?, image=?, category=?, video_url=?, purchase_link=?, updated_at=NOW()
                WHERE id=?
            ");
            if ($stmt->execute([$name, $description, $price, $stock, $image, $category, $video_url, $purchase_link, $product_id])) {
                // Çoklu resim desteği: product_images tablosunu güncelle
                if (is_array($images)) {
                    // Önce eski resimleri sil
                    $db->prepare("DELETE FROM product_images WHERE product_id = ?")->execute([$product_id]);
                    // Sonra yeni resimleri ekle
                    $imgStmt = $db->prepare("INSERT INTO product_images (product_id, image_url) VALUES (?, ?)");
                    foreach ($images as $imgUrl) {
                        $imgStmt->execute([$product_id, $imgUrl]);
                    }
                }
                sendSuccessResponse([], 'Ürün başarıyla güncellendi');
            } else {
                sendErrorResponse('Ürün güncellenirken hata oluştu');
            }
            break;
            
        case 'DELETE':
            // Ürün sil
            $product_id = $_GET['id'] ?? null;

            if (!$product_id || !is_numeric($product_id)) {
                sendErrorResponse('Geçersiz ürün ID');
            }
            
            $stmt = $db->prepare("DELETE FROM products WHERE id = ?");
            
            if ($stmt->execute([$product_id])) {
                sendSuccessResponse([], 'Ürün başarıyla silindi');
            } else {
                sendErrorResponse('Ürün silinirken hata oluştu');
            }
            break;
            
        default:
            sendErrorResponse('Desteklenmeyen HTTP metodu', 405);
    }
    
} catch(Exception $e) {
    logError('Admin products error: ' . $e->getMessage());
    sendErrorResponse('Sunucu hatası');
}
?>
